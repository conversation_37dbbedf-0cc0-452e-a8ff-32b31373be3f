/**
 * Utility functions for handling rich text content with React Quill
 * Provides conversion between HTML and plain text, validation, and sanitization
 */

/**
 * Converts HTML content to plain text for backward compatibility
 * @param html - HTML string from React Quill
 * @returns Plain text string
 */
export function htmlToPlainText(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // Remove HTML tags and decode HTML entities
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || '';
}

/**
 * Converts plain text to basic HTML for React Quill
 * @param text - Plain text string
 * @returns HTML string with paragraph tags
 */
export function plainTextToHtml(text: string): string {
  if (!text || typeof text !== 'string') {
    return '<p><br></p>';
  }

  // Split by line breaks and wrap each line in paragraph tags
  const lines = text.split('\n').filter(line => line.trim() !== '');
  if (lines.length === 0) {
    return '<p><br></p>';
  }

  return lines.map(line => `<p>${escapeHtml(line.trim())}</p>`).join('');
}

/**
 * Escapes HTML characters in plain text
 * @param text - Text to escape
 * @returns Escaped text
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Checks if content is HTML (from React Quill) or plain text
 * @param content - Content to check
 * @returns true if content appears to be HTML
 */
export function isHtmlContent(content: string): boolean {
  if (!content || typeof content !== 'string') {
    return false;
  }

  // Check for HTML tags
  const htmlTagRegex = /<[^>]*>/;
  return htmlTagRegex.test(content);
}

/**
 * Sanitizes HTML content to ensure it's safe
 * @param html - HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return '<p><br></p>';
  }

  // Create a temporary div to parse and clean the HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Remove script tags and other potentially dangerous elements
  const dangerousTags = ['script', 'iframe', 'object', 'embed', 'form'];
  dangerousTags.forEach(tag => {
    const elements = tempDiv.getElementsByTagName(tag);
    for (let i = elements.length - 1; i >= 0; i--) {
      elements[i].remove();
    }
  });

  // Remove dangerous attributes
  const allElements = tempDiv.getElementsByTagName('*');
  for (let i = 0; i < allElements.length; i++) {
    const element = allElements[i];
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus'];
    dangerousAttrs.forEach(attr => {
      element.removeAttribute(attr);
    });
  }

  return tempDiv.innerHTML || '<p><br></p>';
}

/**
 * Prepares content for React Quill editor
 * Handles both HTML and plain text input
 * @param content - Content from database (HTML or plain text)
 * @returns HTML content ready for React Quill
 */
export function prepareContentForEditor(content: string): string {
  if (!content || typeof content !== 'string') {
    return '<p><br></p>';
  }

  // If it's already HTML, sanitize and return
  if (isHtmlContent(content)) {
    return sanitizeHtml(content);
  }

  // If it's plain text, convert to HTML
  return plainTextToHtml(content);
}

/**
 * Truncates HTML content for display in lists/cards
 * @param html - HTML content to truncate
 * @param maxLength - Maximum length of plain text
 * @returns Truncated plain text with ellipsis if needed
 */
export function truncateHtmlContent(html: string, maxLength: number = 55): string {
  const plainText = htmlToPlainText(html);
  
  if (plainText.length <= maxLength) {
    return plainText;
  }

  return plainText.slice(0, maxLength) + '...';
}

/**
 * Validates if HTML content is valid for React Quill
 * @param html - HTML content to validate
 * @returns Object with validation result and error message if any
 */
export function validateHtmlContent(html: string): { isValid: boolean; error?: string } {
  try {
    if (!html || typeof html !== 'string') {
      return { isValid: true }; // Empty content is valid
    }

    // Try to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Check if parsing was successful
    if (tempDiv.innerHTML === '') {
      return { isValid: false, error: 'Invalid HTML content' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Failed to parse HTML content' };
  }
}

/**
 * Gets a preview of rich text content for display
 * @param html - HTML content
 * @param maxLength - Maximum length for preview
 * @returns Plain text preview
 */
export function getContentPreview(html: string, maxLength: number = 100): string {
  const plainText = htmlToPlainText(html);
  
  if (!plainText.trim()) {
    return 'No description';
  }

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // Find the last space before maxLength to avoid cutting words
  const truncated = plainText.slice(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) { // Only use last space if it's not too far back
    return truncated.slice(0, lastSpace) + '...';
  }

  return truncated + '...';
}
