import { 
  AutomationWorkflow, 
  AutomationExecution, 
  AutomationContext,
  ConditionConfig,
  ConditionGroup,
  Condition,
  ConditionOperator,
  ActionConfig,
  ActionType,
  TriggerType
} from '../types/automation';
import { 
  automationWorkflowService, 
  automationExecutionService,
  taskService,
  projectService,
  notificationService,
  commentService
} from './supabaseService';
import { getCurrentUser } from '../lib/supabase';

// Condition evaluation engine
export class ConditionEvaluator {
  static evaluateCondition(condition: Condition, context: AutomationContext): boolean {
    const { field, operator, value, valueType, entityType } = condition;
    
    // Get the actual value from the context
    let actualValue = this.getFieldValue(field, entityType, context);
    let expectedValue = value;
    
    // Handle dynamic values
    if (valueType === 'dynamic') {
      expectedValue = this.resolveDynamicValue(value, context);
    } else if (valueType === 'field_reference') {
      expectedValue = this.getFieldValue(value, entityType, context);
    }
    
    return this.compareValues(actualValue, expectedValue, operator);
  }
  
  static evaluateConditionGroup(group: ConditionGroup, context: AutomationContext): boolean {
    const { logic, conditions, groups } = group;
    
    // Evaluate all conditions in this group
    const conditionResults = conditions.map(condition => 
      this.evaluateCondition(condition, context)
    );
    
    // Evaluate all nested groups
    const groupResults = groups.map(nestedGroup => 
      this.evaluateConditionGroup(nestedGroup, context)
    );
    
    // Combine all results
    const allResults = [...conditionResults, ...groupResults];
    
    if (allResults.length === 0) return true;
    
    // Apply logic operator
    if (logic === 'AND') {
      return allResults.every(result => result);
    } else {
      return allResults.some(result => result);
    }
  }
  
  static evaluateConditionConfig(config: ConditionConfig, context: AutomationContext): boolean {
    if (!config || !config.rootGroup) return true;
    return this.evaluateConditionGroup(config.rootGroup, context);
  }
  
  private static getFieldValue(field: string, entityType: string | undefined, context: AutomationContext): any {
    const entity = entityType ? context.entities[entityType as keyof typeof context.entities] : null;
    
    if (entity && entity[field] !== undefined) {
      return entity[field];
    }
    
    // Try to get from trigger data
    if (context.triggerData[field] !== undefined) {
      return context.triggerData[field];
    }
    
    // Special system fields
    switch (field) {
      case 'current_user_id':
        return context.currentUser.id;
      case 'current_user_email':
        return context.currentUser.email;
      case 'current_user_role':
        return context.currentUser.role;
      case 'current_timestamp':
        return context.timestamp;
      default:
        return null;
    }
  }
  
  private static resolveDynamicValue(value: string, context: AutomationContext): any {
    // Handle template strings like {{task.title}} or {{current_user.name}}
    return value.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const parts = path.split('.');
      let current: any = context;
      
      for (const part of parts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part];
        } else {
          return match; // Return original if path not found
        }
      }
      
      return current;
    });
  }
  
  private static compareValues(actual: any, expected: any, operator: ConditionOperator): boolean {
    switch (operator) {
      case 'equals':
        return actual === expected;
      case 'not_equals':
        return actual !== expected;
      case 'contains':
        return String(actual).toLowerCase().includes(String(expected).toLowerCase());
      case 'not_contains':
        return !String(actual).toLowerCase().includes(String(expected).toLowerCase());
      case 'greater_than':
        return Number(actual) > Number(expected);
      case 'less_than':
        return Number(actual) < Number(expected);
      case 'greater_than_or_equal':
        return Number(actual) >= Number(expected);
      case 'less_than_or_equal':
        return Number(actual) <= Number(expected);
      case 'is_empty':
        return !actual || actual === '' || (Array.isArray(actual) && actual.length === 0);
      case 'is_not_empty':
        return !!actual && actual !== '' && (!Array.isArray(actual) || actual.length > 0);
      case 'in':
        return Array.isArray(expected) && expected.includes(actual);
      case 'not_in':
        return !Array.isArray(expected) || !expected.includes(actual);
      default:
        return false;
    }
  }
}

// Action execution engine
export class ActionExecutor {
  static async executeAction(action: ActionConfig, context: AutomationContext): Promise<{ success: boolean; result?: any; error?: string }> {
    try {
      switch (action.type) {
        case 'update_task_status':
          return await this.updateTaskStatus(action, context);
        case 'assign_task':
          return await this.assignTask(action, context);
        case 'reassign_task':
          return await this.reassignTask(action, context);
        case 'update_task_field':
          return await this.updateTaskField(action, context);
        case 'add_comment':
          return await this.addComment(action, context);
        case 'send_notification':
          return await this.sendNotification(action, context);
        case 'create_task':
          return await this.createTask(action, context);
        case 'create_subtask':
          return await this.createSubtask(action, context);
        case 'update_due_date':
          return await this.updateDueDate(action, context);
        case 'add_tag':
          return await this.addTag(action, context);
        case 'remove_tag':
          return await this.removeTag(action, context);
        case 'update_priority':
          return await this.updatePriority(action, context);
        default:
          return { success: false, error: `Unknown action type: ${action.type}` };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }
  
  private static async updateTaskStatus(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };

    const newStatus = this.resolveValue(action.value, context);
    console.log(`🤖 Automation updating task ${task.id} status: ${task.status} → ${newStatus}`);

    // Update the task status
    await taskService.updateTask(task.id, { status: newStatus });

    console.log(`✅ Automation successfully updated task ${task.id} to status: ${newStatus}`);

    return { success: true, result: { oldStatus: task.status, newStatus } };
  }
  
  private static async assignTask(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const { assignmentConfig } = action;
    if (!assignmentConfig) return { success: false, error: 'No assignment configuration' };
    
    const updates: any = {};
    
    if (assignmentConfig.userId) {
      const userId = this.resolveValue(assignmentConfig.userId, context);
      if (assignmentConfig.assignmentType === 'replace') {
        updates.assignedUsers = [userId];
      } else if (assignmentConfig.assignmentType === 'add') {
        updates.assignedUsers = [...(task.assignedUsers || []), userId];
      }
    }
    
    if (assignmentConfig.groupId) {
      const groupId = this.resolveValue(assignmentConfig.groupId, context);
      if (assignmentConfig.assignmentType === 'replace') {
        updates.assignedGroups = [groupId];
      } else if (assignmentConfig.assignmentType === 'add') {
        updates.assignedGroups = [...(task.assignedGroups || []), groupId];
      }
    }
    
    await taskService.updateTask(task.id, updates);
    return { success: true, result: updates };
  }
  
  private static async reassignTask(action: ActionConfig, context: AutomationContext) {
    // Similar to assignTask but with replace logic
    return this.assignTask({ ...action, assignmentConfig: { ...action.assignmentConfig, assignmentType: 'replace' } }, context);
  }
  
  private static async updateTaskField(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const fieldName = action.targetField;
    const newValue = this.resolveValue(action.value, context);
    
    if (!fieldName) return { success: false, error: 'No target field specified' };
    
    const updates = { [fieldName]: newValue };
    await taskService.updateTask(task.id, updates);
    
    return { success: true, result: { field: fieldName, oldValue: task[fieldName as keyof typeof task], newValue } };
  }
  
  private static async addComment(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const content = this.resolveValue(action.value, context);
    const comment = await commentService.addComment({
      task_id: task.id,
      user_id: context.currentUser.id,
      content,
    });
    
    return { success: true, result: comment };
  }
  
  private static async sendNotification(action: ActionConfig, context: AutomationContext) {
    const { templateConfig } = action;
    if (!templateConfig) return { success: false, error: 'No template configuration' };
    
    const title = this.resolveValue(templateConfig.subject || 'Automation Notification', context);
    const content = this.resolveValue(templateConfig.body || '', context);
    const recipients = templateConfig.recipients || [];
    
    const notifications = [];
    for (const recipientId of recipients) {
      const resolvedRecipientId = this.resolveValue(recipientId, context);
      const notification = await notificationService.createNotification({
        recipient_id: resolvedRecipientId,
        sender_id: context.currentUser.id,
        type: 'automation',
        title,
        content,
        task_id: context.entities.task?.id,
      });
      notifications.push(notification);
    }
    
    return { success: true, result: notifications };
  }
  
  private static async createTask(action: ActionConfig, context: AutomationContext) {
    const { taskCreationConfig } = action;
    if (!taskCreationConfig) return { success: false, error: 'No task creation configuration' };
    
    const taskData = {
      title: this.resolveValue(taskCreationConfig.title, context),
      description: this.resolveValue(taskCreationConfig.description || '', context),
      priority: taskCreationConfig.priority || 'medium',
      projectId: taskCreationConfig.projectId,
      folderId: taskCreationConfig.folderId,
      assignedUsers: taskCreationConfig.assignedUsers || [],
      assignedGroups: taskCreationConfig.assignedGroups || [],
      dueDate: taskCreationConfig.dueDate,
      status: 'todo',
      tags: [],
      comments: [],
      history: [],
      durations: [],
      subtasks: [],
    };
    
    const newTask = await taskService.createTask(taskData);
    return { success: true, result: newTask };
  }
  
  private static async createSubtask(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const { taskCreationConfig } = action;
    if (!taskCreationConfig) return { success: false, error: 'No subtask creation configuration' };
    
    const subtaskData = {
      title: this.resolveValue(taskCreationConfig.title, context),
      description: this.resolveValue(taskCreationConfig.description || '', context),
      status: 'todo',
      priority: taskCreationConfig.priority || 'medium',
      assignedUsers: taskCreationConfig.assignedUsers || [],
      dueDate: taskCreationConfig.dueDate,
      comments: [],
      history: [],
      durations: [],
    };
    
    // This would need to be implemented in the task service
    // For now, return a placeholder
    return { success: true, result: subtaskData };
  }
  
  private static async updateDueDate(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const newDueDate = this.resolveValue(action.value, context);
    await taskService.updateTask(task.id, { dueDate: newDueDate });
    
    return { success: true, result: { oldDueDate: task.dueDate, newDueDate } };
  }
  
  private static async addTag(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const newTag = this.resolveValue(action.value, context);
    const updatedTags = [...(task.tags || []), newTag];
    await taskService.updateTask(task.id, { tags: updatedTags });
    
    return { success: true, result: { addedTag: newTag, tags: updatedTags } };
  }
  
  private static async removeTag(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const tagToRemove = this.resolveValue(action.value, context);
    const updatedTags = (task.tags || []).filter(tag => tag !== tagToRemove);
    await taskService.updateTask(task.id, { tags: updatedTags });
    
    return { success: true, result: { removedTag: tagToRemove, tags: updatedTags } };
  }
  
  private static async updatePriority(action: ActionConfig, context: AutomationContext) {
    const task = context.entities.task;
    if (!task) return { success: false, error: 'No task in context' };
    
    const newPriority = this.resolveValue(action.value, context);
    await taskService.updateTask(task.id, { priority: newPriority });
    
    return { success: true, result: { oldPriority: task.priority, newPriority } };
  }
  
  private static resolveValue(value: any, context: AutomationContext): any {
    if (typeof value === 'string' && value.includes('{{')) {
      // Handle template strings
      return value.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
        const parts = path.split('.');
        let current: any = context;

        for (const part of parts) {
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            return match; // Return original if path not found
          }
        }

        return current;
      });
    }

    return value;
  }
}

// Main automation engine
export class AutomationEngine {
  private static instance: AutomationEngine;
  private isInitialized = false;

  static getInstance(): AutomationEngine {
    if (!AutomationEngine.instance) {
      AutomationEngine.instance = new AutomationEngine();
    }
    return AutomationEngine.instance;
  }

  async initialize() {
    if (this.isInitialized) return;

    console.log('🤖 Initializing Automation Engine...');
    this.isInitialized = true;
  }

  async processEvent(eventType: TriggerType, eventData: Record<string, any>) {
    try {
      console.log(`🔄 Processing automation event: ${eventType}`, eventData);

      // Get all active workflows that match this trigger
      const workflows = await automationWorkflowService.getActiveWorkflowsByTrigger(eventType);

      if (workflows.length === 0) {
        console.log(`No active workflows found for trigger: ${eventType}`);
        return;
      }

      console.log(`Found ${workflows.length} workflows to process for trigger: ${eventType}`);

      // Process each workflow
      for (const workflow of workflows) {
        await this.executeWorkflow(workflow, eventData);
      }
    } catch (error) {
      console.error('Error processing automation event:', error);
    }
  }

  async executeWorkflow(workflow: AutomationWorkflow, triggerData: Record<string, any>): Promise<AutomationExecution> {
    const startTime = Date.now();
    let execution: AutomationExecution | null = null;

    console.log(`🚀 Executing workflow: ${workflow.name} (${workflow.id})`);

    try {
      // Create execution context
      const context = await this.createExecutionContext(workflow, triggerData);

      // Create initial execution record
      execution = await automationExecutionService.createExecution({
        workflowId: workflow.id,
        triggerData,
        actionResults: [],
        status: 'pending',
      });

      if (!execution) {
        throw new Error('Failed to create execution record');
      }

      // Evaluate conditions if they exist
      let conditionResult;
      if (workflow.conditionConfig) {
        console.log(`🔍 Evaluating conditions for workflow: ${workflow.name}`, {
          conditionConfig: workflow.conditionConfig,
          contextTask: context.task
        });

        const conditionPassed = ConditionEvaluator.evaluateConditionConfig(workflow.conditionConfig, context);
        conditionResult = {
          passed: conditionPassed,
          evaluationDetails: { /* Add detailed evaluation info if needed */ }
        };

        console.log(`🎯 Condition evaluation result: ${conditionPassed ? 'PASSED' : 'FAILED'}`);

        if (!conditionPassed) {
          console.log(`⏭️ Workflow conditions not met, skipping: ${workflow.name}`);
          execution = await automationExecutionService.updateExecution(execution.id, {
            status: 'skipped',
            conditionResult,
            executionTimeMs: Date.now() - startTime,
          });
          return execution!;
        }
      }

      // Execute actions
      const actionResults = [];
      for (const action of workflow.actionConfig) {
        console.log(`⚡ Executing action: ${action.type}`);
        const result = await ActionExecutor.executeAction(action, context);
        actionResults.push({
          actionType: action.type,
          success: result.success,
          result: result.result,
          error: result.error,
        });

        if (!result.success) {
          console.error(`❌ Action failed: ${action.type}`, result.error);
        } else {
          console.log(`✅ Action completed: ${action.type}`);
        }
      }

      // Determine overall status
      const hasFailures = actionResults.some(result => !result.success);
      const status = hasFailures ? 'failed' : 'success';

      // Update execution record
      execution = await automationExecutionService.updateExecution(execution.id, {
        status,
        conditionResult,
        actionResults,
        executionTimeMs: Date.now() - startTime,
      });

      // Update workflow execution count
      await automationWorkflowService.incrementExecutionCount(workflow.id);

      console.log(`🏁 Workflow execution completed: ${workflow.name} - Status: ${status}`);

      return execution!;
    } catch (error) {
      console.error(`💥 Workflow execution failed: ${workflow.name}`, error);

      // Try to update execution with error if we have an execution record
      try {
        if (execution) {
          const updatedExecution = await automationExecutionService.updateExecution(execution.id, {
            status: 'failed',
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            executionTimeMs: Date.now() - startTime,
          });
          return updatedExecution!;
        }
      } catch (updateError) {
        console.error('Failed to update execution with error:', updateError);
      }

      // If we can't update, create a minimal execution record
      const failedExecution = await automationExecutionService.createExecution({
        workflowId: workflow.id,
        triggerData,
        actionResults: [],
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        executionTimeMs: Date.now() - startTime,
      });

      return failedExecution!;
    }
  }

  private async createExecutionContext(workflow: AutomationWorkflow, triggerData: Record<string, any>): Promise<AutomationContext> {
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw new Error('No authenticated user found');
    }

    const context: AutomationContext = {
      triggerData,
      currentUser: {
        id: currentUser.id,
        name: currentUser.user_metadata?.name || currentUser.email || '',
        email: currentUser.email || '',
        role: currentUser.user_metadata?.role || 'user',
      },
      entities: {},
      timestamp: new Date().toISOString(),
    };

    // Load relevant entities based on trigger data
    if (triggerData.task) {
      // Use the task data directly from trigger data
      context.entities.task = triggerData.task;
    } else if (triggerData.taskId) {
      try {
        const tasks = await taskService.getTasks();
        context.entities.task = tasks.find(t => t.id === triggerData.taskId);
      } catch (error) {
        console.warn('Failed to load task for context:', error);
      }
    }

    if (triggerData.projectId) {
      try {
        const projects = await projectService.getProjects();
        context.entities.project = projects.find(p => p.id === triggerData.projectId);
      } catch (error) {
        console.warn('Failed to load project for context:', error);
      }
    }

    if (triggerData.userId) {
      // Load user data if needed
      context.entities.user = { id: triggerData.userId };
    }

    if (triggerData.commentId) {
      // Load comment data if needed
      context.entities.comment = { id: triggerData.commentId };
    }

    console.log('🔧 Created execution context:', {
      hasTask: !!context.entities.task,
      taskId: context.entities.task?.id,
      taskTitle: context.entities.task?.title,
      taskStatus: context.entities.task?.status
    });

    return context;
  }

  // Trigger detection methods
  async onTaskCreated(task: any) {
    await this.processEvent('task_created', {
      taskId: task.id,
      projectId: task.projectId,
      folderId: task.folderId,
      task,
    });
  }

  async onTaskUpdated(oldTask: any, newTask: any) {
    console.log('🤖 AutomationEngine.onTaskUpdated called', {
      taskId: newTask.id,
      taskTitle: newTask.title,
      oldStatus: oldTask?.status,
      newStatus: newTask?.status,
      hasStatusChange: oldTask?.status !== newTask?.status,
      automationTriggered: newTask.automationTriggered
    });

    // Prevent infinite loops by checking if this update was triggered by automation
    if (newTask.automationTriggered) {
      console.log('🔄 Skipping automation for automation-triggered update');
      return;
    }

    console.log('🔄 Task update detected, processing automation events...');

    await this.processEvent('task_updated', {
      taskId: newTask.id,
      projectId: newTask.projectId,
      folderId: newTask.folderId,
      oldTask,
      newTask,
      changes: this.getTaskChanges(oldTask, newTask),
    });

    // Check for specific field changes
    if (oldTask.status !== newTask.status) {
      await this.processEvent('task_status_changed', {
        taskId: newTask.id,
        projectId: newTask.projectId,
        oldStatus: oldTask.status,
        newStatus: newTask.status,
        task: newTask,
      });
    }

    if (this.hasAssignmentChanged(oldTask, newTask)) {
      await this.processEvent('task_assigned', {
        taskId: newTask.id,
        projectId: newTask.projectId,
        oldAssignedUsers: oldTask.assignedUsers || [],
        newAssignedUsers: newTask.assignedUsers || [],
        oldAssignedGroups: oldTask.assignedGroups || [],
        newAssignedGroups: newTask.assignedGroups || [],
        task: newTask,
      });
    }
  }

  async onCommentAdded(comment: any, task: any) {
    await this.processEvent('comment_added', {
      commentId: comment.id,
      taskId: task.id,
      projectId: task.projectId,
      userId: comment.userId,
      comment,
      task,
    });
  }

  async onProjectCreated(project: any) {
    await this.processEvent('project_created', {
      projectId: project.id,
      folderId: project.folderId,
      project,
    });
  }

  async onProjectUpdated(oldProject: any, newProject: any) {
    await this.processEvent('project_updated', {
      projectId: newProject.id,
      folderId: newProject.folderId,
      oldProject,
      newProject,
      changes: this.getProjectChanges(oldProject, newProject),
    });
  }

  async onCustomEvent(eventName: string, eventData: Record<string, any>) {
    await this.processEvent('custom_event', {
      eventName,
      ...eventData,
    });
  }

  // Helper methods
  private getTaskChanges(oldTask: any, newTask: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    const fields = ['title', 'description', 'status', 'priority', 'assignedUsers', 'assignedGroups', 'dueDate', 'tags'];

    for (const field of fields) {
      if (JSON.stringify(oldTask[field]) !== JSON.stringify(newTask[field])) {
        changes[field] = { old: oldTask[field], new: newTask[field] };
      }
    }

    return changes;
  }

  private getProjectChanges(oldProject: any, newProject: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    const fields = ['name', 'description', 'color', 'startDate', 'endDate'];

    for (const field of fields) {
      if (oldProject[field] !== newProject[field]) {
        changes[field] = { old: oldProject[field], new: newProject[field] };
      }
    }

    return changes;
  }

  private hasAssignmentChanged(oldTask: any, newTask: any): boolean {
    const oldUsers = JSON.stringify((oldTask.assignedUsers || []).sort());
    const newUsers = JSON.stringify((newTask.assignedUsers || []).sort());
    const oldGroups = JSON.stringify((oldTask.assignedGroups || []).sort());
    const newGroups = JSON.stringify((newTask.assignedGroups || []).sort());

    return oldUsers !== newUsers || oldGroups !== newGroups;
  }
}

// Export singleton instance
export const automationEngine = AutomationEngine.getInstance();
