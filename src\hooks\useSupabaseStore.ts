import { useEffect } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { useAuth } from './useAuth';


export const useInitializeSupabaseStore = () => {
  const { user, initialized: authInitialized } = useAuth();
  const { initialize, initialized: storeInitialized, cleanup } = useSupabaseStore();

  useEffect(() => {
    let isEffectActive = true;

    // Only initialize once when user is authenticated and store is not initialized
    if (authInitialized && user && !storeInitialized && isEffectActive) {
      console.log('Initializing Supabase store for user:', user.email);
      initialize().catch(error => {
        console.error('Failed to initialize store:', error);
      });
    }

    // Complete cleanup when user logs out
    if (authInitialized && !user && storeInitialized) {
      console.log('User logged out, cleaning up store');
      cleanup();
    }

    return () => {
      isEffectActive = false;
    };
  }, [authInitialized, user, storeInitialized, initialize, cleanup]);

  useEffect(() => {
    // Complete cleanup on unmount
    return () => {
      if (storeInitialized) {
        console.log('Component unmounting, cleaning up store');
        cleanup();
      }
    };
  }, [cleanup, storeInitialized]);

  return { initialized: storeInitialized };
};
