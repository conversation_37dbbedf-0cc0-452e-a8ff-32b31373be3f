import React, { useState } from 'react';
import { Subtask, Task } from '../types';
import { Plus, Trash2, Check, Edit2, Calendar, User } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import SubtaskForm from './SubtaskForm';


interface SubtaskListProps {
  taskId: string;
  subtasks: Subtask[];
  parentTaskStatus: Task['status'];
  onSubtaskUpdate: () => void;
  onSubtaskFormStateChange?: (isOpen: boolean) => void;
}

export default function SubtaskList({ taskId, subtasks, parentTaskStatus, onSubtaskUpdate, onSubtaskFormStateChange }: SubtaskListProps) {
  const { users, addSubtask, updateSubtask, deleteSubtask } = useSupabaseStore();
  const [showForm, setShowForm] = useState(false);
  const [editingSubtask, setEditingSubtask] = useState<Subtask | null>(null);

  const handleAddSubtask = () => {
    if (subtasks.length < 20) {
      setEditingSubtask(null);
      setShowForm(true);
      onSubtaskFormStateChange?.(true);
    }
  };

  const handleEditSubtask = (subtask: Subtask) => {
    setEditingSubtask(subtask);
    setShowForm(true);
    onSubtaskFormStateChange?.(true);
  };

  const handleSubmit = async (subtaskData: Omit<Subtask, 'id'>) => {
    try {
      if (editingSubtask) {
        await updateSubtask(taskId, editingSubtask.id, subtaskData);
      } else {
        await addSubtask(taskId, subtaskData);
      }
      setShowForm(false);
      setEditingSubtask(null);
      onSubtaskFormStateChange?.(false);
      onSubtaskUpdate(); // Trigger parent update
    } catch (error) {
      console.error('Failed to save subtask:', error);
      alert('Failed to save subtask. Please try again.');
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-700">📌 Subtasks ({subtasks.length}/20)</h3>
        <button
          onClick={handleAddSubtask}
          disabled={subtasks.length >= 20}
          className="px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1 transition-colors"
        >
          <Plus className="w-3 h-3" />
          Add
        </button>
      </div>

      <div className="space-y-1">
        {subtasks.map((subtask) => {
          const assignedUser = users.find(u => u.id === subtask.assignedUserId);
          const hasStartDate = subtask.startDate;
          const hasDueDate = subtask.dueDate;

          return (
          <div
            key={subtask.id}
            className="flex items-center gap-2 px-2 py-1.5 hover:bg-gray-50 rounded group border border-gray-100 transition-colors"
          >
            {/* Completion Checkbox */}
            <button
              onClick={() => {
                updateSubtask(taskId, subtask.id, { completed: !subtask.completed });
                onSubtaskUpdate();
              }}
              className={`w-4 h-4 rounded border flex items-center justify-center flex-shrink-0 transition-colors ${
                subtask.completed ? 'bg-blue-600 border-blue-600' : 'border-gray-300 hover:border-blue-400'
              }`}
            >
              {subtask.completed && <Check className="w-2.5 h-2.5 text-white" />}
            </button>

            {/* Single Line Content */}
            <div className="flex-1 min-w-0 flex items-center justify-between gap-2">
              {/* Left side: Title and Info */}
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <h4 className={`text-sm font-medium truncate ${subtask.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                  {subtask.title}
                </h4>

                {/* Assignee */}
                {assignedUser && (
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <User className="w-3 h-3" />
                    <span className="truncate max-w-16">{assignedUser.name || assignedUser.email.split('@')[0]}</span>
                  </div>
                )}

                {/* Dates */}
                {(hasStartDate || hasDueDate) && (
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Calendar className="w-3 h-3" />
                    <span className="whitespace-nowrap">
                      {hasStartDate && hasDueDate ? (
                        `${new Date(subtask.startDate!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(subtask.dueDate!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
                      ) : hasStartDate ? (
                        new Date(subtask.startDate!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                      ) : hasDueDate ? (
                        new Date(subtask.dueDate!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                      ) : null}
                    </span>
                  </div>
                )}

                {/* Priority indicator */}
                {subtask.priority && subtask.priority !== 'medium' && (
                  <span className={`px-1.5 py-0.5 rounded text-xs font-medium whitespace-nowrap ${
                    subtask.priority === 'urgent' ? 'bg-red-100 text-red-700' :
                    subtask.priority === 'high' ? 'bg-orange-100 text-orange-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {subtask.priority}
                  </span>
                )}
              </div>

              {/* Right side: Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                <button
                  onClick={() => handleEditSubtask(subtask)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                  title="Edit subtask"
                >
                  <Edit2 className="w-3 h-3 text-gray-500" />
                </button>
                <button
                  onClick={async () => {
                    try {
                      await deleteSubtask(taskId, subtask.id);
                      onSubtaskUpdate();
                    } catch (error) {
                      console.error('Failed to delete subtask:', error);
                      alert('Failed to delete subtask. Please try again.');
                    }
                  }}
                  className="p-1 hover:bg-red-50 rounded text-red-500 transition-colors"
                  title="Delete subtask"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
          );
        })}
      </div>

      {showForm && (
        <SubtaskForm
          onSubmit={handleSubmit}
          onClose={() => {
            setShowForm(false);
            setEditingSubtask(null);
            onSubtaskFormStateChange?.(false);
          }}
          initialData={editingSubtask}
          taskId={taskId}
          parentTaskStatus={parentTaskStatus}
        />
      )}

      {subtasks.length >= 20 && (
        <p className="text-sm text-yellow-600">
          Maximum number of subtasks (20) reached
        </p>
      )}
    </div>
  );
}