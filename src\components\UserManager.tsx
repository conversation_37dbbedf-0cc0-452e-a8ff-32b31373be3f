import React, { useState } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { Plus, Trash2, Edit2, Users, Clock } from 'lucide-react';
import SkillsetSelector from './SkillsetSelector';
import UserCapacityManager from './UserCapacityManager';

export default function UserManager() {
  const { users, userGroups, skillsetGroups, addUser, updateUser, deleteUser } = useSupabaseStore();
  const [showForm, setShowForm] = useState(false);
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [showCapacityManager, setShowCapacityManager] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    groupId: '',
    avatar: '',
    skillsetIds: [] as string[],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingUser) {
      updateUser(editingUser, formData);
      setEditingUser(null);
    } else {
      addUser(formData);
    }
    setFormData({ name: '', email: '', groupId: '', avatar: '', skillsetIds: [] });
    setShowForm(false);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white flex items-center gap-2">
          <Users className="w-6 h-6" />
          Users
        </h2>
        <button
          onClick={() => setShowForm(true)}
          className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add User
        </button>
      </div>

      <div className="grid gap-4">
        {users.map((user) => {
          const group = userGroups.find(g => g.id === user.groupId);
          const userSkillsets = skillsetGroups.filter(s => user.skillsetIds?.includes(s.id));
          return (
            <div
              key={user.id}
              className="bg-gray-800 p-4 rounded-lg flex items-center justify-between group"
            >
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-lg font-semibold text-white">
                  {user.name.charAt(0)}
                </div>
                <div>
                  <h3 className="text-white font-medium">{user.name}</h3>
                  <p className="text-gray-400 text-sm">{user.email}</p>
                  {group && (
                    <span className={`text-xs px-2 py-1 rounded-full ${group.color} bg-opacity-20 mt-1 inline-block mr-2`}>
                      {group.name}
                    </span>
                  )}
                  {userSkillsets.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {userSkillsets.map((skillset) => (
                        <span
                          key={skillset.id}
                          className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-gray-700 text-gray-300"
                        >
                          <div className={`w-2 h-2 rounded-full ${skillset.color}`} />
                          {skillset.name}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => setShowCapacityManager(user.id)}
                  className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-blue-400"
                  title="Manage Capacity"
                >
                  <Clock className="w-4 h-4" />
                </button>
                <button
                  onClick={() => {
                    setEditingUser(user.id);
                    setFormData({
                      name: user.name,
                      email: user.email,
                      groupId: user.groupId,
                      avatar: user.avatar || '',
                      skillsetIds: user.skillsetIds || [],
                    });
                    setShowForm(true);
                  }}
                  className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-white"
                >
                  <Edit2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => deleteUser(user.id)}
                  className="p-1 hover:bg-gray-700 rounded text-red-400 hover:text-red-300"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-96">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {editingUser ? 'Edit User' : 'Add New User'}
            </h3>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm text-gray-300 mb-1">Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm text-gray-300 mb-1">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm text-gray-300 mb-1">User Group</label>
                <select
                  value={formData.groupId}
                  onChange={(e) => setFormData({ ...formData, groupId: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  required
                >
                  <option value="">Select a group</option>
                  {userGroups.map((group) => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm text-gray-300 mb-1">Avatar URL (optional)</label>
                <input
                  type="url"
                  value={formData.avatar}
                  onChange={(e) => setFormData({ ...formData, avatar: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  placeholder="https://example.com/avatar.jpg"
                />
              </div>

              <div>
                <SkillsetSelector
                  selectedSkillsetIds={formData.skillsetIds}
                  onSelectionChange={(skillsetIds) => setFormData({ ...formData, skillsetIds })}
                  label="Skillsets"
                  placeholder="Select user skillsets..."
                />
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingUser(null);
                    setFormData({ name: '', email: '', groupId: '', avatar: '', skillsetIds: [] });
                  }}
                  className="px-4 py-2 text-gray-300 hover:bg-gray-700 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {editingUser ? 'Update' : 'Add'} User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Capacity Manager Modal */}
      {showCapacityManager && (
        <UserCapacityManager
          userId={showCapacityManager}
          onClose={() => setShowCapacityManager(null)}
        />
      )}
    </div>
  );
}