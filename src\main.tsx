// import { StrictMode } from 'react'; // Temporarily disabled to prevent double initialization
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';

// Clear browser cache on load in development
if (import.meta.env.DEV) {
  // Clear localStorage and sessionStorage
  localStorage.clear();
  sessionStorage.clear();

  // Unregister any service workers
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister();
      }
    });
  }

  // Clear all caches
  if ('caches' in window) {
    caches.keys().then(function(names) {
      for (let name of names) {
        caches.delete(name);
      }
    });
  }

  // Add cache-busting timestamp to prevent caching
  const timestamp = Date.now();
  if (!window.location.search.includes('t=')) {
    const separator = window.location.search ? '&' : '?';
    window.history.replaceState({}, '', `${window.location.pathname}${window.location.search}${separator}t=${timestamp}`);
  }
}

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Root element not found');

createRoot(rootElement).render(
  // Temporarily disable StrictMode to prevent double initialization issues
  // <StrictMode>
    <App />
  // </StrictMode>
);