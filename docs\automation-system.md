# Automation System Documentation

## Overview

The Automation System provides a comprehensive visual workflow automation platform that enables users of all technical backgrounds to automate business and project workflows using an intuitive, drag-and-drop interface. The system supports trigger-based automation with conditional logic and multiple action support.

## Features

### Core Capabilities
- **Visual Workflow Builder**: Drag-and-drop interface for creating automation workflows
- **Trigger System**: Support for various trigger types including task events, schedules, and webhooks
- **Conditional Logic**: Advanced condition evaluation with AND/OR logic and nested conditions
- **Action Execution**: Multiple action types for task management, notifications, and data updates
- **Workflow Management**: CRUD operations, sharing, testing, and monitoring
- **Template System**: Predefined and custom workflow templates
- **Execution Monitoring**: Comprehensive logging and analytics

### Supported Triggers
- **Task Events**: Created, updated, status changed, assigned, due date reached
- **Project Events**: Created, updated
- **Comment Events**: Comment added
- **Scheduled Events**: Daily, weekly, monthly schedules
- **Webhook Events**: External system integrations
- **Custom Events**: Application-specific events

### Supported Actions
- **Task Management**: Update status, assign/reassign, update fields, set due dates
- **Content Actions**: Add comments, add/remove tags
- **Task Creation**: Create new tasks or subtasks
- **Notifications**: Send in-app notifications or emails
- **Field Updates**: Update any task or project field

## Architecture

### Database Schema

The automation system uses four main tables:

#### automation_workflows
Stores workflow definitions including trigger, condition, and action configurations.

```sql
CREATE TABLE automation_workflows (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  is_template BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  project_id UUID REFERENCES projects(id),
  folder_id UUID REFERENCES folders(id),
  trigger_config JSONB NOT NULL,
  condition_config JSONB,
  action_config JSONB NOT NULL,
  execution_count INTEGER DEFAULT 0,
  last_executed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### automation_executions
Logs all workflow executions with detailed results and performance metrics.

#### automation_triggers
Manages custom triggers like webhooks and scheduled events.

### Core Components

#### AutomationEngine
The main orchestrator that processes events and executes workflows.

```typescript
class AutomationEngine {
  async processEvent(eventType: TriggerType, eventData: Record<string, any>)
  async executeWorkflow(workflow: AutomationWorkflow, triggerData: Record<string, any>)
}
```

#### ConditionEvaluator
Evaluates workflow conditions using a flexible rule engine.

```typescript
class ConditionEvaluator {
  static evaluateCondition(condition: Condition, context: AutomationContext): boolean
  static evaluateConditionGroup(group: ConditionGroup, context: AutomationContext): boolean
}
```

#### ActionExecutor
Executes workflow actions with proper error handling and result tracking.

```typescript
class ActionExecutor {
  static async executeAction(action: ActionConfig, context: AutomationContext)
}
```

## Usage Guide

### Creating a Workflow

1. **Access Automation**: Navigate to the Automation section in the sidebar
2. **Create Workflow**: Click "Create Workflow" or "From Template"
3. **Configure Trigger**: Select when the workflow should run
4. **Add Conditions**: Define optional conditions that must be met
5. **Configure Actions**: Specify what actions to perform
6. **Test & Save**: Test the workflow and save when ready

### Workflow Builder Interface

The visual workflow builder provides:
- **Toolbox**: Drag components (triggers, conditions, actions) onto the canvas
- **Canvas**: Visual representation of the workflow
- **Properties Panel**: Configure workflow settings and view current configuration
- **Configuration Modals**: Detailed setup for triggers, conditions, and actions

### Template System

#### Using Templates
- Browse predefined templates for common automation scenarios
- Import/export templates for sharing across projects
- Create custom templates from existing workflows

#### Predefined Templates
- **Auto-assign High Priority Tasks**: Automatically assign high priority tasks to team leads
- **Status Change Notifications**: Notify assignees when task status changes
- **Overdue Task Reminder**: Send daily reminders for overdue tasks
- **Auto-create Follow-up Tasks**: Create follow-up tasks when tasks are completed
- **Project Completion Notification**: Notify team when all project tasks are completed

### Condition Building

Conditions support:
- **Field Comparisons**: Compare task/project fields with static or dynamic values
- **Logical Operators**: AND/OR logic with nested condition groups
- **Dynamic Values**: Use template variables like `{{task.title}}` or `{{currentUser.id}}`
- **Multiple Entity Types**: Filter on tasks, projects, users, or comments

### Action Configuration

Actions can:
- **Update Data**: Modify task status, priority, assignments, due dates
- **Create Content**: Add comments, create new tasks or subtasks
- **Send Notifications**: In-app notifications or email alerts
- **Use Templates**: Dynamic content with variable substitution

## API Reference

### Automation Services

#### automationWorkflowService
```typescript
// Get workflows
getWorkflows(projectId?: string, folderId?: string): Promise<AutomationWorkflow[]>

// CRUD operations
createWorkflow(workflow: Omit<AutomationWorkflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<AutomationWorkflow>
updateWorkflow(id: string, updates: Partial<AutomationWorkflow>): Promise<AutomationWorkflow>
deleteWorkflow(id: string): Promise<void>

// Utility methods
duplicateWorkflow(id: string, newName: string): Promise<AutomationWorkflow>
getActiveWorkflowsByTrigger(triggerType: string): Promise<AutomationWorkflow[]>
```

#### automationExecutionService
```typescript
// Execution management
getExecutions(workflowId?: string, limit?: number): Promise<AutomationExecution[]>
createExecution(execution: Omit<AutomationExecution, 'id' | 'executedAt'>): Promise<AutomationExecution>
getExecutionStats(workflowId: string, days?: number): Promise<ExecutionStats>
```

### Store Integration

The automation system integrates with the main Zustand store:

```typescript
const useAutomationStore = create<AutomationStoreState>((set, get) => ({
  // Data
  workflows: [],
  executions: [],
  
  // Actions
  loadWorkflows: async (projectId?: string) => { /* ... */ },
  createWorkflow: async (workflow) => { /* ... */ },
  executeWorkflow: async (workflowId, triggerData) => { /* ... */ },
  
  // UI State
  openWorkflowBuilder: (workflow?) => { /* ... */ },
  openExecutionLog: (workflowId?) => { /* ... */ }
}));
```

## Security Considerations

### Application-Level Validation
- All workflow operations validate user permissions
- Execution context includes authenticated user information
- Action execution respects existing access controls

### Row Level Security (RLS)
- Workflows are scoped to creators and project members
- Execution logs are only visible to authorized users
- Templates can be shared across projects with proper permissions

### Safe Execution
- Actions are executed with proper error handling
- Infinite loop prevention through execution limits
- Resource usage monitoring and throttling

## Performance Optimization

### Execution Efficiency
- Asynchronous workflow processing
- Batch action execution where possible
- Efficient condition evaluation with early termination

### Database Optimization
- Indexed queries for workflow lookup
- Execution log retention policies
- Optimized JSONB queries for configuration data

### Monitoring
- Execution time tracking
- Success/failure rate monitoring
- Resource usage analytics

## Troubleshooting

### Common Issues

#### Workflow Not Triggering
1. Check if workflow is active
2. Verify trigger configuration matches event data
3. Review condition logic for false negatives
4. Check execution logs for errors

#### Action Failures
1. Verify user permissions for target resources
2. Check action configuration for invalid values
3. Review template variable syntax
4. Validate target entity existence

#### Performance Issues
1. Review condition complexity
2. Check for excessive action counts
3. Monitor execution frequency
4. Optimize trigger specificity

### Debugging Tools

#### Execution Log Viewer
- Real-time execution monitoring
- Detailed error messages and stack traces
- Performance metrics and timing data
- Condition evaluation results

#### Workflow Testing
- Test mode with sample data
- Dry-run execution without side effects
- Condition validation and preview
- Action simulation and validation

## Best Practices

### Workflow Design
- Use specific triggers to reduce unnecessary executions
- Keep conditions simple and readable
- Group related actions together
- Use descriptive names and documentation

### Performance
- Avoid overly complex condition logic
- Limit the number of actions per workflow
- Use appropriate trigger specificity
- Monitor execution frequency and performance

### Maintenance
- Regularly review and update workflows
- Archive unused workflows
- Monitor execution success rates
- Update templates based on usage patterns

### Security
- Follow principle of least privilege
- Validate all user inputs
- Use application-level permissions
- Audit workflow changes and executions

## Future Enhancements

### Planned Features
- Visual workflow debugging with step-through execution
- Advanced scheduling with cron expressions
- Workflow versioning and rollback capabilities
- Integration with external automation platforms
- Machine learning-based workflow suggestions
- Advanced analytics and reporting dashboard

### API Extensions
- REST API for external integrations
- Webhook management interface
- Bulk workflow operations
- Advanced query capabilities for executions and analytics
