import React from 'react';
import { TaskDuration } from '../types';
import { differenceInHours, differenceInMinutes } from 'date-fns';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface CompactTimelineStatsProps {
  durations: TaskDuration[];
  currentStatus: string;
}

export default function CompactTimelineStats({ durations, currentStatus }: CompactTimelineStatsProps) {
  const { columns } = useSupabaseStore();

  // Handle undefined durations
  if (!durations || !Array.isArray(durations)) {
    return (
      <div className="text-center text-gray-500 py-2 text-sm">
        No duration data available
      </div>
    );
  }

  // Group durations by status
  const durationsByStatus = durations.reduce((acc, duration) => {
    if (!acc[duration.status]) {
      acc[duration.status] = [];
    }
    acc[duration.status].push(duration);
    return acc;
  }, {} as Record<string, TaskDuration[]>);

  // Calculate total time per status
  const totalTimeByStatus = Object.entries(durationsByStatus).map(([statusId, statusDurations]) => {
    let totalMinutes = 0;

    statusDurations.forEach(duration => {
      const start = new Date(duration.startTime);
      const end = duration.endTime ? new Date(duration.endTime) : 
        (statusId === currentStatus ? new Date() : start);
      
      totalMinutes += differenceInMinutes(end, start);
    });

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    const column = columns.find(col => col.id === statusId);
    const statusLabel = column ? column.title : statusId;

    return {
      statusId,
      statusLabel,
      totalTime: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`,
      totalMinutes,
      color: column?.color || 'bg-gray-100 text-gray-800'
    };
  });

  // Calculate total time for percentage calculation
  const totalMinutesAll = totalTimeByStatus.reduce((sum, status) => sum + status.totalMinutes, 0);

  // Sort by order of appearance in columns
  const sortedTimeByStatus = totalTimeByStatus.sort((a, b) => {
    const aIndex = columns.findIndex(col => col.id === a.statusId);
    const bIndex = columns.findIndex(col => col.id === b.statusId);
    return aIndex - bIndex;
  });

  if (sortedTimeByStatus.length === 0) {
    return (
      <div className="text-center text-gray-500 py-2 text-sm">
        No duration data available
      </div>
    );
  }

  return (
    <div className="mb-4">
      <div className="text-sm font-medium text-gray-700 mb-2">Time in Status</div>
      
      {/* Horizontal Timeline Bar */}
      <div className="flex rounded-lg overflow-hidden border border-gray-200 h-8">
        {sortedTimeByStatus.map(({ statusId, statusLabel, totalTime, totalMinutes, color }, index) => {
          const percentage = totalMinutesAll > 0 ? (totalMinutes / totalMinutesAll) * 100 : 0;
          const minWidth = percentage < 15 ? 15 : percentage; // Ensure minimum width for readability
          
          return (
            <div
              key={statusId}
              className={`flex items-center justify-center text-xs font-medium transition-all duration-200 hover:opacity-80 ${color}`}
              style={{ 
                width: `${minWidth}%`,
                minWidth: '60px' // Ensure text is readable
              }}
              title={`${statusLabel}: ${totalTime}`}
            >
              <span className="truncate px-1">
                {totalTime}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* Status Labels Below */}
      <div className="flex mt-1 text-xs text-gray-600">
        {sortedTimeByStatus.map(({ statusId, statusLabel, totalMinutes }, index) => {
          const percentage = totalMinutesAll > 0 ? (totalMinutes / totalMinutesAll) * 100 : 0;
          const minWidth = percentage < 15 ? 15 : percentage;
          
          return (
            <div
              key={`label-${statusId}`}
              className="text-center truncate"
              style={{ 
                width: `${minWidth}%`,
                minWidth: '60px'
              }}
            >
              {statusLabel}
            </div>
          );
        })}
      </div>
    </div>
  );
}
