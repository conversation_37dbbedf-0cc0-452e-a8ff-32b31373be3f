{"tasks": [{"title": "task 2", "description": "desc 2", "status": "in-progress", "priority": "medium", "startDate": "2025-07-21", "dueDate": "2025-07-23", "tags": [], "assignedGroups": ["devs"], "assignedUsers": ["ctlhesdr0"], "owner": "pm", "projectId": "ttmrqrdd6", "folderId": "", "subtasks": [], "comments": [], "history": [{"id": "ct0nl46yn", "taskId": "mi1p8pb8q", "timestamp": "2025-07-21T20:23:43.637Z", "field": "status", "oldValue": "To Do", "newValue": "In Progress", "userId": "system"}, {"id": "df34wtcou", "taskId": "mi1p8pb8q", "timestamp": "2025-07-21T20:22:29.780Z", "field": "effort", "newValue": "{\"taskId\":\"mi1p8pb8q\",\"estimatedHours\":21,\"actualHours\":0,\"requiredSkillsets\":[\"fvaar4ewj\",\"fmpmpkjmh\"]}", "userId": "system"}, {"id": "b027ey4e3", "taskId": "mi1p8pb8q", "timestamp": "2025-07-20T19:06:02.455Z", "field": "projectId", "oldValue": "\"\"", "newValue": "\"ttmrqrdd6\"", "userId": "system"}, {"id": "gfpx8nmio", "taskId": "mi1p8pb8q", "timestamp": "2025-07-21T20:24:22.291Z", "field": "effort", "oldValue": "{\"taskId\":\"mi1p8pb8q\",\"estimatedHours\":21,\"actualHours\":0,\"requiredSkillsets\":[\"fvaar4ewj\",\"fmpmpkjmh\"]}", "newValue": "{\"taskId\":\"mi1p8pb8q\",\"estimatedHours\":21,\"actualHours\":0,\"assignedUserId\":\"ctlhesdr0\",\"requiredSkillsets\":[\"fvaar4ewj\",\"fmpmpkjmh\"]}", "userId": "system"}], "durations": [{"status": "in-progress", "startTime": "2025-07-21T20:23:43.637Z"}], "id": "mi1p8pb8q", "effort": {"taskId": "mi1p8pb8q", "estimatedHours": 21, "actualHours": 0, "assignedUserId": "ctlhesdr0", "requiredSkillsets": ["fvaar4ewj", "fmpmpkjmh"]}}, {"title": "project 2 task", "description": "sdasd", "status": "review", "priority": "high", "startDate": "2025-07-21", "dueDate": "2025-07-25", "tags": [], "assignedGroups": ["devs"], "assignedUsers": ["ctlhesdr0"], "owner": "pm", "projectId": "ssz8awxqv", "folderId": "", "subtasks": [], "comments": [], "history": [{"id": "gzwy6i9j7", "taskId": "w7wt7l9iv", "timestamp": "2025-07-21T20:23:05.779Z", "field": "dueDate", "oldValue": "\"2025-07-23\"", "newValue": "\"2025-07-25\"", "userId": "system"}, {"id": "nlumpzyd0", "taskId": "w7wt7l9iv", "timestamp": "2025-07-21T20:23:05.779Z", "field": "effort", "newValue": "{\"taskId\":\"w7wt7l9iv\",\"estimatedHours\":30,\"actualHours\":0,\"assignedUserId\":\"ctlhesdr0\",\"requiredSkillsets\":[\"fmpmpkjmh\",\"fvaar4ewj\"]}", "userId": "system"}, {"id": "iu220yk7h", "taskId": "w7wt7l9iv", "timestamp": "2025-07-21T20:22:39.435Z", "field": "status", "oldValue": "To Do", "newValue": "Review", "userId": "system"}], "durations": [{"status": "review", "startTime": "2025-07-21T20:22:39.435Z"}], "id": "w7wt7l9iv", "effort": {"taskId": "w7wt7l9iv", "estimatedHours": 30, "actualHours": 0, "assignedUserId": "ctlhesdr0", "requiredSkillsets": ["fmpmpkjmh", "fvaar4ewj"]}}, {"title": "Website Redesign Homepage", "description": "Complete redesign of the main homepage with new branding guidelines", "status": "todo", "priority": "high", "startDate": "2025-07-22", "dueDate": "2025-07-28", "tags": ["design", "frontend"], "assignedGroups": ["devs"], "assignedUsers": ["js8k2m9p1", "ak3n7q2r8"], "owner": "pm", "projectId": "nx9w4v7t2", "folderId": "h5m8k9j2p", "subtasks": [{"id": "sub_001", "title": "Create wireframes", "completed": true}, {"id": "sub_002", "title": "Design mockups", "completed": false}], "comments": [{"id": "comment_001", "userId": "js8k2m9p1", "content": "Working on the initial wireframes", "timestamp": "2025-07-21T10:30:00.000Z"}], "history": [{"id": "hist_001", "taskId": "tk3m5n8w9", "timestamp": "2025-07-21T09:00:00.000Z", "field": "status", "oldValue": "", "newValue": "To Do", "userId": "pm1x4z7b9"}], "durations": [], "id": "tk3m5n8w9", "effort": {"taskId": "tk3m5n8w9", "estimatedHours": 40, "actualHours": 8, "assignedUserId": "js8k2m9p1", "requiredSkillsets": ["fvaar4ewj", "css3k9j2m", "js4l7n8p2"]}}, {"title": "Database Migration", "description": "Migrate legacy database to new PostgreSQL instance", "status": "in-progress", "priority": "critical", "startDate": "2025-07-20", "dueDate": "2025-07-24", "tags": ["backend", "database"], "assignedGroups": ["devs"], "assignedUsers": ["mk7p9q3s5"], "owner": "leaders", "projectId": "nx9w4v7t2", "folderId": "t8n3k6j9m", "subtasks": [], "comments": [], "history": [{"id": "hist_002", "taskId": "db2x7k9m4", "timestamp": "2025-07-20T14:00:00.000Z", "field": "status", "oldValue": "To Do", "newValue": "In Progress", "userId": "mk7p9q3s5"}], "durations": [{"status": "in-progress", "startTime": "2025-07-20T14:00:00.000Z"}], "id": "db2x7k9m4", "effort": {"taskId": "db2x7k9m4", "estimatedHours": 32, "actualHours": 12, "assignedUserId": "mk7p9q3s5", "requiredSkillsets": ["sql9m4k7n", "py8j3k2m9"]}}, {"title": "Email Campaign Setup", "description": "Set up automated email sequences for Q3 marketing campaign", "status": "review", "priority": "medium", "startDate": "2025-07-18", "dueDate": "2025-07-22", "tags": ["marketing", "automation"], "assignedGroups": ["cm"], "assignedUsers": ["em4k8l2n9", "rp7j9k3m6"], "owner": "cm", "projectId": "cm8k3j9m2", "folderId": "q3m7k9j2p", "subtasks": [], "comments": [{"id": "comment_002", "userId": "em4k8l2n9", "content": "Email templates are ready for review", "timestamp": "2025-07-21T16:45:00.000Z"}], "history": [{"id": "hist_003", "taskId": "em5k9j3m7", "timestamp": "2025-07-21T16:30:00.000Z", "field": "status", "oldValue": "In Progress", "newValue": "Review", "userId": "em4k8l2n9"}], "durations": [{"status": "review", "startTime": "2025-07-21T16:30:00.000Z"}], "id": "em5k9j3m7", "effort": {"taskId": "em5k9j3m7", "estimatedHours": 16, "actualHours": 14, "assignedUserId": "em4k8l2n9", "requiredSkillsets": ["fmpmpkjmh", "mk7j3k9m2"]}}, {"title": "API Documentation", "description": "Create comprehensive API documentation for external partners", "status": "done", "priority": "medium", "startDate": "2025-07-15", "dueDate": "2025-07-20", "tags": ["documentation", "api"], "assignedGroups": ["devs"], "assignedUsers": ["ak3n7q2r8"], "owner": "leaders", "projectId": "nx9w4v7t2", "folderId": "t8n3k6j9m", "subtasks": [], "comments": [], "history": [{"id": "hist_004", "taskId": "api8k3j9m2", "timestamp": "2025-07-20T11:00:00.000Z", "field": "status", "oldValue": "Review", "newValue": "Done", "userId": "ld2k8j9m3"}], "durations": [{"status": "done", "startTime": "2025-07-20T11:00:00.000Z"}], "id": "api8k3j9m2", "effort": {"taskId": "api8k3j9m2", "estimatedHours": 24, "actualHours": 22, "assignedUserId": "ak3n7q2r8", "requiredSkillsets": ["js4l7n8p2", "doc7k3j9m"]}}, {"title": "Mobile App Testing", "description": "Comprehensive testing of mobile application across different devices", "status": "todo", "priority": "high", "startDate": "2025-07-23", "dueDate": "2025-07-30", "tags": ["testing", "mobile"], "assignedGroups": ["devs"], "assignedUsers": ["qa9k2j8m7"], "owner": "pm", "projectId": "mb3k9j7m2", "folderId": "m9k7j3n2p", "subtasks": [{"id": "sub_003", "title": "iOS testing", "completed": false}, {"id": "sub_004", "title": "Android testing", "completed": false}, {"id": "sub_005", "title": "Cross-platform compatibility", "completed": false}], "comments": [], "history": [], "durations": [], "id": "mob7k3j9m", "effort": {"taskId": "mob7k3j9m", "estimatedHours": 35, "actualHours": 0, "assignedUserId": "qa9k2j8m7", "requiredSkillsets": ["test8k3j9", "mob5j7k2m"]}}, {"title": "Social Media Content Calendar", "description": "Plan and create content calendar for August social media campaigns", "status": "in-progress", "priority": "low", "startDate": "2025-07-21", "dueDate": "2025-07-26", "tags": ["content", "social"], "assignedGroups": ["cm"], "assignedUsers": ["sm8j3k9m2"], "owner": "cm", "projectId": "cm8k3j9m2", "folderId": "soc9k3j7m", "subtasks": [], "comments": [], "history": [{"id": "hist_005", "taskId": "soc3k9j7m", "timestamp": "2025-07-21T13:00:00.000Z", "field": "status", "oldValue": "To Do", "newValue": "In Progress", "userId": "sm8j3k9m2"}], "durations": [{"status": "in-progress", "startTime": "2025-07-21T13:00:00.000Z"}], "id": "soc3k9j7m", "effort": {"taskId": "soc3k9j7m", "estimatedHours": 20, "actualHours": 6, "assignedUserId": "sm8j3k9m2", "requiredSkillsets": ["cont7k3j9", "soc9k2j8m"]}}], "projects": [{"name": "project 1", "description": "", "color": "bg-pink-500", "folderId": "29ngafjn6", "id": "ttmrqrdd6", "tasks": []}, {"name": "project 2", "description": "", "color": "bg-green-500", "folderId": "6qwu9sbcn", "id": "ssz8awxqv", "tasks": []}, {"name": "Website Revamp", "description": "Complete overhaul of company website including design and backend improvements", "color": "bg-blue-500", "folderId": "h5m8k9j2p", "id": "nx9w4v7t2", "tasks": []}, {"name": "Q3 Marketing Campaign", "description": "Comprehensive marketing campaign for third quarter targets", "color": "bg-purple-500", "folderId": "q3m7k9j2p", "id": "cm8k3j9m2", "tasks": []}, {"name": "Mobile App Development", "description": "Native mobile application for iOS and Android platforms", "color": "bg-orange-500", "folderId": "m9k7j3n2p", "id": "mb3k9j7m2", "tasks": []}], "userGroups": [{"id": "devs", "name": "Developers", "color": "bg-blue-500"}, {"id": "pm", "name": "Project Managers", "color": "bg-green-500"}, {"id": "cm", "name": "Campaign Managers", "color": "bg-purple-500"}, {"id": "leaders", "name": "Team Leaders", "color": "bg-red-500"}, {"id": "qa", "name": "Quality Assurance", "color": "bg-yellow-500"}, {"id": "design", "name": "Designers", "color": "bg-pink-500"}], "users": [{"name": "vklonis", "email": "<EMAIL>", "groupId": "devs", "avatar": "", "id": "ctlhesdr0", "skillsetIds": ["fvaar4ewj", "fmpmpkjmh"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "devs", "avatar": "", "id": "js8k2m9p1", "skillsetIds": ["fvaar4ewj", "css3k9j2m", "js4l7n8p2"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "devs", "avatar": "", "id": "ak3n7q2r8", "skillsetIds": ["js4l7n8p2", "py8j3k2m9", "doc7k3j9m"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "devs", "avatar": "", "id": "mk7p9q3s5", "skillsetIds": ["sql9m4k7n", "py8j3k2m9", "fvaar4ewj"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "cm", "avatar": "", "id": "em4k8l2n9", "skillsetIds": ["fmpmpkjmh", "mk7j3k9m2"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "cm", "avatar": "", "id": "rp7j9k3m6", "skillsetIds": ["fmpmpkjmh", "cont7k3j9"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "pm", "avatar": "", "id": "pm1x4z7b9", "skillsetIds": ["pm9k3j7m2"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "leaders", "avatar": "", "id": "ld2k8j9m3", "skillsetIds": ["lead8k3j9", "pm9k3j7m2"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "qa", "avatar": "", "id": "qa9k2j8m7", "skillsetIds": ["test8k3j9", "mob5j7k2m"]}, {"name": "<PERSON>", "email": "<EMAIL>", "groupId": "cm", "avatar": "", "id": "sm8j3k9m2", "skillsetIds": ["cont7k3j9", "soc9k2j8m"]}], "columns": [{"id": "todo", "title": "To Do", "color": "bg-gray-100"}, {"id": "in-progress", "title": "In Progress", "color": "bg-blue-50"}, {"id": "review", "title": "Review", "color": "bg-yellow-50"}, {"id": "done", "title": "Done", "color": "bg-green-50"}], "folders": [{"name": "client 1", "id": "k8cdumroc"}, {"name": "client 2", "id": "6qwu9sbcn"}, {"name": "campaign 1", "parentId": "k8cdumroc", "id": "29ngafjn6"}, {"name": "Enterprise Solutions", "id": "ent9k3j7m"}, {"name": "Web Development", "parentId": "ent9k3j7m", "id": "h5m8k9j2p"}, {"name": "Backend Services", "parentId": "h5m8k9j2p", "id": "t8n3k6j9m"}, {"name": "Frontend Components", "parentId": "h5m8k9j2p", "id": "fr3k9j7m2"}, {"name": "Marketing Division", "id": "mk3j9m7k2"}, {"name": "Q3 Campaigns", "parentId": "mk3j9m7k2", "id": "q3m7k9j2p"}, {"name": "Social Media", "parentId": "q3m7k9j2p", "id": "soc9k3j7m"}, {"name": "Email Marketing", "parentId": "q3m7k9j2p", "id": "em7k3j9m2"}, {"name": "Mobile Development", "id": "m9k7j3n2p"}, {"name": "iOS Development", "parentId": "m9k7j3n2p", "id": "ios8k3j9m"}, {"name": "Android Development", "parentId": "m9k7j3n2p", "id": "and7k3j9m"}], "skillsetGroups": [{"name": "html", "description": "", "color": "bg-blue-500", "id": "fvaar4ewj", "createdAt": "2025-07-21T20:22:19.944Z", "updatedAt": "2025-07-21T20:22:19.944Z"}, {"name": "ESP", "description": "", "color": "bg-green-500", "id": "fmpmpkjmh", "createdAt": "2025-07-21T20:22:19.988Z", "updatedAt": "2025-07-21T20:22:19.988Z"}, {"name": "CSS", "description": "Advanced CSS and styling skills", "color": "bg-purple-500", "id": "css3k9j2m", "createdAt": "2025-07-21T10:00:00.000Z", "updatedAt": "2025-07-21T10:00:00.000Z"}, {"name": "JavaScript", "description": "Frontend and backend JavaScript development", "color": "bg-yellow-500", "id": "js4l7n8p2", "createdAt": "2025-07-21T10:05:00.000Z", "updatedAt": "2025-07-21T10:05:00.000Z"}, {"name": "Python", "description": "Backend development and data processing", "color": "bg-orange-500", "id": "py8j3k2m9", "createdAt": "2025-07-21T10:10:00.000Z", "updatedAt": "2025-07-21T10:10:00.000Z"}, {"name": "SQL", "description": "Database design and management", "color": "bg-indigo-500", "id": "sql9m4k7n", "createdAt": "2025-07-21T10:15:00.000Z", "updatedAt": "2025-07-21T10:15:00.000Z"}, {"name": "Marketing Automation", "description": "Email and campaign automation tools", "color": "bg-pink-500", "id": "mk7j3k9m2", "createdAt": "2025-07-21T10:20:00.000Z", "updatedAt": "2025-07-21T10:20:00.000Z"}, {"name": "Technical Writing", "description": "Documentation and technical communication", "color": "bg-gray-500", "id": "doc7k3j9m", "createdAt": "2025-07-21T10:25:00.000Z", "updatedAt": "2025-07-21T10:25:00.000Z"}, {"name": "Quality Assurance", "description": "Testing and QA methodologies", "color": "bg-red-500", "id": "test8k3j9", "createdAt": "2025-07-21T10:30:00.000Z", "updatedAt": "2025-07-21T10:30:00.000Z"}, {"name": "Mobile Development", "description": "iOS and Android development", "color": "bg-teal-500", "id": "mob5j7k2m", "createdAt": "2025-07-21T10:35:00.000Z", "updatedAt": "2025-07-21T10:35:00.000Z"}, {"name": "Content Creation", "description": "Content strategy and creation", "color": "bg-lime-500", "id": "cont7k3j9", "createdAt": "2025-07-21T10:40:00.000Z", "updatedAt": "2025-07-21T10:40:00.000Z"}, {"name": "Social Media", "description": "Social media management and strategy", "color": "bg-cyan-500", "id": "soc9k2j8m", "createdAt": "2025-07-21T10:45:00.000Z", "updatedAt": "2025-07-21T10:45:00.000Z"}, {"name": "Project Management", "description": "Project planning and management", "color": "bg-emerald-500", "id": "pm9k3j7m2", "createdAt": "2025-07-21T10:50:00.000Z", "updatedAt": "2025-07-21T10:50:00.000Z"}, {"name": "Leadership", "description": "Team leadership and management skills", "color": "bg-rose-500", "id": "lead8k3j9", "createdAt": "2025-07-21T10:55:00.000Z", "updatedAt": "2025-07-21T10:55:00.000Z"}], "userCapacities": [{"userId": "ctlhesdr0", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "js8k2m9p1", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "ak3n7q2r8", "dailyHours": 7, "weeklyHours": 35, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "mk7p9q3s5", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "em4k8l2n9", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "rp7j9k3m6", "dailyHours": 6, "weeklyHours": 30, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "pm1x4z7b9", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "ld2k8j9m3", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "qa9k2j8m7", "dailyHours": 8, "weeklyHours": 40, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}, {"userId": "sm8j3k9m2", "dailyHours": 7, "weeklyHours": 35, "workingDays": [1, 2, 3, 4, 5], "effectiveFrom": "2025-07-21"}], "taskEfforts": [{"taskId": "mi1p8pb8q", "estimatedHours": 21, "actualHours": 0, "assignedUserId": "ctlhesdr0", "requiredSkillsets": ["fvaar4ewj", "fmpmpkjmh"]}, {"taskId": "w7wt7l9iv", "estimatedHours": 30, "actualHours": 0, "assignedUserId": "ctlhesdr0", "requiredSkillsets": ["fmpmpkjmh", "fvaar4ewj"]}, {"taskId": "tk3m5n8w9", "estimatedHours": 40, "actualHours": 8, "assignedUserId": "js8k2m9p1", "requiredSkillsets": ["fvaar4ewj", "css3k9j2m", "js4l7n8p2"]}, {"taskId": "db2x7k9m4", "estimatedHours": 32, "actualHours": 12, "assignedUserId": "mk7p9q3s5", "requiredSkillsets": ["sql9m4k7n", "py8j3k2m9"]}, {"taskId": "em5k9j3m7", "estimatedHours": 16, "actualHours": 14, "assignedUserId": "em4k8l2n9", "requiredSkillsets": ["fmpmpkjmh", "mk7j3k9m2"]}, {"taskId": "api8k3j9m2", "estimatedHours": 24, "actualHours": 22, "assignedUserId": "ak3n7q2r8", "requiredSkillsets": ["js4l7n8p2", "doc7k3j9m"]}, {"taskId": "mob7k3j9m", "estimatedHours": 35, "actualHours": 0, "assignedUserId": "qa9k2j8m7", "requiredSkillsets": ["test8k3j9", "mob5j7k2m"]}, {"taskId": "soc3k9j7m", "estimatedHours": 20, "actualHours": 6, "assignedUserId": "sm8j3k9m2", "requiredSkillsets": ["cont7k3j9", "soc9k2j8m"]}], "version": "2.0", "timestamp": "2025-07-21T20:24:32.167Z", "features": ["resource-management", "capacity-planning", "skillset-groups", "effort-estimation", "task-filtering", "project-hierarchy", "team-collaboration", "task-dependencies"]}