import { User } from '../types';

/**
 * Extracts @username mentions from text content
 * @param content - The text content to parse
 * @returns Array of mentioned usernames (without the @ symbol)
 */
export function extractMentions(content: string): string[] {
  // Match @username pattern - alphanumeric, underscore, dot, hyphen
  const mentionRegex = /@([a-zA-Z0-9._-]+)/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    const username = match[1];
    if (!mentions.includes(username)) {
      mentions.push(username);
    }
  }

  return mentions;
}

/**
 * Validates that mentioned usernames exist in the user list
 * @param mentions - Array of mentioned usernames
 * @param users - Array of all users
 * @returns Array of valid user objects that were mentioned
 */
export function validateMentions(mentions: string[], users: User[]): User[] {
  const validUsers: User[] = [];

  for (const mention of mentions) {
    // Try to find user by name first (case-insensitive)
    let user = users.find(u => u.name.toLowerCase() === mention.toLowerCase());
    
    // If not found by name, try by email (case-insensitive)
    if (!user) {
      user = users.find(u => u.email.toLowerCase() === mention.toLowerCase());
    }

    if (user && !validUsers.find(u => u.id === user.id)) {
      validUsers.push(user);
    }
  }

  return validUsers;
}

/**
 * Highlights @mentions in text content for display
 * @param content - The text content to process
 * @param users - Array of all users for validation
 * @returns HTML string with highlighted mentions
 */
export function highlightMentions(content: string, users: User[]): string {
  const mentionRegex = /@([a-zA-Z0-9._-]+)/g;
  
  return content.replace(mentionRegex, (match, username) => {
    // Check if this is a valid user
    const user = users.find(u => 
      u.name.toLowerCase() === username.toLowerCase() || 
      u.email.toLowerCase() === username.toLowerCase()
    );
    
    if (user) {
      return `<span class="mention-highlight bg-blue-100 text-blue-800 px-1 rounded">${match}</span>`;
    }
    
    return match; // Return unchanged if not a valid user
  });
}

/**
 * Creates notification content for a mention
 * @param mentionerName - Name of the user who made the mention
 * @param taskTitle - Title of the task where the mention occurred
 * @param commentContent - Content of the comment (truncated for notification)
 * @returns Object with title and content for the notification
 */
export function createMentionNotificationContent(
  mentionerName: string,
  taskTitle: string,
  commentContent: string
): { title: string; content: string } {
  // Truncate comment content for notification display
  const maxLength = 100;
  const truncatedContent = commentContent.length > maxLength 
    ? commentContent.substring(0, maxLength) + '...'
    : commentContent;

  return {
    title: `${mentionerName} mentioned you in "${taskTitle}"`,
    content: truncatedContent
  };
}

/**
 * Checks if a user should receive a notification for a mention
 * @param mentionedUserId - ID of the mentioned user
 * @param senderId - ID of the user who made the mention
 * @returns boolean indicating if notification should be sent
 */
export function shouldNotifyUser(mentionedUserId: string, senderId: string): boolean {
  // Don't notify if user mentions themselves
  return mentionedUserId !== senderId;
}
