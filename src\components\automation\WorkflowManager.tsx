import React, { useEffect, useState } from 'react';
import { Plus, Settings, Play, Pause, Copy, Trash2, Bar<PERSON>hart<PERSON>, <PERSON>, Edit, Layers, History } from 'lucide-react';
import { useAutomationStore } from '../../store/useAutomationStore';
import { AutomationWorkflow } from '../../types/automation';
import WorkflowBuilder from './WorkflowBuilder';
import ExecutionLog from './ExecutionLog';
import WorkflowTemplates from './WorkflowTemplates';

export default function WorkflowManager() {
  const {
    workflows,
    loading,
    error,
    loadWorkflows,
    updateWorkflow,
    deleteWorkflow,
    duplicateWorkflow,
    openWorkflowBuilder,
    closeWorkflowBuilder,
    isWorkflowBuilderOpen,
    selectedWorkflow,
    getExecutionStats,
    clearError
  } = useAutomationStore();

  const [showBuilder, setShowBuilder] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState<AutomationWorkflow | null>(null);
  const [executionStats, setExecutionStats] = useState<Record<string, any>>({});
  const [showExecutionLog, setShowExecutionLog] = useState(false);
  const [selectedWorkflowForLog, setSelectedWorkflowForLog] = useState<string | undefined>();
  const [showTemplates, setShowTemplates] = useState(false);

  useEffect(() => {
    loadWorkflows();
  }, [loadWorkflows]);

  useEffect(() => {
    if (error) {
      console.error('Automation error:', error);
      setTimeout(() => clearError(), 5000);
    }
  }, [error, clearError]);

  const handleCreateWorkflow = () => {
    setEditingWorkflow(null);
    setShowBuilder(true);
  };

  const handleCreateFromTemplate = () => {
    setShowTemplates(true);
  };

  const handleSelectTemplate = (template: any) => {
    setEditingWorkflow(template);
    setShowBuilder(true);
  };

  const handleViewExecutions = (workflowId: string) => {
    setSelectedWorkflowForLog(workflowId);
    setShowExecutionLog(true);
  };

  const handleEditWorkflow = (workflow: AutomationWorkflow) => {
    setEditingWorkflow(workflow);
    setShowBuilder(true);
  };

  const handleToggleActive = async (workflow: AutomationWorkflow) => {
    await updateWorkflow(workflow.id, { isActive: !workflow.isActive });
  };

  const handleDuplicate = async (workflow: AutomationWorkflow) => {
    const newName = prompt('Enter name for duplicated workflow:', `${workflow.name} (Copy)`);
    if (newName) {
      await duplicateWorkflow(workflow.id, newName);
    }
  };

  const handleDelete = async (workflow: AutomationWorkflow) => {
    if (confirm(`Are you sure you want to delete "${workflow.name}"?`)) {
      await deleteWorkflow(workflow.id);
    }
  };

  const loadStats = async (workflowId: string) => {
    if (!executionStats[workflowId]) {
      const stats = await getExecutionStats(workflowId);
      if (stats) {
        setExecutionStats(prev => ({ ...prev, [workflowId]: stats }));
      }
    }
  };

  const getTriggerTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'task_created': 'Task Created',
      'task_updated': 'Task Updated',
      'task_status_changed': 'Status Changed',
      'task_assigned': 'Task Assigned',
      'comment_added': 'Comment Added',
      'project_created': 'Project Created',
      'project_updated': 'Project Updated',
      'schedule': 'Scheduled',
      'webhook': 'Webhook',
      'custom_event': 'Custom Event'
    };
    return labels[type] || type;
  };

  const renderWorkflowCard = (workflow: AutomationWorkflow) => {
    const stats = executionStats[workflow.id];

    return (
      <div
        key={workflow.id}
        className="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-gray-600 transition-colors"
      >
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-white">{workflow.name}</h3>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  workflow.isActive
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-gray-500/20 text-gray-400'
                }`}
              >
                {workflow.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            {workflow.description && (
              <p className="text-gray-400 text-sm mb-3">{workflow.description}</p>
            )}
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>Trigger: {getTriggerTypeLabel(workflow.triggerConfig.type)}</span>
              <span>Actions: {workflow.actionConfig.length}</span>
              <span>Executions: {workflow.executionCount}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleToggleActive(workflow)}
              className={`p-2 rounded-lg transition-colors ${
                workflow.isActive
                  ? 'text-green-400 hover:bg-green-500/20'
                  : 'text-gray-400 hover:bg-gray-700'
              }`}
              title={workflow.isActive ? 'Pause workflow' : 'Activate workflow'}
            >
              {workflow.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </button>
            
            <button
              onClick={() => handleEditWorkflow(workflow)}
              className="p-2 text-blue-400 hover:bg-blue-500/20 rounded-lg transition-colors"
              title="Edit workflow"
            >
              <Edit className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => handleDuplicate(workflow)}
              className="p-2 text-yellow-400 hover:bg-yellow-500/20 rounded-lg transition-colors"
              title="Duplicate workflow"
            >
              <Copy className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => loadStats(workflow.id)}
              className="p-2 text-purple-400 hover:bg-purple-500/20 rounded-lg transition-colors"
              title="View statistics"
            >
              <BarChart3 className="w-4 h-4" />
            </button>

            <button
              onClick={() => handleViewExecutions(workflow.id)}
              className="p-2 text-indigo-400 hover:bg-indigo-500/20 rounded-lg transition-colors"
              title="View execution log"
            >
              <History className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => handleDelete(workflow)}
              className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
              title="Delete workflow"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {stats && (
          <div className="border-t border-gray-700 pt-4 mt-4">
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-white font-medium">{stats.total}</div>
                <div className="text-gray-400">Total</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-medium">{stats.success}</div>
                <div className="text-gray-400">Success</div>
              </div>
              <div className="text-center">
                <div className="text-red-400 font-medium">{stats.failed}</div>
                <div className="text-gray-400">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-blue-400 font-medium">{stats.successRate.toFixed(1)}%</div>
                <div className="text-gray-400">Success Rate</div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Automation Workflows</h1>
          <p className="text-gray-400 mt-1">
            Create and manage automated workflows for your project management
          </p>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={handleCreateFromTemplate}
            disabled={loading.workflows}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
          >
            <Layers className="w-4 h-4" />
            From Template
          </button>
          <button
            onClick={handleCreateWorkflow}
            disabled={loading.workflows}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Workflow
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {loading.workflows ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-gray-400">Loading workflows...</div>
        </div>
      ) : workflows.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🤖</div>
          <h3 className="text-xl font-semibold text-white mb-2">No Workflows Yet</h3>
          <p className="text-gray-400 mb-6">
            Create your first automation workflow to streamline your project management
          </p>
          <button
            onClick={handleCreateWorkflow}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 mx-auto"
          >
            <Plus className="w-5 h-5" />
            Create Your First Workflow
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {workflows.map(renderWorkflowCard)}
        </div>
      )}

      {showBuilder && (
        <WorkflowBuilder
          workflow={editingWorkflow || undefined}
          onClose={() => {
            setShowBuilder(false);
            setEditingWorkflow(null);
          }}
        />
      )}

      {showExecutionLog && (
        <ExecutionLog
          workflowId={selectedWorkflowForLog}
          onClose={() => {
            setShowExecutionLog(false);
            setSelectedWorkflowForLog(undefined);
          }}
        />
      )}

      {showTemplates && (
        <WorkflowTemplates
          onClose={() => setShowTemplates(false)}
          onSelectTemplate={handleSelectTemplate}
        />
      )}
    </div>
  );
}
