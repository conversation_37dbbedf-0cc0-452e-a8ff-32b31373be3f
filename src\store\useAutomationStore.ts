import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  AutomationWorkflow, 
  AutomationExecution, 
  AutomationTrigger,
  TriggerType 
} from '../types/automation';
import {
  automationWorkflowService,
  automationExecutionService,
  automationTriggerService
} from '../services/automationService';

interface AutomationStoreState {
  // Data
  workflows: AutomationWorkflow[];
  executions: AutomationExecution[];
  triggers: AutomationTrigger[];
  
  // UI State
  selectedWorkflow: AutomationWorkflow | null;
  isWorkflowBuilderOpen: boolean;
  isExecutionLogOpen: boolean;
  
  // Loading states
  loading: {
    workflows: boolean;
    executions: boolean;
    triggers: boolean;
    creating: boolean;
    updating: boolean;
    deleting: boolean;
    executing: boolean;
  };
  
  // Error states
  error: string | null;
  
  // Actions
  loadWorkflows: (projectId?: string, folderId?: string) => Promise<void>;
  loadExecutions: (workflowId?: string, limit?: number) => Promise<void>;
  loadTriggers: (workflowId?: string) => Promise<void>;
  
  createWorkflow: (workflow: Omit<AutomationWorkflow, 'id' | 'createdAt' | 'updatedAt' | 'executionCount' | 'lastExecutedAt'>) => Promise<AutomationWorkflow | null>;
  updateWorkflow: (id: string, updates: Partial<AutomationWorkflow>) => Promise<AutomationWorkflow | null>;
  deleteWorkflow: (id: string) => Promise<void>;
  duplicateWorkflow: (id: string, newName: string) => Promise<AutomationWorkflow | null>;
  
  createExecution: (execution: Omit<AutomationExecution, 'id' | 'executedAt'>) => Promise<AutomationExecution | null>;
  updateExecution: (id: string, updates: Partial<AutomationExecution>) => Promise<AutomationExecution | null>;
  
  createTrigger: (trigger: Omit<AutomationTrigger, 'id' | 'createdAt' | 'updatedAt'>) => Promise<AutomationTrigger | null>;
  updateTrigger: (id: string, updates: Partial<AutomationTrigger>) => Promise<AutomationTrigger | null>;
  deleteTrigger: (id: string) => Promise<void>;
  
  // Workflow execution
  executeWorkflow: (workflowId: string, triggerData: Record<string, any>) => Promise<void>;
  testWorkflow: (workflow: AutomationWorkflow, testData: Record<string, any>) => Promise<AutomationExecution>;
  
  // UI Actions
  setSelectedWorkflow: (workflow: AutomationWorkflow | null) => void;
  openWorkflowBuilder: (workflow?: AutomationWorkflow) => void;
  closeWorkflowBuilder: () => void;
  openExecutionLog: (workflowId?: string) => void;
  closeExecutionLog: () => void;
  
  // Utility actions
  getWorkflowsByTrigger: (triggerType: TriggerType) => AutomationWorkflow[];
  getExecutionStats: (workflowId: string) => Promise<any>;
  clearError: () => void;
}

export const useAutomationStore = create<AutomationStoreState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    workflows: [],
    executions: [],
    triggers: [],
    selectedWorkflow: null,
    isWorkflowBuilderOpen: false,
    isExecutionLogOpen: false,
    
    loading: {
      workflows: false,
      executions: false,
      triggers: false,
      creating: false,
      updating: false,
      deleting: false,
      executing: false,
    },
    
    error: null,
    
    // Data loading actions
    loadWorkflows: async (projectId?: string, folderId?: string) => {
      try {
        set(state => ({
          loading: { ...state.loading, workflows: true },
          error: null
        }));
        
        const workflows = await automationWorkflowService.getWorkflows(projectId, folderId);
        
        set(state => ({
          workflows,
          loading: { ...state.loading, workflows: false }
        }));
      } catch (error) {
        console.error('Failed to load workflows:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to load workflows',
          loading: { ...state.loading, workflows: false }
        }));
      }
    },
    
    loadExecutions: async (workflowId?: string, limit = 100) => {
      try {
        set(state => ({
          loading: { ...state.loading, executions: true },
          error: null
        }));
        
        const executions = await automationExecutionService.getExecutions(workflowId, limit);
        
        set(state => ({
          executions,
          loading: { ...state.loading, executions: false }
        }));
      } catch (error) {
        console.error('Failed to load executions:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to load executions',
          loading: { ...state.loading, executions: false }
        }));
      }
    },
    
    loadTriggers: async (workflowId?: string) => {
      try {
        set(state => ({
          loading: { ...state.loading, triggers: true },
          error: null
        }));
        
        const triggers = await automationTriggerService.getTriggers(workflowId);
        
        set(state => ({
          triggers,
          loading: { ...state.loading, triggers: false }
        }));
      } catch (error) {
        console.error('Failed to load triggers:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to load triggers',
          loading: { ...state.loading, triggers: false }
        }));
      }
    },
    
    // Workflow CRUD actions
    createWorkflow: async (workflow) => {
      try {
        set(state => ({
          loading: { ...state.loading, creating: true },
          error: null
        }));
        
        const newWorkflow = await automationWorkflowService.createWorkflow(workflow);
        
        if (newWorkflow) {
          set(state => ({
            workflows: [newWorkflow, ...state.workflows],
            loading: { ...state.loading, creating: false }
          }));
        }
        
        return newWorkflow;
      } catch (error) {
        console.error('Failed to create workflow:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to create workflow',
          loading: { ...state.loading, creating: false }
        }));
        return null;
      }
    },
    
    updateWorkflow: async (id, updates) => {
      try {
        set(state => ({
          loading: { ...state.loading, updating: true },
          error: null
        }));
        
        const updatedWorkflow = await automationWorkflowService.updateWorkflow(id, updates);
        
        if (updatedWorkflow) {
          set(state => ({
            workflows: state.workflows.map(w => w.id === id ? updatedWorkflow : w),
            selectedWorkflow: state.selectedWorkflow?.id === id ? updatedWorkflow : state.selectedWorkflow,
            loading: { ...state.loading, updating: false }
          }));
        }
        
        return updatedWorkflow;
      } catch (error) {
        console.error('Failed to update workflow:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to update workflow',
          loading: { ...state.loading, updating: false }
        }));
        return null;
      }
    },
    
    deleteWorkflow: async (id) => {
      try {
        set(state => ({
          loading: { ...state.loading, deleting: true },
          error: null
        }));
        
        await automationWorkflowService.deleteWorkflow(id);
        
        set(state => ({
          workflows: state.workflows.filter(w => w.id !== id),
          selectedWorkflow: state.selectedWorkflow?.id === id ? null : state.selectedWorkflow,
          loading: { ...state.loading, deleting: false }
        }));
      } catch (error) {
        console.error('Failed to delete workflow:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to delete workflow',
          loading: { ...state.loading, deleting: false }
        }));
      }
    },
    
    duplicateWorkflow: async (id, newName) => {
      try {
        set(state => ({
          loading: { ...state.loading, creating: true },
          error: null
        }));
        
        const duplicatedWorkflow = await automationWorkflowService.duplicateWorkflow(id, newName);
        
        if (duplicatedWorkflow) {
          set(state => ({
            workflows: [duplicatedWorkflow, ...state.workflows],
            loading: { ...state.loading, creating: false }
          }));
        }
        
        return duplicatedWorkflow;
      } catch (error) {
        console.error('Failed to duplicate workflow:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to duplicate workflow',
          loading: { ...state.loading, creating: false }
        }));
        return null;
      }
    },
    
    // Execution actions
    createExecution: async (execution) => {
      try {
        const newExecution = await automationExecutionService.createExecution(execution);
        
        if (newExecution) {
          set(state => ({
            executions: [newExecution, ...state.executions]
          }));
        }
        
        return newExecution;
      } catch (error) {
        console.error('Failed to create execution:', error);
        return null;
      }
    },
    
    updateExecution: async (id, updates) => {
      try {
        const updatedExecution = await automationExecutionService.updateExecution(id, updates);
        
        if (updatedExecution) {
          set(state => ({
            executions: state.executions.map(e => e.id === id ? updatedExecution : e)
          }));
        }
        
        return updatedExecution;
      } catch (error) {
        console.error('Failed to update execution:', error);
        return null;
      }
    },
    
    // Trigger actions
    createTrigger: async (trigger) => {
      try {
        const newTrigger = await automationTriggerService.createTrigger(trigger);
        
        if (newTrigger) {
          set(state => ({
            triggers: [newTrigger, ...state.triggers]
          }));
        }
        
        return newTrigger;
      } catch (error) {
        console.error('Failed to create trigger:', error);
        return null;
      }
    },
    
    updateTrigger: async (id, updates) => {
      try {
        const updatedTrigger = await automationTriggerService.updateTrigger(id, updates);
        
        if (updatedTrigger) {
          set(state => ({
            triggers: state.triggers.map(t => t.id === id ? updatedTrigger : t)
          }));
        }
        
        return updatedTrigger;
      } catch (error) {
        console.error('Failed to update trigger:', error);
        return null;
      }
    },
    
    deleteTrigger: async (id) => {
      try {
        await automationTriggerService.deleteTrigger(id);
        
        set(state => ({
          triggers: state.triggers.filter(t => t.id !== id)
        }));
      } catch (error) {
        console.error('Failed to delete trigger:', error);
      }
    },
    
    // Workflow execution (placeholder - will be implemented in automation engine)
    executeWorkflow: async (workflowId, triggerData) => {
      try {
        set(state => ({
          loading: { ...state.loading, executing: true },
          error: null
        }));
        
        // This will be implemented in the automation engine
        console.log('Executing workflow:', workflowId, 'with data:', triggerData);
        
        set(state => ({
          loading: { ...state.loading, executing: false }
        }));
      } catch (error) {
        console.error('Failed to execute workflow:', error);
        set(state => ({
          error: error instanceof Error ? error.message : 'Failed to execute workflow',
          loading: { ...state.loading, executing: false }
        }));
      }
    },
    
    testWorkflow: async (workflow, testData) => {
      // This will be implemented in the automation engine
      console.log('Testing workflow:', workflow.name, 'with data:', testData);
      
      // Return a mock execution for now
      return {
        id: 'test-execution',
        workflowId: workflow.id,
        triggerData: testData,
        actionResults: [],
        status: 'success' as const,
        executedAt: new Date().toISOString(),
      };
    },
    
    // UI Actions
    setSelectedWorkflow: (workflow) => {
      set({ selectedWorkflow: workflow });
    },
    
    openWorkflowBuilder: (workflow) => {
      set({ 
        isWorkflowBuilderOpen: true,
        selectedWorkflow: workflow || null
      });
    },
    
    closeWorkflowBuilder: () => {
      set({ 
        isWorkflowBuilderOpen: false,
        selectedWorkflow: null
      });
    },
    
    openExecutionLog: (workflowId) => {
      set({ isExecutionLogOpen: true });
      if (workflowId) {
        get().loadExecutions(workflowId);
      }
    },
    
    closeExecutionLog: () => {
      set({ isExecutionLogOpen: false });
    },
    
    // Utility actions
    getWorkflowsByTrigger: (triggerType) => {
      return get().workflows.filter(w => w.triggerConfig.type === triggerType && w.isActive);
    },
    
    getExecutionStats: async (workflowId) => {
      try {
        return await automationExecutionService.getExecutionStats(workflowId);
      } catch (error) {
        console.error('Failed to get execution stats:', error);
        return null;
      }
    },
    
    clearError: () => {
      set({ error: null });
    },
  }))
);
