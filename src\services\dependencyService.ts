import { supabase, handleSupabaseError, getCurrentUser } from '../lib/supabase';
import { Database } from '../types/database';
import {
  TaskDependency,
  TaskDependencyInput,
  TaskDependencyUpdate,
  DependencyValidationResult,
  CircularD<PERSON>endency<PERSON>heck,
  DependencyServiceResponse
} from '../types/dependencies';

type Tables = Database['public']['Tables'];

// Core dependency service for managing task dependencies
export class DependencyService {
  
  // Get all dependencies for a specific task
  static async getTaskDependencies(taskId: string): Promise<DependencyServiceResponse<TaskDependency[]>> {
    try {
      const { data, error } = await supabase
        .from('task_dependencies')
        .select('*')
        .or(`predecessor_task_id.eq.${taskId},successor_task_id.eq.${taskId}`)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const dependencies = data?.map(this.transformSupabaseDependency) || [];
      return { success: true, data: dependencies };
    } catch (error) {
      console.error('Failed to get task dependencies:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Get dependencies where task is a predecessor (tasks that depend on this task)
  static async getTaskDependents(taskId: string): Promise<DependencyServiceResponse<TaskDependency[]>> {
    try {
      const { data, error } = await supabase
        .from('task_dependencies')
        .select('*')
        .eq('predecessor_task_id', taskId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const dependents = data?.map(this.transformSupabaseDependency) || [];
      return { success: true, data: dependents };
    } catch (error) {
      console.error('Failed to get task dependents:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Get dependencies where task is a successor (tasks this task depends on)
  static async getTaskPredecessors(taskId: string): Promise<DependencyServiceResponse<TaskDependency[]>> {
    try {
      const { data, error } = await supabase
        .from('task_dependencies')
        .select('*')
        .eq('successor_task_id', taskId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const predecessors = data?.map(this.transformSupabaseDependency) || [];
      return { success: true, data: predecessors };
    } catch (error) {
      console.error('Failed to get task predecessors:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Create a new dependency
  static async createDependency(dependency: TaskDependencyInput): Promise<DependencyServiceResponse<TaskDependency>> {
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        return { success: false, error: 'User not authenticated' };
      }

      // Validate the dependency first
      const validation = await this.validateDependency(dependency);
      if (!validation.isValid) {
        return { 
          success: false, 
          error: validation.errors.join(', '),
          warnings: validation.warnings 
        };
      }

      const { data, error } = await supabase
        .from('task_dependencies')
        .insert({
          predecessor_task_id: dependency.predecessorTaskId,
          successor_task_id: dependency.successorTaskId,
          dependency_type: dependency.dependencyType || 'finish_to_start',
          lag_days: dependency.lagDays || 0,
          created_by: currentUser.id
        })
        .select()
        .single();

      if (error) throw error;

      const newDependency = this.transformSupabaseDependency(data);
      return { success: true, data: newDependency };
    } catch (error) {
      console.error('Failed to create dependency:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Update an existing dependency
  static async updateDependency(id: string, updates: TaskDependencyUpdate): Promise<DependencyServiceResponse<TaskDependency>> {
    try {
      const { data, error } = await supabase
        .from('task_dependencies')
        .update({
          dependency_type: updates.dependencyType,
          lag_days: updates.lagDays
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      const updatedDependency = this.transformSupabaseDependency(data);
      return { success: true, data: updatedDependency };
    } catch (error) {
      console.error('Failed to update dependency:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Delete a dependency
  static async deleteDependency(id: string): Promise<DependencyServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from('task_dependencies')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Failed to delete dependency:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }

  // Validate a dependency before creation
  static async validateDependency(dependency: TaskDependencyInput): Promise<DependencyValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if tasks exist
    const { data: predecessorTask } = await supabase
      .from('tasks')
      .select('id, title')
      .eq('id', dependency.predecessorTaskId)
      .single();

    const { data: successorTask } = await supabase
      .from('tasks')
      .select('id, title')
      .eq('id', dependency.successorTaskId)
      .single();

    if (!predecessorTask) {
      errors.push('Predecessor task not found');
    }

    if (!successorTask) {
      errors.push('Successor task not found');
    }

    // Check for self-dependency
    if (dependency.predecessorTaskId === dependency.successorTaskId) {
      errors.push('A task cannot depend on itself');
    }

    // Check for existing dependency
    const { data: existingDependency } = await supabase
      .from('task_dependencies')
      .select('id')
      .eq('predecessor_task_id', dependency.predecessorTaskId)
      .eq('successor_task_id', dependency.successorTaskId)
      .single();

    if (existingDependency) {
      errors.push('Dependency already exists between these tasks');
    }

    // Check for circular dependencies
    const circularCheck = await this.checkCircularDependency(
      dependency.predecessorTaskId,
      dependency.successorTaskId
    );

    if (circularCheck.hasCycle) {
      errors.push(`Creating this dependency would create a circular dependency: ${circularCheck.cyclePath?.join(' → ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      wouldCreateCycle: circularCheck.hasCycle
    };
  }

  // Check for circular dependencies using DFS
  static async checkCircularDependency(
    predecessorId: string,
    successorId: string
  ): Promise<CircularDependencyCheck> {
    try {
      // Get all dependencies to build the graph
      const { data: allDependencies } = await supabase
        .from('task_dependencies')
        .select('predecessor_task_id, successor_task_id');

      if (!allDependencies) {
        return { hasCycle: false };
      }

      // Build adjacency list
      const graph = new Map<string, string[]>();
      
      // Add existing dependencies
      allDependencies.forEach(dep => {
        if (!graph.has(dep.predecessor_task_id)) {
          graph.set(dep.predecessor_task_id, []);
        }
        graph.get(dep.predecessor_task_id)!.push(dep.successor_task_id);
      });

      // Add the proposed dependency
      if (!graph.has(predecessorId)) {
        graph.set(predecessorId, []);
      }
      graph.get(predecessorId)!.push(successorId);

      // DFS to detect cycle
      const visited = new Set<string>();
      const recursionStack = new Set<string>();
      const path: string[] = [];

      const dfs = (node: string): boolean => {
        visited.add(node);
        recursionStack.add(node);
        path.push(node);

        const neighbors = graph.get(node) || [];
        for (const neighbor of neighbors) {
          if (!visited.has(neighbor)) {
            if (dfs(neighbor)) return true;
          } else if (recursionStack.has(neighbor)) {
            // Found cycle
            const cycleStart = path.indexOf(neighbor);
            const cyclePath = path.slice(cycleStart).concat(neighbor);
            return true;
          }
        }

        recursionStack.delete(node);
        path.pop();
        return false;
      };

      // Check from the successor to see if it can reach the predecessor
      if (dfs(successorId)) {
        return { hasCycle: true, cyclePath: path };
      }

      return { hasCycle: false };
    } catch (error) {
      console.error('Error checking circular dependency:', error);
      return { hasCycle: false };
    }
  }

  // Transform Supabase data to app format
  private static transformSupabaseDependency(supabaseData: any): TaskDependency {
    return {
      id: supabaseData.id,
      predecessorTaskId: supabaseData.predecessor_task_id,
      successorTaskId: supabaseData.successor_task_id,
      dependencyType: supabaseData.dependency_type,
      lagDays: supabaseData.lag_days,
      createdAt: supabaseData.created_at,
      updatedAt: supabaseData.updated_at,
      createdBy: supabaseData.created_by
    };
  }

  // Get all dependencies for multiple tasks (batch operation)
  static async getBatchDependencies(taskIds: string[]): Promise<DependencyServiceResponse<TaskDependency[]>> {
    try {
      const { data, error } = await supabase
        .from('task_dependencies')
        .select('*')
        .or(taskIds.map(id => `predecessor_task_id.eq.${id},successor_task_id.eq.${id}`).join(','))
        .order('created_at', { ascending: true });

      if (error) throw error;

      const dependencies = data?.map(this.transformSupabaseDependency) || [];
      return { success: true, data: dependencies };
    } catch (error) {
      console.error('Failed to get batch dependencies:', error);
      return { success: false, error: handleSupabaseError(error) };
    }
  }
}

export const dependencyService = DependencyService;
