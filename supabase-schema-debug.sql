-- Debug version of the schema - run this step by step to identify issues

-- Step 1: Clean up and create basic tables without triggers first
-- Run this first to see if basic table creation works

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Clean up existing objects first
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.handle_new_user_simple();
DROP FUNCTION IF EXISTS public.insert_default_data();
DROP FUNCTION IF EXISTS public.insert_default_data_manual();

-- Create custom types (only if they don't exist)
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('admin', 'user');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE task_status AS ENUM ('todo', 'in-progress', 'review', 'done');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- User profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  avatar_url TEXT,
  role user_role DEFAULT 'user',
  group_id UUID,
  skillset_ids TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User groups table
CREATE TABLE IF NOT EXISTS user_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Kanban columns table
CREATE TABLE IF NOT EXISTS kanban_columns (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Enable Row Level Security on tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_columns ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies for user_profiles
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);

-- Basic RLS policies for user_groups
DROP POLICY IF EXISTS "Users can view all user groups" ON user_groups;
CREATE POLICY "Users can view all user groups" ON user_groups FOR SELECT USING (true);

DROP POLICY IF EXISTS "Authenticated users can create user groups" ON user_groups;
CREATE POLICY "Authenticated users can create user groups" ON user_groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Basic RLS policies for kanban_columns
DROP POLICY IF EXISTS "Users can view all kanban columns" ON kanban_columns;
CREATE POLICY "Users can view all kanban columns" ON kanban_columns FOR SELECT USING (true);

DROP POLICY IF EXISTS "Authenticated users can create kanban columns" ON kanban_columns;
CREATE POLICY "Authenticated users can create kanban columns" ON kanban_columns FOR INSERT WITH CHECK (auth.role() = 'authenticated');
