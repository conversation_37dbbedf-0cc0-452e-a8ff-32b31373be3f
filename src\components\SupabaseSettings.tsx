import React, { useRef, useState } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { useAuth } from '../hooks/useAuth';
import { Download, Upload, AlertCircle, Loader2, LogOut, User } from 'lucide-react';

export default function SupabaseSettingsPanel() {
  const {
    tasks,
    projects,
    userGroups,
    users,
    columns,
    folders,
    skillsetGroups,
    userCapacities,
    taskEfforts,
    addTask,
    addProject,
    addUserGroup,
    addColumn,
    addFolder,
    addUser,
    addSkillsetGroup,
    addUserCapacity,
    addTaskEffort,
    syncData
  } = useSupabaseStore();

  const { user, profile, signOut, isAdmin } = useAuth();

  const [isImporting, setIsImporting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const projectsFileInputRef = useRef<HTMLInputElement>(null);
  const tasksFileInputRef = useRef<HTMLInputElement>(null);

  const handleDownload = (type: 'full' | 'projects' | 'tasks') => {
    let data;
    let filename;

    switch (type) {
      case 'full':
        data = {
          tasks: tasks.map(task => ({
            ...task,
            comments: task.comments || [],
            history: task.history || [],
            subtasks: task.subtasks || [],
            effort: task.effort || undefined
          })),
          projects,
          userGroups,
          users,
          columns,
          folders,
          skillsetGroups,
          userCapacities,
          taskEfforts,
          version: '3.0',
          timestamp: new Date().toISOString(),
          exportedBy: {
            userId: user?.id,
            userEmail: user?.email,
            userName: profile?.name
          },
          features: [
            'resource-management',
            'capacity-planning',
            'skillset-groups',
            'effort-estimation',
            'task-filtering',
            'multi-user-auth',
            'real-time-collaboration'
          ]
        };
        filename = `taskflow-backup-${new Date().toISOString().split('T')[0]}.json`;
        break;
      case 'projects':
        data = {
          projects,
          folders,
          version: '2.0',
          timestamp: new Date().toISOString(),
          exportedBy: {
            userId: user?.id,
            userEmail: user?.email,
            userName: profile?.name
          }
        };
        filename = `taskflow-projects-${new Date().toISOString().split('T')[0]}.json`;
        break;
      case 'tasks':
        const projectMap = new Map(projects.map(p => [p.id, p]));
        const folderMap = new Map(folders.map(f => [f.id, f]));
        
        const tasksWithRelations = tasks.map(task => ({
          ...task,
          comments: task.comments || [],
          history: task.history || [],
          subtasks: task.subtasks || [],
          projectDetails: task.projectId ? {
            project: projectMap.get(task.projectId),
            folder: projectMap.get(task.projectId)?.folderId ? 
              folderMap.get(projectMap.get(task.projectId)?.folderId || '') : undefined
          } : undefined
        }));

        data = {
          tasks: tasksWithRelations,
          version: '2.0',
          timestamp: new Date().toISOString(),
          exportedBy: {
            userId: user?.id,
            userEmail: user?.email,
            userName: profile?.name
          }
        };
        filename = `taskflow-tasks-${new Date().toISOString().split('T')[0]}.json`;
        break;
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Helper function to generate UUID from string ID for backward compatibility
  const generateUUIDFromString = (str: string): string => {
    // Simple hash function to generate consistent UUIDs from string IDs
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Convert hash to UUID format (not cryptographically secure, but consistent)
    const hex = Math.abs(hash).toString(16).padStart(8, '0');
    return `${hex.slice(0, 8)}-${hex.slice(0, 4)}-4${hex.slice(1, 4)}-8${hex.slice(0, 3)}-${hex.slice(0, 12)}`.slice(0, 36);
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>, type: 'full' | 'projects' | 'tasks') => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);

    try {
      const text = await file.text();
      const backup = JSON.parse(text);
      
      // Validate user permissions for import
      if (!isAdmin && type === 'full') {
        throw new Error('Only administrators can perform full imports');
      }

      // Check if this is an old format backup (with string IDs)
      if (backup.tasks && backup.tasks.length > 0) {
        const firstTask = backup.tasks[0];
        if (firstTask.id && typeof firstTask.id === 'string' && firstTask.id.length < 20) {
          throw new Error('This backup file uses an old format that is not compatible with the new Supabase version. Please export your data again from the new version after manually recreating your tasks.');
        }
      }
      
      switch (type) {
        case 'full':
          // Validate required arrays
          const requiredArrays = ['tasks', 'projects', 'userGroups', 'users', 'columns', 'folders'];
          for (const arrayName of requiredArrays) {
            if (!Array.isArray(backup[arrayName])) {
              throw new Error(`Invalid backup file format: missing or invalid ${arrayName}`);
            }
          }

          if (!backup.version || !backup.timestamp) {
            throw new Error('Invalid backup file format: missing version or timestamp');
          }

          // Import skillset groups first (if available)
          if (Array.isArray(backup.skillsetGroups)) {
            for (const skillset of backup.skillsetGroups) {
              await new Promise(resolve => setTimeout(resolve, 0));
              await addSkillsetGroup({ 
                name: skillset.name,
                description: skillset.description,
                color: skillset.color
              });
            }
          }

          for (const folder of backup.folders) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addFolder({ 
              name: folder.name,
              parentId: folder.parentId
            });
          }

          for (const project of backup.projects) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addProject({ 
              name: project.name,
              description: project.description,
              color: project.color,
              startDate: project.startDate,
              endDate: project.endDate,
              folderId: project.folderId,
              effort: project.effort
            });
          }

          for (const group of backup.userGroups) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addUserGroup({ 
              name: group.name,
              color: group.color
            });
          }

          for (const column of backup.columns) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addColumn({ 
              title: column.title,
              color: column.color
            });
          }

          // Import user capacities (if available)
          if (Array.isArray(backup.userCapacities)) {
            for (const capacity of backup.userCapacities) {
              await new Promise(resolve => setTimeout(resolve, 0));
              await addUserCapacity(capacity);
            }
          }

          // Import task efforts (if available)
          if (Array.isArray(backup.taskEfforts)) {
            for (const effort of backup.taskEfforts) {
              await new Promise(resolve => setTimeout(resolve, 0));
              await addTaskEffort(effort);
            }
          }

          for (const task of backup.tasks) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addTask({
              title: task.title,
              description: task.description,
              status: task.status,
              priority: task.priority,
              assignedUserId: task.assignedUserId,
              assignedUsers: task.assignedUsers,
              assignedGroups: task.assignedGroups,
              ownerId: task.ownerId,
              dueDate: task.dueDate,
              startDate: task.startDate,
              tags: task.tags,
              projectId: task.projectId,
              folderId: task.folderId,
              effort: task.effort
            });
          }
          break;

        case 'projects':
          if (!backup.version || !backup.timestamp || 
              !Array.isArray(backup.projects) ||
              !Array.isArray(backup.folders)) {
            throw new Error('Invalid projects backup file format');
          }

          for (const folder of backup.folders) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addFolder({ 
              name: folder.name,
              parentId: folder.parentId
            });
          }

          for (const project of backup.projects) {
            await new Promise(resolve => setTimeout(resolve, 0));
            await addProject({ 
              name: project.name,
              description: project.description,
              color: project.color,
              startDate: project.startDate,
              endDate: project.endDate,
              folderId: project.folderId,
              effort: project.effort
            });
          }
          break;

        case 'tasks':
          if (!backup.version || !backup.timestamp || 
              !Array.isArray(backup.tasks)) {
            throw new Error('Invalid tasks backup file format');
          }

          for (const taskData of backup.tasks) {
            const { projectDetails, ...task } = taskData;
            
            if (projectDetails?.project) {
              const projectExists = projects.some(p => p.id === projectDetails.project.id);
              
              if (!projectExists) {
                if (projectDetails.folder && !folders.some(f => f.id === projectDetails.folder.id)) {
                  await new Promise(resolve => setTimeout(resolve, 0));
                  await addFolder({
                    name: projectDetails.folder.name,
                    parentId: projectDetails.folder.parentId
                  });
                }
                
                await new Promise(resolve => setTimeout(resolve, 0));
                await addProject({
                  name: projectDetails.project.name,
                  description: projectDetails.project.description,
                  color: projectDetails.project.color,
                  startDate: projectDetails.project.startDate,
                  endDate: projectDetails.project.endDate,
                  folderId: projectDetails.project.folderId,
                  effort: projectDetails.project.effort
                });
              }
            }

            await new Promise(resolve => setTimeout(resolve, 0));
            await addTask({
              title: task.title,
              description: task.description,
              status: task.status,
              priority: task.priority,
              assignedUserId: task.assignedUserId,
              assignedUsers: task.assignedUsers,
              assignedGroups: task.assignedGroups,
              ownerId: task.ownerId,
              dueDate: task.dueDate,
              startDate: task.startDate,
              tags: task.tags,
              projectId: task.projectId,
              folderId: task.folderId,
              effort: task.effort
            });
          }
          break;
      }

      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} backup imported successfully!`);
    } catch (error) {
      console.error('Error importing backup:', error);
      alert(`Error importing backup: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) fileInputRef.current.value = '';
      if (projectsFileInputRef.current) projectsFileInputRef.current.value = '';
      if (tasksFileInputRef.current) tasksFileInputRef.current.value = '';
    }
  };

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      await syncData();
      alert('Data synchronized successfully!');
    } catch (error) {
      console.error('Error syncing data:', error);
      alert('Error syncing data. Please try again.');
    } finally {
      setIsSyncing(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      alert('Error signing out. Please try again.');
    }
  };

  const BackupSection = ({
    title,
    description,
    type,
    inputRef,
    adminOnly = false
  }: {
    title: string;
    description: string;
    type: 'full' | 'projects' | 'tasks';
    inputRef: React.RefObject<HTMLInputElement>;
    adminOnly?: boolean;
  }) => {
    const canImport = !adminOnly || isAdmin;

    return (
      <div className="border-t border-gray-700 pt-6 first:border-t-0 first:pt-0">
        <h4 className="text-md font-medium mb-2 text-gray-300">{title}</h4>
        <p className="text-sm text-gray-400 mb-3">{description}</p>
        {adminOnly && !isAdmin && (
          <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 text-sm flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            Administrator privileges required for import
          </div>
        )}
        <div className="flex gap-3">
          <button
            onClick={() => handleDownload(type)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            Download
          </button>
          <button
            onClick={() => inputRef.current?.click()}
            disabled={isImporting || !canImport}
            className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isImporting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Upload className="w-4 h-4" />
            )}
            {isImporting ? 'Importing...' : 'Import'}
          </button>
          <input
            type="file"
            ref={inputRef}
            onChange={(e) => handleImport(e, type)}
            accept=".json"
            className="hidden"
            disabled={isImporting || !canImport}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-6 text-white">Settings</h2>

      {/* User Profile Section */}
      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4 text-white flex items-center gap-2">
          <User className="w-5 h-5" />
          User Profile
        </h3>
        <div className="space-y-3">
          <div>
            <span className="text-sm text-gray-400">Name:</span>
            <span className="ml-2 text-white">{profile?.name}</span>
          </div>
          <div>
            <span className="text-sm text-gray-400">Email:</span>
            <span className="ml-2 text-white">{profile?.email}</span>
          </div>
          <div>
            <span className="text-sm text-gray-400">Role:</span>
            <span className={`ml-2 px-2 py-1 rounded text-xs ${
              profile?.role === 'admin'
                ? 'bg-red-100 text-red-800'
                : 'bg-blue-100 text-blue-800'
            }`}>
              {profile?.role === 'admin' ? 'Administrator' : 'User'}
            </span>
          </div>
          <button
            onClick={handleSignOut}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <LogOut className="w-4 h-4" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Data Sync Section */}
      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4 text-white">Data Synchronization</h3>
        <p className="text-sm text-gray-400 mb-4">
          Manually sync your data with the server to ensure you have the latest updates.
        </p>
        <button
          onClick={handleSync}
          disabled={isSyncing}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSyncing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Download className="w-4 h-4" />
          )}
          {isSyncing ? 'Syncing...' : 'Sync Data'}
        </button>
      </div>

      {/* Backup & Restore Section */}
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl">
        <h3 className="text-lg font-semibold mb-4 text-white">Backup & Restore</h3>
        <p className="text-sm text-gray-400 mb-6">
          Export your data for backup purposes or import data from previous exports.
          {!isAdmin && ' Note: Full system imports require administrator privileges.'}
        </p>

        <div className="space-y-6">
          <BackupSection
            title="Full Backup"
            description="Complete backup including all tasks, projects, users, and settings. Recommended for full system backup."
            type="full"
            inputRef={fileInputRef}
            adminOnly={true}
          />

          <BackupSection
            title="Projects Backup"
            description="Export/import only projects and folders. Useful for sharing project structures."
            type="projects"
            inputRef={projectsFileInputRef}
          />

          <BackupSection
            title="Tasks Backup"
            description="Export/import tasks with their associated project information. Good for task migration."
            type="tasks"
            inputRef={tasksFileInputRef}
          />
        </div>

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900">Import Notes</h4>
              <p className="text-sm text-blue-700 mt-1">
                Imported data will be added to your existing data. Duplicate items may be created.
                Consider backing up your current data before importing.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
