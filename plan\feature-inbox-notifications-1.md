---
goal: Implement Inbox Functionality with @username Mentions and Notifications
version: 1.0
date_created: 2025-01-17
last_updated: 2025-01-17
owner: Development Team
tags: [feature, notifications, mentions, real-time, ui]
---

# Introduction

This plan implements a comprehensive inbox functionality that allows users to receive notifications when mentioned in comments using @username syntax. The feature includes a new Inbox section in the left sidebar, real-time notifications, unread count indicators, and the ability to mark notifications as read. The implementation follows modern UI patterns and integrates seamlessly with the existing Supabase-based architecture.

## 1. Requirements & Constraints

- **REQ-001**: Detect @username mentions in comment content when users post comments
- **REQ-002**: Create notifications for mentioned users linking to the specific task and comment
- **REQ-003**: Add Inbox section to left sidebar with unread notification count
- **REQ-004**: Implement real-time notification delivery using Supabase subscriptions
- **REQ-005**: Allow users to mark notifications as read/unread
- **REQ-006**: Make task references clickable to navigate directly to the task
- **REQ-007**: Display notification content including task title and comment excerpt
- **SEC-001**: Ensure users can only see notifications intended for them via RLS policies
- **SEC-002**: Validate mentioned usernames exist before creating notifications
- **CON-001**: Must integrate with existing Supabase database and authentication system
- **CON-002**: Must follow existing UI patterns and component structure
- **CON-003**: Must not impact existing comment functionality performance
- **GUD-001**: Use TypeScript for all new code with proper type definitions
- **GUD-002**: Follow existing error handling and loading state patterns
- **PAT-001**: Use Zustand store pattern for state management
- **PAT-002**: Follow existing service layer pattern for database operations

## 2. Implementation Steps

### Phase 1: Database Schema and Backend Services
1. Create notifications table with proper RLS policies
2. Implement notification service functions
3. Add mention detection utility functions
4. Create database triggers for automatic notification creation

### Phase 2: Core Notification Logic
1. Modify comment submission to detect @username mentions
2. Implement notification creation when mentions are detected
3. Add real-time subscription for notifications
4. Integrate notification state into Zustand store

### Phase 3: UI Components and Sidebar Integration
1. Create Inbox component with notification list
2. Add Inbox menu item to sidebar with unread count badge
3. Implement notification item component with task navigation
4. Add mark as read/unread functionality

### Phase 4: Real-time Updates and Polish
1. Set up real-time notification subscriptions
2. Add notification sound/visual indicators (optional)
3. Implement notification cleanup and archiving
4. Add comprehensive error handling and loading states

## 3. Alternatives

- **ALT-001**: Use browser push notifications instead of in-app only - rejected due to complexity and permission requirements
- **ALT-002**: Store mentions as separate table instead of parsing content - rejected to avoid data duplication
- **ALT-003**: Use email notifications instead of real-time - rejected as not meeting real-time collaboration requirements
- **ALT-004**: Implement as modal overlay instead of sidebar section - rejected to maintain consistency with existing navigation

## 4. Dependencies

- **DEP-001**: Existing Supabase database and authentication system
- **DEP-002**: Current Zustand store architecture and patterns
- **DEP-003**: Existing comment system and TaskComments component
- **DEP-004**: Lucide React icons for UI elements
- **DEP-005**: Existing real-time subscription infrastructure

## 5. Files

- **FILE-001**: `supabase-schema.sql` - Add notifications table and RLS policies
- **FILE-002**: `src/types/index.ts` - Add notification type definitions
- **FILE-003**: `src/types/database.ts` - Add database type definitions for notifications
- **FILE-004**: `src/services/supabaseService.ts` - Add notification service functions
- **FILE-005**: `src/utils/mentionUtils.ts` - Utility functions for detecting and parsing mentions
- **FILE-006**: `src/store/useSupabaseStore.ts` - Add notification state and actions
- **FILE-007**: `src/components/Inbox.tsx` - Main inbox component
- **FILE-008**: `src/components/NotificationItem.tsx` - Individual notification component
- **FILE-009**: `src/components/SupabaseSidebar.tsx` - Add inbox menu item and integration
- **FILE-010**: `src/components/TaskComments.tsx` - Modify to detect mentions and create notifications

## 6. Testing

- **TEST-001**: Unit tests for mention detection utility functions
- **TEST-002**: Integration tests for notification creation when comments with mentions are posted
- **TEST-003**: Real-time notification delivery tests with multiple users
- **TEST-004**: UI tests for inbox component rendering and interaction
- **TEST-005**: Navigation tests for clicking on task links in notifications
- **TEST-006**: Mark as read/unread functionality tests
- **TEST-007**: RLS policy tests to ensure notification privacy

## 7. Risks & Assumptions

- **RISK-001**: High volume of mentions could impact database performance - mitigate with proper indexing
- **RISK-002**: Real-time subscriptions may fail requiring polling fallback - use existing fallback patterns
- **RISK-003**: Username parsing may have edge cases with special characters - implement robust regex patterns
- **ASSUMPTION-001**: Users will use standard @username format for mentions
- **ASSUMPTION-002**: Notification volume will be manageable for typical team sizes
- **ASSUMPTION-003**: Existing real-time infrastructure can handle additional notification subscriptions

## 8. Related Specifications / Further Reading

- [Multi-user Collaboration Guide](../docs/multi-user-collaboration-guide.md)
- [Database Schema Documentation](../docs/database-schema.md)
- [Supabase Real-time Documentation](https://supabase.com/docs/guides/realtime)
- [Existing Comment System Implementation](../src/components/TaskComments.tsx)
