import { addDays, parseISO, isValid, format, differenceInDays, isWeekend } from 'date-fns';
import { supabase } from '../lib/supabase';
import {
  TaskDependency,
  DateCalculationResult,
  DatePropagationConfig,
  DependencyServiceResponse,
  DependencyType
} from '../types/dependencies';
import { Task } from '../types';
import { dependencyService } from './dependencyService';

// Service for calculating task dates based on dependencies
export class DateCalculationService {
  
  private static defaultConfig: DatePropagationConfig = {
    respectManualDates: true,
    maxPropagationDepth: 50,
    workingDaysOnly: false,
    workingDays: [1, 2, 3, 4, 5], // Monday to Friday
    holidays: []
  };

  // Calculate dates for a task based on its dependencies
  static async calculateTaskDates(
    taskId: string,
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<DateCalculationResult>> {
    try {
      const fullConfig = { ...this.defaultConfig, ...config };
      
      // Get task details
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError || !task) {
        return { success: false, error: 'Task not found' };
      }

      // Get task dependencies (predecessors)
      const predecessorsResult = await dependencyService.getTaskPredecessors(taskId);
      if (!predecessorsResult.success) {
        return { success: false, error: predecessorsResult.error };
      }

      const dependencies = predecessorsResult.data || [];
      
      if (dependencies.length === 0) {
        // No dependencies, return current dates
        return {
          success: true,
          data: {
            taskId,
            originalStartDate: task.start_date,
            originalDueDate: task.due_date,
            calculatedStartDate: task.start_date,
            calculatedDueDate: task.due_date,
            hasConflict: false,
            dependencyChain: []
          }
        };
      }

      // Get predecessor tasks
      const predecessorIds = dependencies.map(dep => dep.predecessorTaskId);
      const { data: predecessorTasks, error: predError } = await supabase
        .from('tasks')
        .select('*')
        .in('id', predecessorIds);

      if (predError) {
        return { success: false, error: 'Failed to fetch predecessor tasks' };
      }

      // Calculate the earliest start date based on dependencies
      let earliestStartDate: Date | null = null;
      const dependencyChain: string[] = [];

      for (const dependency of dependencies) {
        const predecessorTask = predecessorTasks?.find(t => t.id === dependency.predecessorTaskId);
        if (!predecessorTask) continue;

        dependencyChain.push(predecessorTask.title);

        const calculatedDate = this.calculateDateFromDependency(
          dependency,
          predecessorTask,
          fullConfig
        );

        if (calculatedDate && (!earliestStartDate || calculatedDate > earliestStartDate)) {
          earliestStartDate = calculatedDate;
        }
      }

      if (!earliestStartDate) {
        return {
          success: true,
          data: {
            taskId,
            originalStartDate: task.start_date,
            originalDueDate: task.due_date,
            calculatedStartDate: task.start_date,
            calculatedDueDate: task.due_date,
            hasConflict: false,
            dependencyChain
          }
        };
      }

      // Calculate duration if both start and due dates exist
      let calculatedDueDate: Date | null = null;
      if (task.start_date && task.due_date) {
        const originalStart = parseISO(task.start_date);
        const originalDue = parseISO(task.due_date);
        if (isValid(originalStart) && isValid(originalDue)) {
          const duration = differenceInDays(originalDue, originalStart);
          calculatedDueDate = this.addWorkingDays(earliestStartDate, duration, fullConfig);
        }
      }

      // Check for conflicts
      const hasConflict = this.checkDateConflict(
        task.start_date,
        task.due_date,
        earliestStartDate,
        calculatedDueDate,
        fullConfig
      );

      return {
        success: true,
        data: {
          taskId,
          originalStartDate: task.start_date,
          originalDueDate: task.due_date,
          calculatedStartDate: format(earliestStartDate, 'yyyy-MM-dd'),
          calculatedDueDate: calculatedDueDate ? format(calculatedDueDate, 'yyyy-MM-dd') : undefined,
          hasConflict,
          conflictReason: hasConflict ? 'Calculated dates conflict with manually set dates' : undefined,
          dependencyChain
        }
      };
    } catch (error) {
      console.error('Failed to calculate task dates:', error);
      return { success: false, error: 'Failed to calculate task dates' };
    }
  }

  // Calculate date from a specific dependency
  private static calculateDateFromDependency(
    dependency: TaskDependency,
    predecessorTask: any,
    config: DatePropagationConfig
  ): Date | null {
    const { dependencyType, lagDays } = dependency;
    
    let baseDate: Date | null = null;

    switch (dependencyType) {
      case 'finish_to_start':
        baseDate = predecessorTask.due_date ? parseISO(predecessorTask.due_date) : null;
        break;
      case 'start_to_start':
        baseDate = predecessorTask.start_date ? parseISO(predecessorTask.start_date) : null;
        break;
      case 'finish_to_finish':
        baseDate = predecessorTask.due_date ? parseISO(predecessorTask.due_date) : null;
        break;
      case 'start_to_finish':
        baseDate = predecessorTask.start_date ? parseISO(predecessorTask.start_date) : null;
        break;
    }

    if (!baseDate || !isValid(baseDate)) return null;

    // Add lag days
    const laggedDate = this.addWorkingDays(baseDate, lagDays, config);

    // For finish_to_start, add one day to start after the predecessor finishes
    if (dependencyType === 'finish_to_start') {
      return this.addWorkingDays(laggedDate, 1, config);
    }

    return laggedDate;
  }

  // Add working days considering weekends and holidays
  private static addWorkingDays(
    startDate: Date,
    days: number,
    config: DatePropagationConfig
  ): Date {
    if (!config.workingDaysOnly) {
      return addDays(startDate, days);
    }

    let currentDate = new Date(startDate);
    let remainingDays = days;

    while (remainingDays > 0) {
      currentDate = addDays(currentDate, 1);
      
      // Check if it's a working day
      const dayOfWeek = currentDate.getDay();
      const isWorkingDay = config.workingDays.includes(dayOfWeek);
      const isHoliday = config.holidays.includes(format(currentDate, 'yyyy-MM-dd'));

      if (isWorkingDay && !isHoliday) {
        remainingDays--;
      }
    }

    return currentDate;
  }

  // Check if calculated dates conflict with manual dates
  private static checkDateConflict(
    originalStartDate: string | null,
    originalDueDate: string | null,
    calculatedStartDate: Date,
    calculatedDueDate: Date | null,
    config: DatePropagationConfig
  ): boolean {
    if (!config.respectManualDates) return false;

    if (originalStartDate) {
      const originalStart = parseISO(originalStartDate);
      if (isValid(originalStart) && originalStart < calculatedStartDate) {
        return true;
      }
    }

    if (originalDueDate && calculatedDueDate) {
      const originalDue = parseISO(originalDueDate);
      if (isValid(originalDue) && originalDue < calculatedDueDate) {
        return true;
      }
    }

    return false;
  }

  // Propagate date changes through the dependency chain
  static async propagateDateChanges(
    changedTaskId: string,
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<DateCalculationResult[]>> {
    try {
      const fullConfig = { ...this.defaultConfig, ...config };
      const results: DateCalculationResult[] = [];
      const processedTasks = new Set<string>();
      const taskQueue = [changedTaskId];
      let depth = 0;

      while (taskQueue.length > 0 && depth < fullConfig.maxPropagationDepth) {
        const currentTaskId = taskQueue.shift()!;
        
        if (processedTasks.has(currentTaskId)) continue;
        processedTasks.add(currentTaskId);

        // Get tasks that depend on this task
        const dependentsResult = await dependencyService.getTaskDependents(currentTaskId);
        if (!dependentsResult.success) continue;

        const dependents = dependentsResult.data || [];

        for (const dependent of dependents) {
          const calculationResult = await this.calculateTaskDates(
            dependent.successorTaskId,
            fullConfig
          );

          if (calculationResult.success && calculationResult.data) {
            results.push(calculationResult.data);
            
            // Add to queue for further propagation
            if (!processedTasks.has(dependent.successorTaskId)) {
              taskQueue.push(dependent.successorTaskId);
            }
          }
        }

        depth++;
      }

      return { success: true, data: results };
    } catch (error) {
      console.error('Failed to propagate date changes:', error);
      return { success: false, error: 'Failed to propagate date changes' };
    }
  }

  // Update task dates in the database
  static async updateTaskDates(
    taskId: string,
    startDate?: string,
    dueDate?: string
  ): Promise<DependencyServiceResponse<void>> {
    try {
      const updates: any = {};
      if (startDate) updates.start_date = startDate;
      if (dueDate) updates.due_date = dueDate;

      const { error } = await supabase
        .from('tasks')
        .update(updates)
        .eq('id', taskId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Failed to update task dates:', error);
      return { success: false, error: 'Failed to update task dates' };
    }
  }

  // Batch update multiple task dates
  static async batchUpdateTaskDates(
    updates: Array<{ taskId: string; startDate?: string; dueDate?: string }>
  ): Promise<DependencyServiceResponse<void>> {
    try {
      const promises = updates.map(update =>
        this.updateTaskDates(update.taskId, update.startDate, update.dueDate)
      );

      const results = await Promise.all(promises);
      const failures = results.filter(result => !result.success);

      if (failures.length > 0) {
        return { 
          success: false, 
          error: `Failed to update ${failures.length} tasks`,
          warnings: failures.map(f => f.error).filter(Boolean) as string[]
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to batch update task dates:', error);
      return { success: false, error: 'Failed to batch update task dates' };
    }
  }
}

export const dateCalculationService = DateCalculationService;
