-- Add Row Level Security policies for all tables
-- Run this after creating all tables

-- User groups policies
CREATE POLICY "Users can view all user groups" ON user_groups FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create user groups" ON user_groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update user groups" ON user_groups FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete user groups" ON user_groups FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Folders policies
CREATE POLICY "Users can view all folders" ON folders FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create folders" ON folders FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update folders" ON folders FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete folders" ON folders FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Projects policies
CREATE POLICY "Users can view all projects" ON projects FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create projects" ON projects FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update projects" ON projects FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete projects" ON projects FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Kanban columns policies
CREATE POLICY "Users can view all kanban columns" ON kanban_columns FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create kanban columns" ON kanban_columns FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update kanban columns" ON kanban_columns FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete kanban columns" ON kanban_columns FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Skillset groups policies
CREATE POLICY "Users can view all skillset groups" ON skillset_groups FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create skillset groups" ON skillset_groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update skillset groups" ON skillset_groups FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete skillset groups" ON skillset_groups FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Tasks policies
CREATE POLICY "Users can view all tasks" ON tasks FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create tasks" ON tasks FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Assigned users, creators and admins can update tasks" ON tasks FOR UPDATE USING (
  created_by = auth.uid() OR 
  assigned_user_id = auth.uid() OR
  owner_id = auth.uid() OR
  auth.uid()::text = ANY(assigned_users) OR
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete tasks" ON tasks FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task comments policies
CREATE POLICY "Users can view all task comments" ON task_comments FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task comments" ON task_comments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Comment authors and admins can update comments" ON task_comments FOR UPDATE USING (
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Comment authors and admins can delete comments" ON task_comments FOR DELETE USING (
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task history policies
CREATE POLICY "Users can view all task history" ON task_history FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task history" ON task_history FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- User capacities policies
CREATE POLICY "Users can view all user capacities" ON user_capacities FOR SELECT USING (true);
CREATE POLICY "Users can manage own capacity" ON user_capacities FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Admins can manage all capacities" ON user_capacities FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task efforts policies
CREATE POLICY "Users can view all task efforts" ON task_efforts FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task efforts" ON task_efforts FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Assigned users and admins can update task efforts" ON task_efforts FOR UPDATE USING (
  assigned_user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Admins can delete task efforts" ON task_efforts FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

SELECT 'RLS policies created successfully!' as message;
