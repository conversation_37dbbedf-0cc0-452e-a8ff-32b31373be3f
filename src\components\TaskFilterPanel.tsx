import React, { useState, useMemo } from 'react';
import { Filter, ChevronDown, ChevronUp, X, User, Users, Tag, Calendar, AlertCircle } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import FilterDropdown from './FilterDropdown';
import FilterChip from './FilterChip';
import SearchFilter from './SearchFilter';

export default function TaskFilterPanel() {
  const {
    tasks,
    users,
    userGroups,
    columns,
    taskFilters,
    setTaskFilters,
    clearTaskFilters,
    selectedTreeNode,
    projects,
    folders
  } = useSupabaseStore();
  
  const [isExpanded, setIsExpanded] = useState(false);

  // Helper function to toggle filter values
  const toggleFilterValue = (filterKey: keyof typeof taskFilters, value: string) => {
    const currentValues = taskFilters[filterKey] as string[];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];

    setTaskFilters({ [filterKey]: newValues });
  };

  // Get tasks in current context for filter options
  const contextTasks = useMemo(() => {
    if (!selectedTreeNode) return tasks;

    const selectedProject = projects.find(p => p.id === selectedTreeNode);
    if (selectedProject) {
      return tasks.filter(t => t.projectId === selectedTreeNode);
    }

    const selectedFolder = folders.find(f => f.id === selectedTreeNode);
    if (selectedFolder) {
      const projectsInFolder = projects.filter(p => p.folderId === selectedTreeNode);
      const projectIds = projectsInFolder.map(p => p.id);
      return tasks.filter(t => t.projectId && projectIds.includes(t.projectId));
    }

    const selectedTask = tasks.find(t => t.id === selectedTreeNode);
    if (selectedTask) {
      return [selectedTask];
    }

    return tasks;
  }, [tasks, selectedTreeNode, projects, folders]);

  // Generate filter options from context tasks
  const filterOptions = useMemo(() => {
    const uniqueTags = [...new Set(contextTasks.flatMap(task => task.tags))];
    const uniqueOwners = [...new Set(contextTasks.map(task => task.owner).filter(Boolean))];
    
    return {
      users: users.map(user => ({ value: user.id, label: user.name })),
      userGroups: userGroups.map(group => ({ 
        value: group.id, 
        label: group.name, 
        color: group.color 
      })),
      statuses: columns.map(col => ({ 
        value: col.id, 
        label: col.title,
        color: col.color 
      })),
      priorities: [
        { value: 'low', label: 'Low', color: 'bg-green-100 text-green-800' },
        { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
        { value: 'high', label: 'High', color: 'bg-red-100 text-red-800' }
      ],
      tags: uniqueTags.map(tag => ({ value: tag, label: tag })),
      owners: uniqueOwners.map(owner => ({ value: owner, label: owner }))
    };
  }, [contextTasks, users, userGroups, columns]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;

    // Count array filters
    count += taskFilters.assignedUsers.length;
    count += taskFilters.status.length;
    count += taskFilters.priority.length;
    count += taskFilters.owners.length;
    count += taskFilters.assignedGroups.length;
    count += taskFilters.tags.length;

    // Count date range filter
    if (taskFilters.dueDateRange.start || taskFilters.dueDateRange.end) {
      count += 1;
    }

    // Count overdue filter
    if (taskFilters.hasOverdueTasks) {
      count += 1;
    }

    // Count search filter
    if (taskFilters.search && taskFilters.search.term.trim()) {
      count += 1;
    }

    return count;
  }, [taskFilters]);

  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    setTaskFilters({
      dueDateRange: {
        ...taskFilters.dueDateRange,
        [field]: value || undefined
      }
    });
  };

  const handleOverdueToggle = () => {
    setTaskFilters({ hasOverdueTasks: !taskFilters.hasOverdueTasks });
  };

  // Search filter handlers
  const handleSearchTypeChange = (type: 'task-title' | 'project-name') => {
    setTaskFilters({
      search: taskFilters.search ? { ...taskFilters.search, type } : { type, term: '', isRegex: false }
    });
  };

  const handleSearchTermChange = (term: string) => {
    if (!term.trim()) {
      setTaskFilters({ search: undefined });
    } else {
      setTaskFilters({
        search: taskFilters.search ? { ...taskFilters.search, term } : { type: 'task-title', term, isRegex: false }
      });
    }
  };

  const handleRegexModeChange = (isRegex: boolean) => {
    setTaskFilters({
      search: taskFilters.search ? { ...taskFilters.search, isRegex } : { type: 'task-title', term: '', isRegex }
    });
  };

  const handleSearchClear = () => {
    setTaskFilters({ search: undefined });
  };

  const removeFilter = (filterKey: keyof typeof taskFilters, value: string) => {
    if (filterKey === 'dueDateRange') {
      setTaskFilters({ dueDateRange: {} });
    } else if (filterKey === 'search') {
      setTaskFilters({ search: undefined });
    } else if (filterKey === 'hasOverdueTasks') {
      setTaskFilters({ hasOverdueTasks: false });
    } else {
      toggleFilterValue(filterKey, value);
    }
  };

  const getActiveFilterChips = () => {
    const chips: Array<{ key: string; value: string; label: string; color?: string }> = [];
    
    // Add user filters
    taskFilters.assignedUsers.forEach(userId => {
      const user = users.find(u => u.id === userId);
      chips.push({
        key: 'assignedUsers',
        value: userId,
        label: `User: ${user?.name || userId}`,
        color: 'bg-blue-100 text-blue-800'
      });
    });

    // Add status filters
    taskFilters.status.forEach(statusId => {
      const status = columns.find(c => c.id === statusId);
      chips.push({
        key: 'status',
        value: statusId,
        label: `Status: ${status?.title || statusId}`,
        color: 'bg-purple-100 text-purple-800'
      });
    });

    // Add priority filters
    taskFilters.priority.forEach(priority => {
      chips.push({
        key: 'priority',
        value: priority,
        label: `Priority: ${priority}`,
        color: priority === 'high' ? 'bg-red-100 text-red-800' : 
              priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
              'bg-green-100 text-green-800'
      });
    });

    // Add group filters
    taskFilters.assignedGroups.forEach(groupId => {
      const group = userGroups.find(g => g.id === groupId);
      chips.push({
        key: 'assignedGroups',
        value: groupId,
        label: `Team: ${group?.name || groupId}`,
        color: 'bg-indigo-100 text-indigo-800'
      });
    });

    // Add tag filters
    taskFilters.tags.forEach(tag => {
      chips.push({
        key: 'tags',
        value: tag,
        label: `Tag: ${tag}`,
        color: 'bg-gray-100 text-gray-800'
      });
    });

    // Add owner filters
    taskFilters.owners.forEach(owner => {
      chips.push({
        key: 'owners',
        value: owner,
        label: `Owner: ${owner}`,
        color: 'bg-orange-100 text-orange-800'
      });
    });

    // Add date range filter
    if (taskFilters.dueDateRange.start || taskFilters.dueDateRange.end) {
      const start = taskFilters.dueDateRange.start;
      const end = taskFilters.dueDateRange.end;
      const label = start && end ? `Due: ${start} to ${end}` :
                   start ? `Due after: ${start}` :
                   `Due before: ${end}`;
      chips.push({
        key: 'dueDateRange',
        value: 'dateRange',
        label,
        color: 'bg-teal-100 text-teal-800'
      });
    }

    // Add overdue filter
    if (taskFilters.hasOverdueTasks) {
      chips.push({
        key: 'hasOverdueTasks',
        value: 'overdue',
        label: 'Overdue tasks',
        color: 'bg-red-100 text-red-800'
      });
    }

    // Add search filter
    if (taskFilters.search && taskFilters.search.term.trim()) {
      const searchTypeLabel = taskFilters.search.type === 'task-title' ? 'Task Title' : 'Project Name';
      const modeLabel = taskFilters.search.isRegex ? ' (regex)' : '';
      chips.push({
        key: 'search',
        value: 'search',
        label: `${searchTypeLabel}: "${taskFilters.search.term}"${modeLabel}`,
        color: 'bg-emerald-100 text-emerald-800'
      });
    }

    return chips;
  };

  return (
    <div className="border-b border-gray-200 bg-gray-50">
      {/* Filter Header */}
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {activeFilterCount > 0 && (
              <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full">
                {activeFilterCount}
              </span>
            )}
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
          
          {activeFilterCount > 0 && (
            <button
              onClick={clearTaskFilters}
              className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1"
            >
              <X className="w-3 h-3" />
              Clear all
            </button>
          )}
        </div>

        {/* Active Filter Chips */}
        {activeFilterCount > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {getActiveFilterChips().map((chip, index) => (
              <FilterChip
                key={`${chip.key}-${chip.value}-${index}`}
                label={chip.label}
                value={chip.value}
                color={chip.color}
                onRemove={(value) => removeFilter(chip.key as keyof typeof taskFilters, value)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Filter Controls */}
      {isExpanded && (
        <div className="px-6 pb-4">
          {/* Search Filter */}
          <div className="mb-4">
            <SearchFilter
              searchType={taskFilters.search?.type || 'task-title'}
              searchTerm={taskFilters.search?.term || ''}
              isRegex={taskFilters.search?.isRegex || false}
              onSearchTypeChange={handleSearchTypeChange}
              onSearchTermChange={handleSearchTermChange}
              onRegexModeChange={handleRegexModeChange}
              onClear={handleSearchClear}
            />
          </div>

          {/* Row 1: Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {/* Assigned Users Filter */}
            <FilterDropdown
              label="Assigned Users"
              options={filterOptions.users}
              selectedValues={taskFilters.assignedUsers}
              onToggle={(value) => toggleFilterValue('assignedUsers', value)}
              placeholder="Select users..."
              icon={<User className="w-4 h-4 text-gray-400" />}
            />

            {/* Status Filter */}
            <FilterDropdown
              label="Status"
              options={filterOptions.statuses}
              selectedValues={taskFilters.status}
              onToggle={(value) => toggleFilterValue('status', value)}
              placeholder="Select statuses..."
              icon={<AlertCircle className="w-4 h-4 text-gray-400" />}
            />

            {/* Priority Filter */}
            <FilterDropdown
              label="Priority"
              options={filterOptions.priorities}
              selectedValues={taskFilters.priority}
              onToggle={(value) => toggleFilterValue('priority', value)}
              placeholder="Select priorities..."
              icon={<Tag className="w-4 h-4 text-gray-400" />}
            />
          </div>

          {/* Row 2: Teams, Date Filters, and Overdue */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-3">
            {/* Assigned Groups Filter */}
            <FilterDropdown
              label="Teams"
              options={filterOptions.userGroups}
              selectedValues={taskFilters.assignedGroups}
              onToggle={(value) => toggleFilterValue('assignedGroups', value)}
              placeholder="Select teams..."
              icon={<Users className="w-4 h-4 text-gray-400" />}
            />

            {/* Due After Filter */}
            <div className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg bg-white">
              <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">Due after:</label>
              <input
                type="date"
                value={taskFilters.dueDateRange.start || ''}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className="border-0 bg-transparent text-sm flex-1 focus:outline-none"
              />
            </div>

            {/* Due Before Filter */}
            <div className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg bg-white">
              <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">Due before:</label>
              <input
                type="date"
                value={taskFilters.dueDateRange.end || ''}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className="border-0 bg-transparent text-sm flex-1 focus:outline-none"
              />
            </div>
          </div>

          {/* Row 3: Tags, Owners, and Overdue Checkbox */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-3">
            {/* Tags Filter */}
            {filterOptions.tags.length > 0 && (
              <FilterDropdown
                label="Tags"
                options={filterOptions.tags}
                selectedValues={taskFilters.tags}
                onToggle={(value) => toggleFilterValue('tags', value)}
                placeholder="Select tags..."
                icon={<Tag className="w-4 h-4 text-gray-400" />}
              />
            )}

            {/* Owners Filter */}
            {filterOptions.owners.length > 0 && (
              <FilterDropdown
                label="Owners"
                options={filterOptions.owners}
                selectedValues={taskFilters.owners}
                onToggle={(value) => toggleFilterValue('owners', value)}
                placeholder="Select owners..."
                icon={<User className="w-4 h-4 text-gray-400" />}
              />
            )}

            {/* Show Only Overdue Tasks */}
            <div className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg bg-white">
              <input
                type="checkbox"
                id="overdue-filter"
                checked={taskFilters.hasOverdueTasks || false}
                onChange={handleOverdueToggle}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="overdue-filter" className="text-sm font-medium text-gray-700">
                Show only overdue tasks
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
