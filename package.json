{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@editorjs/header": "^2.8.8", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.8", "@editorjs/paragraph": "^2.11.7", "@supabase/supabase-js": "^2.52.0", "date-fns": "^3.3.1", "lucide-react": "^0.344.0", "quill": "^2.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-editor-js": "^2.1.0", "react-quill": "^2.0.0", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.10"}}