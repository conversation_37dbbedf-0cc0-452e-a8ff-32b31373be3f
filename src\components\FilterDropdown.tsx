import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';

interface FilterOption {
  value: string;
  label: string;
  color?: string;
}

interface FilterDropdownProps {
  label: string;
  options: FilterOption[];
  selectedValues: string[];
  onToggle: (value: string) => void;
  placeholder?: string;
  icon?: React.ReactNode;
}

export default function FilterDropdown({ 
  label, 
  options, 
  selectedValues, 
  onToggle, 
  placeholder = 'Select options...',
  icon 
}: FilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selectedCount = selectedValues.length;
  const displayText = selectedCount === 0 
    ? placeholder 
    : selectedCount === 1 
      ? options.find(opt => opt.value === selectedValues[0])?.label || selectedValues[0]
      : `${selectedCount} selected`;

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        className={`flex items-center justify-between w-full px-3 py-2 text-sm border rounded-lg hover:bg-gray-50 transition-colors ${
          selectedCount > 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-300 bg-white'
        }`}
      >
        <div className="flex items-center gap-2">
          {icon}
          <span className="font-medium text-gray-700">{label}:</span>
          <span className={selectedCount > 0 ? 'text-blue-700' : 'text-gray-500'}>
            {displayText}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          className="absolute top-full mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {options.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500">No options available</div>
          ) : (
            options.map((option) => {
              const isSelected = selectedValues.includes(option.value);
              return (
                <button
                  key={option.value}
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggle(option.value);
                  }}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    {option.color && (
                      <div className={`w-3 h-3 rounded-full ${option.color}`} />
                    )}
                    <span>{option.label}</span>
                  </div>
                  {isSelected && <Check className="w-4 h-4 text-blue-600" />}
                </button>
              );
            })
          )}
        </div>
      )}
    </div>
  );
}
