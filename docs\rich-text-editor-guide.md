# Rich Text Editor Guide

## Overview

The TaskFlow Project Management Tool features a comprehensive rich text editor for task and subtask descriptions, built with React Quill. This guide covers all aspects of the rich text functionality, from basic usage to technical implementation details.

## Features

### Text Formatting Options

#### Basic Formatting
- **Bold**: Make text bold for emphasis
- **Italic**: Italicize text for subtle emphasis
- **Underline**: Underline important text
- **Strikethrough**: Cross out text to show deletions or completed items
- **Code**: Format inline code with monospace font and background highlighting

#### Content Structure
- **Headers**: Three levels of headers (H1, H2, H3) for organizing content
- **Ordered Lists**: Numbered lists for sequential items
- **Unordered Lists**: Bullet points for non-sequential items
- **Links**: Insert and manage hyperlinks to external resources

#### Toolbar Layout
The editor toolbar is organized into logical groups:
1. **Headers**: Dropdown for selecting header levels
2. **Text Formatting**: Bold, italic, underline, strikethrough buttons
3. **Code**: Inline code formatting
4. **Lists**: Ordered and unordered list options
5. **Links**: Link insertion and management
6. **Clean**: Remove all formatting from selected text

## Usage Guide

### Creating Rich Content

#### Basic Text Formatting
1. **Select text** you want to format
2. **Click the appropriate toolbar button** (Bold, Italic, etc.)
3. **Continue typing** to maintain the formatting
4. **Click the button again** to turn off formatting

#### Adding Headers
1. **Place cursor** at the beginning of a line
2. **Select header level** from the dropdown (Normal, H1, H2, H3)
3. **Type your header text**
4. **Press Enter** to return to normal text

#### Creating Lists
1. **Click the list button** (ordered or unordered)
2. **Type your first item**
3. **Press Enter** to create a new list item
4. **Press Enter twice** to exit the list

#### Inserting Links
1. **Select the text** you want to make into a link
2. **Click the link button** in the toolbar
3. **Enter the URL** in the popup dialog
4. **Click OK** to create the link

### Keyboard Shortcuts

Common keyboard shortcuts work in the editor:
- **Ctrl+B** (Cmd+B on Mac): Bold
- **Ctrl+I** (Cmd+I on Mac): Italic
- **Ctrl+U** (Cmd+U on Mac): Underline
- **Ctrl+`** (Cmd+` on Mac): Code formatting

## Technical Implementation

### Architecture

#### Components
- **RichTextEditor.tsx**: Main editor component with React Quill integration
- **RichTextDisplay.tsx**: Read-only display components for various contexts
- **richTextUtils.ts**: Utility functions for content conversion and validation

#### Data Storage
- **Format**: HTML content stored in existing database TEXT fields
- **Backward Compatibility**: Automatic conversion of plain text to HTML
- **Sanitization**: HTML content is sanitized to prevent XSS attacks

#### Content Conversion
- **HTML to Plain Text**: For display in cards and lists with proper truncation
- **Plain Text to HTML**: For backward compatibility with existing content
- **Validation**: Content validation ensures data integrity

### Configuration

#### Toolbar Configuration
```javascript
toolbar: [
  [{ 'header': [1, 2, 3, false] }],
  ['bold', 'italic', 'underline', 'strike'],
  ['code'],
  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
  ['link'],
  ['clean']
]
```

#### Supported Formats
- `header`: Header levels 1-3
- `bold`, `italic`, `underline`, `strike`: Text formatting
- `code`: Inline code formatting
- `list`, `bullet`: List formatting
- `link`: Hyperlink support

### Styling

#### Custom CSS
The editor includes custom styling to match the application design:
- **Consistent borders** and border radius
- **Matching colors** with the application theme
- **Proper spacing** and typography
- **Focus states** for accessibility

#### Responsive Design
- **Mobile-friendly** toolbar and editor
- **Proper sizing** on different screen sizes
- **Touch-friendly** controls for mobile devices

## Best Practices

### Content Creation
1. **Use headers** to organize long descriptions
2. **Use lists** for step-by-step instructions or requirements
3. **Use links** to reference external resources
4. **Use code formatting** for technical terms or commands
5. **Keep descriptions concise** but informative

### Performance Considerations
1. **Avoid extremely long content** in descriptions
2. **Use links** instead of embedding large amounts of text
3. **Consider using subtasks** for breaking down complex tasks

### Accessibility
1. **Use proper header hierarchy** (H1 → H2 → H3)
2. **Provide meaningful link text** (avoid "click here")
3. **Use lists** for better screen reader navigation
4. **Keep content structure logical**

## Troubleshooting

### Common Issues

#### Editor Not Loading
- **Check browser compatibility**: Modern browsers required
- **Verify JavaScript**: Ensure JavaScript is enabled
- **Check console errors**: Look for React Quill loading issues

#### Formatting Not Saving
- **Check network connection**: Ensure stable internet connection
- **Verify permissions**: Ensure user has edit permissions
- **Check for errors**: Look for validation or save errors

#### Content Display Issues
- **Clear browser cache**: Force refresh the page
- **Check HTML validity**: Ensure content is properly formatted
- **Verify sanitization**: Check if content was modified during save

### Browser Compatibility
- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Internet Explorer**: Not supported

## Migration from Plain Text

### Automatic Conversion
Existing plain text descriptions are automatically converted to HTML when:
1. **Opening the editor**: Plain text is converted to HTML paragraphs
2. **Saving content**: Content is validated and sanitized
3. **Displaying content**: Proper rendering in both editor and display modes

### Manual Migration
For bulk migration of existing content:
1. **No action required**: Conversion happens automatically
2. **Content preserved**: Original plain text content is maintained
3. **Gradual upgrade**: Users can enhance content over time

## Security

### HTML Sanitization
All rich text content is sanitized to prevent:
- **XSS attacks**: Script tags and dangerous attributes removed
- **Malicious content**: Potentially harmful HTML filtered out
- **Data integrity**: Content validation ensures proper format

### Content Validation
- **Format checking**: Ensures content is valid HTML
- **Size limits**: Prevents extremely large content
- **Type validation**: Ensures content matches expected format

## Future Enhancements

### Planned Features
- **Image support**: Inline image insertion and management
- **Table support**: Rich table creation and editing
- **Advanced formatting**: Additional text formatting options
- **Collaboration**: Real-time collaborative editing
- **Templates**: Pre-defined content templates

### Integration Opportunities
- **Markdown support**: Import/export Markdown format
- **External integrations**: Connect with external content sources
- **API access**: Programmatic content management
- **Automation**: Automated content generation and formatting
