import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, MoreHorizontal, Users } from 'lucide-react';

interface User {
  id: string;
  email: string;
  name?: string;
}

interface Group {
  id: string;
  name: string;
  color?: string;
}

interface CompactAssignmentFieldProps {
  label: string;
  selectedUserId: string;
  users: User[];
  additionalUsers?: string[];
  groups?: string[];
  availableGroups?: Group[];
  onUserChange: (userId: string) => void;
  onAdditionalUsersChange?: (userIds: string[]) => void;
  onGroupsChange?: (groupIds: string[]) => void;
  showAdvanced?: boolean;
}

export default function CompactAssignmentField({
  label,
  selectedUserId,
  users,
  additionalUsers = [],
  groups = [],
  availableGroups = [],
  onUserChange,
  onAdditionalUsersChange,
  onGroupsChange,
  showAdvanced = false
}: CompactAssignmentFieldProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedUser = users.find(u => u.id === selectedUserId);
  const hasAdvancedOptions = showAdvanced && (onAdditionalUsersChange || onGroupsChange);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setShowMoreOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleUserSelect = (userId: string) => {
    onUserChange(userId);
    if (!hasAdvancedOptions) {
      setIsOpen(false);
    }
  };

  const handleAdditionalUserToggle = (userId: string) => {
    if (!onAdditionalUsersChange) return;
    
    const newAdditionalUsers = additionalUsers.includes(userId)
      ? additionalUsers.filter(id => id !== userId)
      : [...additionalUsers, userId];
    
    onAdditionalUsersChange(newAdditionalUsers);
  };

  const handleGroupToggle = (groupId: string) => {
    if (!onGroupsChange) return;
    
    const newGroups = groups.includes(groupId)
      ? groups.filter(id => id !== groupId)
      : [...groups, groupId];
    
    onGroupsChange(newGroups);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium mb-1">{label}</label>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full border rounded-lg p-2 text-sm text-left bg-white hover:bg-gray-50 transition-colors flex items-center justify-between"
      >
        <span className="truncate">
          {selectedUser ? (
            <span>
              {selectedUser.name || selectedUser.email}
              {selectedUser.name && (
                <span className="text-gray-500 text-xs ml-1">({selectedUser.email})</span>
              )}
            </span>
          ) : (
            <span className="text-gray-400">Select {label.toLowerCase()}</span>
          )}
        </span>
        <div className="flex items-center gap-1 flex-shrink-0">
          {hasAdvancedOptions && (additionalUsers.length > 0 || groups.length > 0) && (
            <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
              +{additionalUsers.length + groups.length}
            </span>
          )}
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </div>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-full max-h-80 overflow-hidden">
          {/* Primary User Selection */}
          <div className="max-h-40 overflow-y-auto">
            {users.map((user) => (
              <button
                key={user.id}
                type="button"
                onClick={() => handleUserSelect(user.id)}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                  user.id === selectedUserId ? 'bg-blue-50 text-blue-700' : ''
                }`}
              >
                <div className="truncate">
                  {user.name || user.email}
                  {user.name && (
                    <div className="text-xs text-gray-500 truncate">{user.email}</div>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Advanced Options */}
          {hasAdvancedOptions && (
            <>
              <div className="border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowMoreOptions(!showMoreOptions)}
                  className="w-full px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors flex items-center justify-between"
                >
                  <span className="flex items-center gap-2">
                    <MoreHorizontal className="w-4 h-4" />
                    More Options
                  </span>
                  <ChevronDown className={`w-4 h-4 transition-transform ${showMoreOptions ? 'rotate-180' : ''}`} />
                </button>
              </div>

              {showMoreOptions && (
                <div className="border-t border-gray-200 bg-gray-50">
                  {/* Additional Users */}
                  {onAdditionalUsersChange && (
                    <div className="p-3">
                      <div className="text-xs font-medium text-gray-700 mb-2 flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        Additional Users
                      </div>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {users.filter(u => u.id !== selectedUserId).map((user) => (
                          <label key={user.id} className="flex items-center gap-2 text-xs">
                            <input
                              type="checkbox"
                              checked={additionalUsers.includes(user.id)}
                              onChange={() => handleAdditionalUserToggle(user.id)}
                              className="rounded border-gray-300"
                            />
                            <span className="truncate">
                              {user.name || user.email}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Groups */}
                  {onGroupsChange && availableGroups.length > 0 && (
                    <div className="p-3 border-t border-gray-200">
                      <div className="text-xs font-medium text-gray-700 mb-2">Groups</div>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {availableGroups.map((group) => (
                          <label key={group.id} className="flex items-center gap-2 text-xs">
                            <input
                              type="checkbox"
                              checked={groups.includes(group.id)}
                              onChange={() => handleGroupToggle(group.id)}
                              className="rounded border-gray-300"
                            />
                            <span className="truncate">{group.name}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}
