import React, { useState } from 'react';
import { X, Plus, Trash2, Target, Mail, MessageSquare, Edit, UserPlus } from 'lucide-react';
import { ActionConfig as ActionConfigType, ActionType } from '../../types/automation';

interface ActionConfigProps {
  configs: ActionConfigType[];
  onSave: (configs: ActionConfigType[]) => void;
  onClose: () => void;
}

const ACTION_TYPES: Array<{ value: ActionType; label: string; description: string; icon: React.ReactNode }> = [
  {
    value: 'update_task_status',
    label: 'Update Task Status',
    description: 'Change the status of a task',
    icon: <Edit className="w-4 h-4" />
  },
  {
    value: 'assign_task',
    label: 'Assign Task',
    description: 'Assign task to users or groups',
    icon: <UserPlus className="w-4 h-4" />
  },
  {
    value: 'reassign_task',
    label: 'Reassign Task',
    description: 'Replace current assignees',
    icon: <UserPlus className="w-4 h-4" />
  },
  {
    value: 'update_task_field',
    label: 'Update Task Field',
    description: 'Update any task field',
    icon: <Edit className="w-4 h-4" />
  },
  {
    value: 'add_comment',
    label: 'Add Comment',
    description: 'Add a comment to the task',
    icon: <MessageSquare className="w-4 h-4" />
  },
  {
    value: 'send_notification',
    label: 'Send Notification',
    description: 'Send notification to users',
    icon: <Mail className="w-4 h-4" />
  },
  {
    value: 'send_email',
    label: 'Send Email',
    description: 'Send email notification',
    icon: <Mail className="w-4 h-4" />
  },
  {
    value: 'create_task',
    label: 'Create Task',
    description: 'Create a new task',
    icon: <Plus className="w-4 h-4" />
  },
  {
    value: 'create_subtask',
    label: 'Create Subtask',
    description: 'Create a subtask under current task',
    icon: <Plus className="w-4 h-4" />
  },
  {
    value: 'update_due_date',
    label: 'Update Due Date',
    description: 'Change task due date',
    icon: <Edit className="w-4 h-4" />
  },
  {
    value: 'add_tag',
    label: 'Add Tag',
    description: 'Add a tag to the task',
    icon: <Target className="w-4 h-4" />
  },
  {
    value: 'remove_tag',
    label: 'Remove Tag',
    description: 'Remove a tag from the task',
    icon: <Target className="w-4 h-4" />
  },
  {
    value: 'update_priority',
    label: 'Update Priority',
    description: 'Change task priority',
    icon: <Edit className="w-4 h-4" />
  }
];

const TASK_STATUSES = ['todo', 'in-progress', 'review', 'done'];
const TASK_PRIORITIES = ['low', 'medium', 'high'];
const TASK_FIELDS = ['title', 'description', 'status', 'priority', 'dueDate', 'startDate'];

export default function ActionConfig({ configs, onSave, onClose }: ActionConfigProps) {
  const [actionConfigs, setActionConfigs] = useState<ActionConfigType[]>(
    configs.length > 0 ? configs : [{ type: 'update_task_status' }]
  );

  const handleSave = () => {
    onSave(actionConfigs);
  };

  const addAction = () => {
    setActionConfigs(prev => [...prev, { type: 'update_task_status' }]);
  };

  const removeAction = (index: number) => {
    setActionConfigs(prev => prev.filter((_, i) => i !== index));
  };

  const updateAction = (index: number, updates: Partial<ActionConfigType>) => {
    setActionConfigs(prev => prev.map((action, i) => 
      i === index ? { ...action, ...updates } : action
    ));
  };

  const renderActionSpecificConfig = (action: ActionConfigType, index: number) => {
    switch (action.type) {
      case 'update_task_status':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              New Status
            </label>
            <select
              value={action.value || 'todo'}
              onChange={(e) => updateAction(index, { value: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              {TASK_STATUSES.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        );

      case 'update_priority':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              New Priority
            </label>
            <select
              value={action.value || 'medium'}
              onChange={(e) => updateAction(index, { value: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              {TASK_PRIORITIES.map(priority => (
                <option key={priority} value={priority}>{priority}</option>
              ))}
            </select>
          </div>
        );

      case 'update_task_field':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Field to Update
              </label>
              <select
                value={action.targetField || 'title'}
                onChange={(e) => updateAction(index, { targetField: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              >
                {TASK_FIELDS.map(field => (
                  <option key={field} value={field}>{field}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                New Value
              </label>
              <input
                type="text"
                value={action.value || ''}
                onChange={(e) => updateAction(index, { value: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="Enter new value"
              />
            </div>
          </div>
        );

      case 'assign_task':
      case 'reassign_task':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Assignment Type
              </label>
              <select
                value={action.assignmentConfig?.assignmentType || 'add'}
                onChange={(e) => updateAction(index, {
                  assignmentConfig: {
                    ...action.assignmentConfig,
                    assignmentType: e.target.value as 'replace' | 'add' | 'remove'
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              >
                <option value="add">Add to existing</option>
                <option value="replace">Replace existing</option>
                <option value="remove">Remove from existing</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                User ID (Optional)
              </label>
              <input
                type="text"
                value={action.assignmentConfig?.userId || ''}
                onChange={(e) => updateAction(index, {
                  assignmentConfig: {
                    ...action.assignmentConfig,
                    userId: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="User ID or {{trigger.userId}}"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Group ID (Optional)
              </label>
              <input
                type="text"
                value={action.assignmentConfig?.groupId || ''}
                onChange={(e) => updateAction(index, {
                  assignmentConfig: {
                    ...action.assignmentConfig,
                    groupId: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="Group ID"
              />
            </div>
          </div>
        );

      case 'add_comment':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Comment Text
            </label>
            <textarea
              value={action.value || ''}
              onChange={(e) => updateAction(index, { value: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              rows={3}
              placeholder="Comment text (use {{task.title}} for dynamic values)"
            />
          </div>
        );

      case 'send_notification':
      case 'send_email':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Subject
              </label>
              <input
                type="text"
                value={action.templateConfig?.subject || ''}
                onChange={(e) => updateAction(index, {
                  templateConfig: {
                    ...action.templateConfig,
                    subject: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="Notification subject"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Message
              </label>
              <textarea
                value={action.templateConfig?.body || ''}
                onChange={(e) => updateAction(index, {
                  templateConfig: {
                    ...action.templateConfig,
                    body: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                rows={3}
                placeholder="Message body (use {{task.title}} for dynamic values)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Recipients (comma-separated user IDs)
              </label>
              <input
                type="text"
                value={action.templateConfig?.recipients?.join(', ') || ''}
                onChange={(e) => updateAction(index, {
                  templateConfig: {
                    ...action.templateConfig,
                    recipients: e.target.value.split(',').map(r => r.trim()).filter(Boolean)
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="user1, user2, {{task.assignedUsers}}"
              />
            </div>
          </div>
        );

      case 'create_task':
      case 'create_subtask':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Task Title
              </label>
              <input
                type="text"
                value={action.taskCreationConfig?.title || ''}
                onChange={(e) => updateAction(index, {
                  taskCreationConfig: {
                    ...action.taskCreationConfig,
                    title: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="New task title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={action.taskCreationConfig?.description || ''}
                onChange={(e) => updateAction(index, {
                  taskCreationConfig: {
                    ...action.taskCreationConfig,
                    description: e.target.value
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                rows={2}
                placeholder="Task description"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Priority
              </label>
              <select
                value={action.taskCreationConfig?.priority || 'medium'}
                onChange={(e) => updateAction(index, {
                  taskCreationConfig: {
                    ...action.taskCreationConfig,
                    priority: e.target.value as 'low' | 'medium' | 'high'
                  }
                })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              >
                {TASK_PRIORITIES.map(priority => (
                  <option key={priority} value={priority}>{priority}</option>
                ))}
              </select>
            </div>
          </div>
        );

      case 'update_due_date':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              New Due Date
            </label>
            <input
              type="date"
              value={action.value || ''}
              onChange={(e) => updateAction(index, { value: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            />
          </div>
        );

      case 'add_tag':
      case 'remove_tag':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Tag Name
            </label>
            <input
              type="text"
              value={action.value || ''}
              onChange={(e) => updateAction(index, { value: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              placeholder="Tag name"
            />
          </div>
        );

      default:
        return null;
    }
  };

  const renderAction = (action: ActionConfigType, index: number) => {
    const actionType = ACTION_TYPES.find(t => t.value === action.type);

    return (
      <div key={index} className="border border-gray-600 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="text-green-400">{actionType?.icon}</div>
            <h4 className="text-white font-medium">Action {index + 1}</h4>
          </div>
          <button
            onClick={() => removeAction(index)}
            className="p-1 text-red-400 hover:text-red-300"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Action Type
            </label>
            <select
              value={action.type}
              onChange={(e) => updateAction(index, { type: e.target.value as ActionType })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              {ACTION_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            <p className="text-gray-400 text-xs mt-1">{actionType?.description}</p>
          </div>

          {renderActionSpecificConfig(action, index)}
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Target className="w-6 h-6 text-green-400" />
            <h2 className="text-xl font-semibold text-white">Configure Actions</h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-300 text-sm">
              Define what actions should be performed when the trigger fires and conditions are met.
            </p>
          </div>

          <div className="space-y-4">
            {actionConfigs.map((action, index) => renderAction(action, index))}
          </div>

          <button
            onClick={addAction}
            className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Action
          </button>
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Save Actions
          </button>
        </div>
      </div>
    </div>
  );
}
