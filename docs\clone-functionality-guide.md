# Clone Functionality Guide

This guide covers the comprehensive clone functionality for tasks and projects in the Project Management Tool.

## 🎯 Overview

The clone functionality allows you to duplicate tasks and projects with all their properties, creating templates and reducing repetitive work. Cloned items maintain all attributes while starting with fresh time tracking.

## ✨ Key Features

### Smart Naming
- **Incremental Naming**: Cloned items get "Clone_N_" prefix with automatic numbering
- **Collision Avoidance**: System automatically finds the next available number
- **Examples**: 
  - "Project Alpha" → "Clone_1_Project Alpha"
  - If "Clone_1_Project Alpha" exists → "Clone_2_Project Alpha"

### Fresh Time Tracking
- **Zero Start**: All cloned tasks start with zero time tracking
- **New History**: Fresh history entries mark the creation of cloned items
- **Independent Tracking**: Cloned items track time independently from originals

### Complete Attribute Preservation
- **All Properties**: Dates, assignments, priorities, custom fields
- **Relationships**: Dependencies within projects are preserved
- **Structure**: Folder assignments and project hierarchies maintained

## 🔄 Task Cloning

### Available Locations

#### 1. Task List View
- **Location**: Copy button (📋) next to edit/delete actions
- **Access**: Click the Copy icon in any task row
- **Result**: Creates a clone of the task in the same project

#### 2. Task Edit Form
- **Location**: Three-dot menu (⋮) in the top-right corner
- **Access**: 
  1. Open any task for editing
  2. Click the three-dot menu in the header
  3. Select "Clone Task"
- **Result**: Creates a clone and closes the edit form

#### 3. Subtask Edit Form
- **Location**: Three-dot menu (⋮) in the top-right corner
- **Access**:
  1. Open any subtask for editing
  2. Click the three-dot menu in the header
  3. Select "Clone Parent Task"
- **Result**: Clones the entire parent task with all subtasks

### What Gets Cloned

✅ **Task Properties**:
- Title (with Clone_N_ prefix)
- Description
- Status
- Priority
- Start and due dates
- Assigned users and groups
- Owner information
- Tags
- Custom field values
- Effort estimates

✅ **Subtasks**:
- All subtasks with their properties
- Subtask assignments and dates
- Subtask custom fields

✅ **Structure**:
- Project assignment
- Folder location

❌ **What Doesn't Get Cloned**:
- Comments (starts fresh)
- Time tracking history (starts at zero)
- Task ID (gets new unique ID)

## 🏗️ Project Cloning

### Available Locations

#### 1. Project Tree Context Menu
- **Location**: Right-click on any project in the tree sidebar
- **Access**: Right-click → "Clone Project"
- **Result**: Creates a complete project clone

#### 2. Project Manager
- **Location**: Hover over project → blue Copy icon
- **Access**: 
  1. Go to Projects tab
  2. Hover over any project
  3. Click the blue Copy icon
- **Result**: Creates a complete project clone

#### 3. Project Tree Node Menu
- **Location**: Three-dot context menu in project tree
- **Access**: Click three-dot menu → "Clone Project"
- **Result**: Creates a complete project clone

### What Gets Cloned

✅ **Project Properties**:
- Name (with Clone_N_ prefix)
- Description
- Color
- Start and end dates
- Folder assignment
- Effort data

✅ **All Tasks**:
- Every task in the project
- All task properties and custom fields
- All subtasks with their properties

✅ **Task Relationships**:
- Dependencies between tasks within the project
- Dependency dates and constraints
- Task hierarchy and structure

✅ **Assignments**:
- User assignments
- Group assignments
- Owner information

❌ **What Doesn't Get Cloned**:
- Comments on tasks (start fresh)
- Time tracking data (starts at zero)
- Project and task IDs (get new unique IDs)

## 🎮 How to Use

### Cloning a Single Task

1. **From Task List**:
   ```
   Tasks → Find your task → Click Copy icon (📋)
   ```

2. **From Task Edit Form**:
   ```
   Tasks → Click task title → Click ⋮ menu → Clone Task
   ```

3. **From Subtask Edit Form**:
   ```
   Tasks → Open task → Click subtask → Click ⋮ menu → Clone Parent Task
   ```

### Cloning a Project

1. **From Project Tree**:
   ```
   Right-click project → Clone Project
   ```

2. **From Project Manager**:
   ```
   Projects tab → Hover over project → Click blue Copy icon
   ```

3. **From Context Menu**:
   ```
   Project tree → Click ⋮ next to project → Clone Project
   ```

## 🔧 Technical Implementation

### Database Operations
- **Atomic Transactions**: All cloning operations are atomic
- **Dependency Mapping**: Internal task dependencies are remapped to cloned tasks
- **Field Validation**: All cloned data passes through the same validation as new items

### Performance
- **Batch Operations**: Multiple tasks are cloned efficiently in batches
- **Real-time Updates**: UI updates immediately after successful cloning
- **Error Handling**: Comprehensive error handling with user feedback

### Security
- **Permission Checks**: Users can only clone items they have access to
- **Data Integrity**: All cloned items maintain referential integrity
- **Audit Trail**: Clone operations are logged in task history

## 🚨 Important Notes

### Limitations
- **Cross-Project Dependencies**: Dependencies to tasks outside the project are not cloned
- **External References**: Links to external systems are copied as-is
- **User Permissions**: Cloned items inherit the same permission requirements

### Best Practices
- **Template Projects**: Create template projects for common workflows
- **Naming Conventions**: Use descriptive names for items you plan to clone
- **Regular Cleanup**: Periodically review and clean up unused cloned items

### Troubleshooting
- **Clone Fails**: Check that you have permission to create items in the target location
- **Missing Attributes**: Ensure all custom fields are properly configured
- **Dependency Issues**: Verify that all referenced users and groups exist

## 📊 Use Cases

### Project Templates
Create template projects for:
- **Recurring Workflows**: Monthly reports, quarterly reviews
- **Standard Processes**: Onboarding, product launches
- **Team Structures**: Standard team compositions and roles

### Task Templates
Create template tasks for:
- **Common Activities**: Code reviews, testing procedures
- **Standard Checklists**: Quality assurance, deployment steps
- **Recurring Tasks**: Weekly meetings, monthly reports

### Training and Documentation
- **Example Projects**: Create sample projects for training
- **Process Documentation**: Clone and modify for different scenarios
- **Best Practice Examples**: Share successful project structures

## 🎯 Tips and Tricks

1. **Batch Cloning**: Clone entire projects to duplicate complex workflows
2. **Incremental Updates**: Clone and modify rather than creating from scratch
3. **Template Management**: Maintain a "Templates" folder for commonly cloned items
4. **Dependency Planning**: Design projects with self-contained dependencies for better cloning
5. **Custom Fields**: Set up custom fields before cloning to ensure all data is preserved

---

*For technical implementation details, see the source code in `src/services/cloneService.ts`*
