import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Task } from '../types';

interface ClickablePriorityBadgeProps {
  priority: Task['priority'];
  onChange: (priority: Task['priority']) => void;
}

export default function ClickablePriorityBadge({ priority, onChange }: ClickablePriorityBadgeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const priorityOptions = [
    { id: 'low', label: 'Low', color: 'bg-green-100 text-green-800' },
    { id: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'high', label: 'High', color: 'bg-red-100 text-red-800' },
    { id: 'urgent', label: 'Urgent', color: 'bg-red-200 text-red-900' }
  ];

  const getCurrentPriorityInfo = () => {
    const option = priorityOptions.find(opt => opt.id === priority);
    return option || priorityOptions[1]; // Default to medium if not found
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentPriority = getCurrentPriorityInfo();

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium transition-all hover:opacity-80 ${currentPriority.color}`}
      >
        {currentPriority.label}
        <ChevronDown className="w-3 h-3" />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[100px]">
          {priorityOptions.map((option) => (
            <button
              key={option.id}
              type="button"
              onClick={() => {
                onChange(option.id as Task['priority']);
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                option.id === priority ? 'bg-gray-50' : ''
              }`}
            >
              <span className={`inline-flex items-center px-2 py-1 rounded-full ${option.color}`}>
                {option.label}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
