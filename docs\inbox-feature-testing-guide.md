# Inbox Feature Testing Guide

## Overview
This guide outlines how to test the newly implemented inbox functionality that allows users to receive notifications when mentioned in comments using @username syntax.

## Features Implemented

### 1. Database Schema
- ✅ `notifications` table with proper RLS policies
- ✅ Indexes for performance optimization
- ✅ Proper foreign key relationships

### 2. Backend Services
- ✅ `notificationService` with CRUD operations
- ✅ Mention detection utilities (`mentionUtils.ts`)
- ✅ Real-time subscription for notifications

### 3. UI Components
- ✅ `Inbox` component with notification list
- ✅ `NotificationItem` component with read/unread states
- ✅ Sidebar integration with unread count badge

### 4. Core Functionality
- ✅ @username mention detection in comments
- ✅ Automatic notification creation for valid mentions
- ✅ Real-time notification delivery
- ✅ Mark as read/unread functionality

## Manual Testing Checklist

### Setup Requirements
1. Ensure Supabase database is updated with the new schema
2. Have at least 2 user accounts for testing mentions
3. Application should be running with real-time subscriptions enabled

### Test Cases

#### 1. Basic Mention Detection
- [ ] Create a comment with `@username` where username exists
- [ ] Verify notification is created for the mentioned user
- [ ] Check that the sender doesn't receive a notification for their own mention

#### 2. Multiple Mentions
- [ ] Create a comment mentioning multiple users: `@user1 @user2 please review`
- [ ] Verify both users receive separate notifications
- [ ] Check notification content includes the task title and comment excerpt

#### 3. Invalid Mentions
- [ ] Create a comment with `@nonexistentuser`
- [ ] Verify no notification is created for invalid usernames
- [ ] Test mentions with special characters that should be ignored

#### 4. Inbox UI Functionality
- [ ] Navigate to Inbox from sidebar
- [ ] Verify unread count badge appears when there are unread notifications
- [ ] Test filtering between "All" and "Unread" notifications
- [ ] Verify notifications are sorted by creation date (newest first)

#### 5. Read/Unread States
- [ ] Click on a notification to mark it as read
- [ ] Verify the notification appearance changes (background color, etc.)
- [ ] Test "Mark all as read" functionality
- [ ] Verify unread count updates in real-time

#### 6. Task Navigation
- [ ] Click "View Task" button on a notification
- [ ] Verify it navigates to the correct task
- [ ] Check that clicking on task link marks notification as read

#### 7. Real-time Updates
- [ ] Have two browser windows open with different users
- [ ] Create a mention in one window
- [ ] Verify the notification appears immediately in the other window
- [ ] Test that unread count updates in real-time

#### 8. Edge Cases
- [ ] Test very long comments (>100 characters) - should be truncated in notifications
- [ ] Test mentions in replies to comments
- [ ] Test mentions in subtask comments
- [ ] Test case-insensitive username matching

## Automated Testing

### Unit Tests
Run the mention utilities tests:
```bash
npm test src/utils/__tests__/mentionUtils.test.ts
```

### Integration Tests
The following areas should have integration tests added:

1. **Comment Creation with Mentions**
   - Test that creating a comment with mentions triggers notification creation
   - Verify notification content is correct

2. **Real-time Notification Delivery**
   - Test that notifications appear in real-time for mentioned users
   - Verify notification state updates work correctly

3. **Database Operations**
   - Test notification CRUD operations
   - Verify RLS policies work correctly

## Performance Considerations

### Database Performance
- Monitor query performance for notification fetching
- Ensure indexes are being used effectively
- Consider pagination for users with many notifications

### Real-time Performance
- Monitor WebSocket connection stability
- Test with multiple concurrent users
- Verify polling fallback works when WebSocket fails

## Security Testing

### Row Level Security
- Verify users can only see their own notifications
- Test that users cannot access other users' notifications via API
- Ensure notification creation requires proper authentication

### Input Validation
- Test mention detection with malicious input
- Verify XSS protection in notification content
- Test SQL injection protection in notification queries

## Known Limitations

1. **Username Matching**: Currently matches by name or email (case-insensitive)
2. **Notification Cleanup**: No automatic cleanup of old notifications implemented
3. **Mention Highlighting**: Comments don't visually highlight mentions yet
4. **Push Notifications**: Only in-app notifications, no browser push notifications

## Future Enhancements

1. Add visual highlighting of @mentions in comments
2. Implement notification cleanup/archiving
3. Add email notifications for mentions
4. Support for @team or @group mentions
5. Notification preferences/settings
6. Mobile push notifications

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check real-time subscription status in browser console
2. **Unread count not updating**: Verify WebSocket connection is active
3. **Mentions not detected**: Check username format and user existence
4. **Database errors**: Verify RLS policies and user permissions

### Debug Commands
```javascript
// Check real-time connection status
window.useSupabaseStore.getState().connectionState

// Get current notifications
window.useSupabaseStore.getState().notifications

// Force refresh notifications
window.useSupabaseStore.getState().loadNotifications(userId)
```
