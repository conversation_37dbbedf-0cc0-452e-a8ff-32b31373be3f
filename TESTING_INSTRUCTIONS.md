# Custom Fields Testing Instructions

## 🔧 Database Setup (Required First)

The database schema has been applied successfully as indicated by the message "Custom fields schema created successfully!" However, let's verify the setup:

### 1. Verify Database Tables
In Supabase Dashboard > Table Editor, you should see:
- `custom_fields` table with columns: id, name, label, field_type, etc.
- `tasks` table should have a new `custom_field_values` column (JSONB type)

### 2. Check Sample Data
The schema includes two sample custom fields:
- **priority_score** (number field)
- **complexity** (dropdown field with options: Simple, Medium, Complex, Very Complex)

## 🧪 Testing Steps

### Phase 1: Fix Current Error
The error in the console should now be resolved. The app should load normally with all existing data visible.

**Expected Result**: 
- No more "Cannot read properties of undefined (reading 'map')" error
- Tasks, projects, columns, users should all load normally
- Console should show "Custom fields loaded: X" (where X is the number of custom fields)

### Phase 2: Test Admin Interface
1. **Navigate to Admin Settings**
   - Click on the admin sidebar
   - Look for "Custom Fields" option (with FormInput icon)
   - Click on "Custom Fields"

2. **Verify Custom Field Manager**
   - Should see the CustomFieldManager component
   - Should display existing custom fields (priority_score and complexity)
   - Should have "Add Custom Field" button

3. **Test Creating New Custom Field**
   - Click "Add Custom Field"
   - Try creating a text field: name="project_phase", label="Project Phase"
   - Try creating a date field: name="target_date", label="Target Date"
   - Verify validation works (try invalid names, empty labels)

### Phase 3: Test Task Form Integration
1. **Open Task Creation Form**
   - Go to any project/view
   - Click "Add Task" or similar
   - Look for "Custom Fields" expandable section

2. **Verify Custom Fields Section**
   - Should see purple-themed expandable section
   - Click to expand - should show all active custom fields
   - Test different field types (text, number, date, dropdown)
   - Test required field validation

3. **Test Task Saving**
   - Fill in custom field values
   - Save the task
   - Verify task saves successfully with custom field data

### Phase 4: Test Subtask Form Integration
1. **Open Subtask Creation**
   - Open an existing task
   - Add a new subtask
   - Verify custom fields section appears in subtask form

2. **Test Subtask Custom Fields**
   - Fill in custom field values for subtask
   - Save subtask
   - Verify subtask saves with custom field data

### Phase 5: Test Data Persistence
1. **Verify Data Saves**
   - Create tasks/subtasks with custom field values
   - Refresh the page
   - Open the tasks/subtasks again
   - Verify custom field values are preserved

2. **Test Editing**
   - Edit existing tasks/subtasks
   - Modify custom field values
   - Save and verify changes persist

## 🐛 Troubleshooting

### If Custom Fields Don't Appear:
1. Check browser console for errors
2. Verify database schema was applied correctly
3. Check if user has admin permissions for custom field management

### If Validation Errors Occur:
1. Check that required fields are filled
2. Verify dropdown options are selected from available choices
3. Ensure number fields contain valid numbers
4. Ensure date fields contain valid dates

### If Data Doesn't Save:
1. Check browser network tab for failed requests
2. Verify database permissions
3. Check if custom_field_values column exists in tasks table

## ✅ Success Criteria

The implementation is working correctly if:

- [ ] App loads without console errors
- [ ] Admin can access Custom Fields management
- [ ] Admin can create/edit/delete custom fields
- [ ] Custom fields appear in task forms
- [ ] Custom fields appear in subtask forms
- [ ] Custom field values save and persist
- [ ] Validation works for required fields
- [ ] Different field types render correctly

## 📞 Next Steps

Once basic functionality is confirmed:
1. Test with multiple users to ensure permissions work
2. Create various field types to test all functionality
3. Test edge cases (very long text, large numbers, etc.)
4. Verify performance with many custom fields

The custom fields system is now ready for production use! 🎉
