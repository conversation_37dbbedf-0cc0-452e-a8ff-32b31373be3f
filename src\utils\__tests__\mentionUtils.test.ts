import { extractMentions, validateMentions, createMentionNotificationContent, shouldNotifyUser } from '../mentionUtils';
import { User } from '../../types';

// Mock users for testing
const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    groupId: null,
    skillsetIds: [],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    groupId: null,
    skillsetIds: [],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    groupId: null,
    skillsetIds: [],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  }
];

describe('mentionUtils', () => {
  describe('extractMentions', () => {
    it('should extract single mention', () => {
      const content = 'Hey @john, can you review this?';
      const mentions = extractMentions(content);
      expect(mentions).toEqual(['john']);
    });

    it('should extract multiple mentions', () => {
      const content = 'Hey @john and @jane, can you both review this?';
      const mentions = extractMentions(content);
      expect(mentions).toEqual(['john', 'jane']);
    });

    it('should handle mentions with dots and underscores', () => {
      const content = 'Hey @jane.smith and @bob_wilson, check this out!';
      const mentions = extractMentions(content);
      expect(mentions).toEqual(['jane.smith', 'bob_wilson']);
    });

    it('should handle email-like mentions', () => {
      const content = 'Please contact @<EMAIL> for details';
      const mentions = extractMentions(content);
      expect(mentions).toEqual(['john']);
    });

    it('should remove duplicates', () => {
      const content = 'Hey @john, @john can you help @john with this?';
      const mentions = extractMentions(content);
      expect(mentions).toEqual(['john']);
    });

    it('should return empty array when no mentions', () => {
      const content = 'This is a regular comment without mentions';
      const mentions = extractMentions(content);
      expect(mentions).toEqual([]);
    });
  });

  describe('validateMentions', () => {
    it('should validate mentions by name', () => {
      const mentions = ['John Doe', 'Jane Smith'];
      const validUsers = validateMentions(mentions, mockUsers);
      expect(validUsers).toHaveLength(2);
      expect(validUsers.map(u => u.name)).toEqual(['John Doe', 'Jane Smith']);
    });

    it('should validate mentions by email', () => {
      const mentions = ['<EMAIL>', '<EMAIL>'];
      const validUsers = validateMentions(mentions, mockUsers);
      expect(validUsers).toHaveLength(2);
      expect(validUsers.map(u => u.email)).toEqual(['<EMAIL>', '<EMAIL>']);
    });

    it('should handle case insensitive matching', () => {
      const mentions = ['JOHN DOE', '<EMAIL>'];
      const validUsers = validateMentions(mentions, mockUsers);
      expect(validUsers).toHaveLength(2);
    });

    it('should filter out invalid mentions', () => {
      const mentions = ['John Doe', '<EMAIL>', 'Jane Smith'];
      const validUsers = validateMentions(mentions, mockUsers);
      expect(validUsers).toHaveLength(2);
      expect(validUsers.map(u => u.name)).toEqual(['John Doe', 'Jane Smith']);
    });

    it('should remove duplicate users', () => {
      const mentions = ['John Doe', '<EMAIL>']; // Same user by name and email
      const validUsers = validateMentions(mentions, mockUsers);
      expect(validUsers).toHaveLength(1);
      expect(validUsers[0].name).toBe('John Doe');
    });
  });

  describe('createMentionNotificationContent', () => {
    it('should create notification content', () => {
      const result = createMentionNotificationContent(
        'Jane Smith',
        'Fix login bug',
        'Hey @john, can you help me with this login issue?'
      );
      
      expect(result.title).toBe('Jane Smith mentioned you in "Fix login bug"');
      expect(result.content).toBe('Hey @john, can you help me with this login issue?');
    });

    it('should truncate long content', () => {
      const longContent = 'A'.repeat(150);
      const result = createMentionNotificationContent(
        'Jane Smith',
        'Long task',
        longContent
      );
      
      expect(result.content).toHaveLength(103); // 100 chars + '...'
      expect(result.content.endsWith('...')).toBe(true);
    });
  });

  describe('shouldNotifyUser', () => {
    it('should return true for different users', () => {
      expect(shouldNotifyUser('user1', 'user2')).toBe(true);
    });

    it('should return false for same user', () => {
      expect(shouldNotifyUser('user1', 'user1')).toBe(false);
    });
  });
});
