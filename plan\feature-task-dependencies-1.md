---
goal: Implement Full Task Dependencies and Successor Functionality for Gantt Chart Support
version: 1.0
date_created: 2025-01-28
last_updated: 2025-01-28
owner: Development Team
tags: [feature, dependencies, gantt, project-management, automation]
---

# Introduction

This plan implements comprehensive task dependency and successor functionality to support Gantt charts and automatic date propagation. When dependencies are specified with due dates, changes to dependency tasks will automatically update start dates and due dates for dependent tasks following standard project management principles.

## 1. Requirements & Constraints

- **REQ-001**: Tasks must support multiple dependencies (predecessor tasks)
- **REQ-002**: Tasks must support multiple successors (dependent tasks)
- **REQ-003**: Automatic date propagation when dependency dates change
- **REQ-004**: Start date calculation based on latest dependency completion
- **REQ-005**: Gantt chart visualization support
- **REQ-006**: Circular dependency detection and prevention
- **REQ-007**: Dependency type support (Finish-to-Start, Start-to-Start, etc.)
- **SEC-001**: Row Level Security policies for dependency tables
- **CON-001**: Must not disturb existing task functionality
- **CON-002**: Application-level validation preferred over complex RLS
- **CON-003**: Modular implementation approach
- **GUD-001**: Follow existing codebase patterns and conventions
- **PAT-001**: Use existing Supabase service patterns
- **PAT-002**: Maintain TypeScript type safety throughout

## 2. Implementation Steps

### Phase 1: Database Schema Extension (connect directly to supabase using supabase tool)
1. Create `task_dependencies` table with dependency types
2. Add database indexes for performance
3. Create RLS policies for security
4. Add migration script for existing data

### Phase 2: Type Definitions and Interfaces
1. Define TypeScript interfaces for dependencies
2. Extend existing Task interface
3. Create dependency-specific types
4. Update database type definitions

### Phase 3: Core Dependency Services
1. Create dependency service layer
2. Implement CRUD operations for dependencies
3. Add circular dependency detection
4. Create date calculation algorithms

### Phase 4: Automatic Date Propagation
1. Implement dependency change detection
2. Create date recalculation engine
3. Add batch update functionality
4. Integrate with existing task update flows

### Phase 5: UI Components
1. Create dependency selection component
2. Add dependency visualization
3. Extend TaskForm with dependency fields
4. Create dependency management interface

### Phase 6: Gantt Chart Enhancement
1. Extend Timeline component for dependencies
2. Add dependency lines visualization
3. Implement drag-and-drop date updates
4. Add critical path highlighting

### Phase 7: Integration and Testing
1. Integrate with existing store
2. Add comprehensive validation
3. Create test scenarios
4. Performance optimization

## 3. Alternatives

- **ALT-001**: Store dependencies as JSONB in tasks table - Rejected due to query complexity and relationship integrity
- **ALT-002**: Use external scheduling library - Rejected to maintain control and integration
- **ALT-003**: Simple predecessor-only model - Rejected as it doesn't support full project management needs

## 4. Dependencies

- **DEP-001**: Existing Supabase infrastructure and authentication
- **DEP-002**: Current task management system and data structures
- **DEP-003**: Timeline/Gantt chart component foundation
- **DEP-004**: Date manipulation utilities (date-fns)

## 5. Files

- **FILE-001**: `supabase-task-dependencies.sql` - Database schema for dependencies
- **FILE-002**: `src/types/dependencies.ts` - TypeScript type definitions
- **FILE-003**: `src/services/dependencyService.ts` - Core dependency operations
- **FILE-004**: `src/services/dateCalculationService.ts` - Date propagation logic
- **FILE-005**: `src/components/TaskDependencyManager.tsx` - Dependency UI component
- **FILE-006**: `src/components/DependencySelector.tsx` - Dependency selection interface
- **FILE-007**: `src/components/GanttChart.tsx` - Enhanced Gantt visualization
- **FILE-008**: `src/store/useSupabaseStore.ts` - Store integration updates
- **FILE-009**: `src/types/index.ts` - Extended Task interface
- **FILE-010**: `src/components/TaskForm.tsx` - Form updates for dependencies

## 6. Testing

- **TEST-001**: Unit tests for circular dependency detection
- **TEST-002**: Integration tests for date propagation
- **TEST-003**: UI tests for dependency selection
- **TEST-004**: Performance tests for large dependency chains
- **TEST-005**: Edge case tests for complex dependency scenarios

## 7. Risks & Assumptions

- **RISK-001**: Complex dependency chains may cause performance issues
- **RISK-002**: Circular dependencies could create infinite loops
- **RISK-003**: Date propagation conflicts with manual date changes
- **ASSUMPTION-001**: Users understand project management dependency concepts
- **ASSUMPTION-002**: Existing task data structure can accommodate extensions
- **ASSUMPTION-003**: Current Timeline component can be enhanced for Gantt features

## 8. Technical Specifications

### Database Schema Design

```sql
-- Task dependencies table
CREATE TABLE task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  predecessor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  successor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  dependency_type TEXT DEFAULT 'finish_to_start' CHECK (dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')),
  lag_days INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  UNIQUE(predecessor_task_id, successor_task_id)
);
```

### TypeScript Interface Design

```typescript
interface TaskDependency {
  id: string;
  predecessorTaskId: string;
  successorTaskId: string;
  dependencyType: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish';
  lagDays: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface TaskWithDependencies extends Task {
  dependencies: TaskDependency[];
  dependents: TaskDependency[];
  calculatedStartDate?: string;
  calculatedDueDate?: string;
  isOnCriticalPath?: boolean;
}
```

### Date Calculation Algorithm

1. **Finish-to-Start (FS)**: Successor starts after predecessor finishes + lag
2. **Start-to-Start (SS)**: Successor starts when predecessor starts + lag
3. **Finish-to-Finish (FF)**: Successor finishes when predecessor finishes + lag
4. **Start-to-Finish (SF)**: Successor finishes when predecessor starts + lag

### Circular Dependency Detection

- Use depth-first search (DFS) algorithm
- Maintain visited and recursion stack tracking
- Prevent creation of dependencies that would create cycles

### Critical Path Calculation

- Forward pass: Calculate earliest start/finish dates
- Backward pass: Calculate latest start/finish dates
- Identify tasks where earliest = latest (zero float)

## 9. Related Specifications / Further Reading

- [Project Management Institute (PMI) Scheduling Guidelines]
- [Critical Path Method (CPM) Documentation]
- [Supabase Row Level Security Best Practices]
