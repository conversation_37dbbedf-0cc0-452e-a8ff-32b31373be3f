---
goal: Implement Task and Project Cloning Functionality with Prefix Naming
version: 1.0
date_created: 2025-01-09
last_updated: 2025-01-09
owner: Development Team
tags: [feature, clone, task-management, project-management]
---

# Introduction

This plan implements comprehensive cloning functionality for both tasks and projects in the project management tool. When cloning a task, all task settings and subtasks will be cloned with a "Clone_N_" prefix. When cloning a project, the project and all contained tasks and subtasks will be cloned with appropriate prefixes. Clone options will be accessible through context menus and action buttons in the UI.

## 1. Requirements & Constraints

- **REQ-001**: Clone tasks with all properties, subtasks, and settings preserved
- **REQ-002**: Clone projects with all contained tasks and subtasks preserved
- **REQ-003**: Apply "Clone_N_" prefix to cloned item names where N is incremental
- **REQ-004**: Add clone option to task list view edit/delete action buttons
- **REQ-005**: Add clone option to project context menu under edit project option
- **REQ-006**: Add clone option to task form and subtask form three-dot menu
- **REQ-007**: Maintain all relationships and dependencies in cloned items
- **REQ-008**: Preserve custom field values in cloned items
- **REQ-009**: Reset creation timestamps for cloned items
- **REQ-010**: Assign current user as creator of cloned items
- **SEC-001**: Ensure user has permission to create tasks/projects in target location
- **SEC-002**: Validate user authentication before cloning operations
- **CON-001**: Use existing service layer patterns for database operations
- **CON-002**: Maintain transaction integrity during bulk clone operations
- **CON-003**: Follow existing UI patterns for menu placement and styling
- **GUD-001**: Implement modular clone services that don't disturb existing codebase
- **GUD-002**: Use application-level validation over complex database constraints
- **PAT-001**: Follow existing error handling and user feedback patterns

## 2. Implementation Steps

### Phase 1: Core Clone Services
1. Create `src/services/cloneService.ts` with task and project cloning logic
2. Add clone functions to `src/services/supabaseService.ts` for database operations
3. Implement prefix generation logic with incremental numbering
4. Add clone actions to `src/store/useSupabaseStore.ts`

### Phase 2: Task Clone UI Integration
1. Add clone button to task list view action buttons in `src/components/TaskListView.tsx`
2. Add three-dot menu to task form header in `src/components/TaskForm.tsx`
3. Add three-dot menu to subtask form header in `src/components/SubtaskForm.tsx`
4. Implement clone confirmation dialogs

### Phase 3: Project Clone UI Integration
1. Add clone option to project context menu in `src/components/TaskTreeNode.tsx`
2. Update project manager context actions in `src/components/TaskTreeSidebar.tsx`
3. Add clone option to project list actions in `src/components/ProjectList.tsx`

### Phase 4: Testing and Validation
1. Create unit tests for clone services
2. Test UI integration and user workflows
3. Validate data integrity and relationships
4. Test error handling and edge cases

## 3. Alternatives

- **ALT-001**: Implement clone as duplicate with manual renaming - rejected due to poor UX
- **ALT-002**: Use database triggers for cloning - rejected to maintain application-level control
- **ALT-003**: Simple copy without prefix - rejected due to naming confusion requirements

## 4. Dependencies

- **DEP-001**: Existing task and project service layers in `src/services/supabaseService.ts`
- **DEP-002**: Current UI component structure for tasks and projects
- **DEP-003**: Zustand store patterns in `src/store/useSupabaseStore.ts`
- **DEP-004**: Existing authentication and permission systems

## 5. Files

- **FILE-001**: `src/services/cloneService.ts` - New clone service implementation
- **FILE-002**: `src/services/supabaseService.ts` - Add clone database operations
- **FILE-003**: `src/store/useSupabaseStore.ts` - Add clone actions to store
- **FILE-004**: `src/components/TaskListView.tsx` - Add clone button to task actions
- **FILE-005**: `src/components/TaskForm.tsx` - Add three-dot menu with clone option
- **FILE-006**: `src/components/SubtaskForm.tsx` - Add three-dot menu with clone option
- **FILE-007**: `src/components/TaskTreeNode.tsx` - Add clone to project context menu
- **FILE-008**: `src/components/TaskTreeSidebar.tsx` - Handle clone context actions
- **FILE-009**: `src/components/ProjectList.tsx` - Add clone to project actions
- **FILE-010**: `src/types/index.ts` - Add clone-related type definitions if needed

## 6. Testing

- **TEST-001**: Unit tests for clone service functions with various task configurations
- **TEST-002**: Integration tests for task cloning with subtasks and custom fields
- **TEST-003**: Integration tests for project cloning with multiple tasks
- **TEST-004**: UI tests for clone button placement and functionality
- **TEST-005**: Error handling tests for permission and validation failures
- **TEST-006**: Performance tests for large project cloning operations

## 7. Risks & Assumptions

- **RISK-001**: Large projects with many tasks may cause performance issues during cloning
- **RISK-002**: Complex task dependencies may not clone correctly
- **RISK-003**: Custom field validation may fail during bulk operations
- **ASSUMPTION-001**: Current user has permission to create tasks/projects in target locations
- **ASSUMPTION-002**: Database can handle bulk insert operations efficiently
- **ASSUMPTION-003**: UI has sufficient space for additional menu options

## 8. Related Specifications / Further Reading

- Existing task management patterns in `src/components/TaskForm.tsx`
- Project management patterns in `src/components/ProjectForm.tsx`
- Service layer architecture in `src/services/supabaseService.ts`
- Store management patterns in `src/store/useSupabaseStore.ts`
