import React, { useState, useMemo } from 'react';
import { Folder, ChevronRight, ChevronDown, Plus, Edit2, Trash2, FileText, Copy } from 'lucide-react';
import { Project, Folder as FolderType } from '../types';

interface FolderTreeItemProps {
  folder?: FolderType;
  level: number;
  onSelectFolder: (folderId?: string) => void;
  expandedFolders: Set<string>;
  onToggleFolder: (folderId: string) => void;
  selectedFolderId?: string;
  onAddSubfolder: (parentId?: string) => void;
  onAddProject: (folderId?: string) => void;
  onEditFolder: (folder: FolderType) => void;
  onDeleteFolder: (folderId: string) => void;
  onEditProject: (project: Project) => void;
  onDeleteProject: (projectId: string) => void;
  onCloneProject: (projectId: string) => void;
  projects: Project[];
  folders: FolderType[];
}

export default function FolderTreeItem({
  folder,
  level,
  onSelectFolder,
  expandedFolders,
  onToggleFolder,
  selectedFolderId,
  onAddSubfolder,
  onAddProject,
  onEditFolder,
  onDeleteFolder,
  onEditProject,
  onDeleteProject,
  onCloneProject,
  projects,
  folders,
}: FolderTreeItemProps) {
  const [showFolderActions, setShowFolderActions] = useState(false);
  const [hoveredProjectId, setHoveredProjectId] = useState<string | null>(null);
  
  const subfolders = useMemo(() => 
    folders.filter(f => f.parentId === folder?.id),
    [folders, folder?.id]
  );

  const folderProjects = useMemo(() => 
    projects.filter(p => p.folderId === folder?.id),
    [projects, folder?.id]
  );

  const hasChildren = subfolders.length > 0 || folderProjects.length > 0;
  
  return (
    <div>
      <div
        className={`flex items-center group hover:bg-gray-700/50 transition-colors ${
          selectedFolderId === folder?.id ? 'bg-gray-700/50' : ''
        }`}
        onMouseEnter={() => setShowFolderActions(true)}
        onMouseLeave={() => setShowFolderActions(false)}
      >
        <div 
          className="flex items-center gap-1 py-1 px-4 flex-1 cursor-pointer min-w-0"
          style={{ paddingLeft: `${level * 16 + 16}px` }}
          onClick={() => onSelectFolder(folder?.id)}
        >
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {hasChildren ? (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  if (folder) onToggleFolder(folder.id);
                }}
                className="p-0.5 hover:bg-gray-600 rounded cursor-pointer flex-shrink-0 text-gray-400"
              >
                {expandedFolders.has(folder?.id || '') ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </div>
            ) : (
              <div className="w-4" />
            )}
            <Folder className="w-4 h-4 flex-shrink-0 text-gray-400" />
            <span className="truncate text-sm text-gray-200">
              {folder ? folder.name : 'All Projects'}
              {folderProjects.length > 0 && (
                <span className="text-gray-500 ml-1">({folderProjects.length})</span>
              )}
            </span>
          </div>
        </div>

        {showFolderActions && (
          <div className="flex items-center gap-1 pr-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onAddProject(folder?.id);
              }}
              className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
              title="Add Project"
            >
              <Plus className="w-3 h-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onAddSubfolder(folder?.id);
              }}
              className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
              title="Add Folder"
            >
              <Folder className="w-3 h-3" />
            </button>
            {folder && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditFolder(folder);
                  }}
                  className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
                  title="Edit Folder"
                >
                  <Edit2 className="w-3 h-3" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteFolder(folder.id);
                  }}
                  className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
                  title="Delete Folder"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </>
            )}
          </div>
        )}
      </div>

      {(!folder || expandedFolders.has(folder.id)) && (
        <div>
          {/* Projects within the folder */}
          {folderProjects.map(project => (
            <div
              key={project.id}
              className="flex items-center justify-between py-1 px-4 hover:bg-gray-700/50 transition-colors group/project"
              style={{ paddingLeft: `${(level + 1) * 16 + 16}px` }}
              onMouseEnter={() => setHoveredProjectId(project.id)}
              onMouseLeave={() => setHoveredProjectId(null)}
            >
              <div className="flex items-center gap-2 min-w-0">
                <FileText className="w-4 h-4 flex-shrink-0 text-gray-400" />
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${project.color}`} />
                  <span className="truncate text-sm text-gray-300">
                    {project.name}
                  </span>
                </div>
              </div>
              
              {hoveredProjectId === project.id && (
                <div className="flex items-center gap-1 pr-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditProject(project);
                    }}
                    className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
                    title="Edit Project"
                  >
                    <Edit2 className="w-3 h-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onCloneProject(project.id);
                    }}
                    className="p-1 hover:bg-blue-600 rounded text-blue-400 hover:text-blue-300"
                    title="Clone Project"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteProject(project.id);
                    }}
                    className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-white"
                    title="Delete Project"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              )}
            </div>
          ))}

          {/* Subfolders */}
          {subfolders.map(subfolder => (
            <FolderTreeItem
              key={subfolder.id}
              folder={subfolder}
              level={level + 1}
              onSelectFolder={onSelectFolder}
              expandedFolders={expandedFolders}
              onToggleFolder={onToggleFolder}
              selectedFolderId={selectedFolderId}
              onAddSubfolder={onAddSubfolder}
              onAddProject={onAddProject}
              onEditFolder={onEditFolder}
              onDeleteFolder={onDeleteFolder}
              onEditProject={onEditProject}
              onDeleteProject={onDeleteProject}
              onCloneProject={onCloneProject}
              projects={projects}
              folders={folders}
            />
          ))}
        </div>
      )}
    </div>
  );
}