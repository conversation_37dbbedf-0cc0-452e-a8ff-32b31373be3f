import React, { useState, useEffect } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { CustomField, CustomFieldValue, validateAllCustomFieldValues, getDefaultCustomFieldValues } from '../types/customFields';
import CustomFieldInput from './CustomFieldInput';
import { ChevronDown, ChevronRight, FormInput } from 'lucide-react';

interface CustomFieldsSectionProps {
  values: CustomFieldValue;
  onChange: (values: CustomFieldValue) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}

export default function CustomFieldsSection({ 
  values, 
  onChange, 
  errors = {}, 
  disabled = false 
}: CustomFieldsSectionProps) {
  const { customFields } = useSupabaseStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Custom fields are loaded automatically by the store

  // Initialize with default values when custom fields are loaded
  useEffect(() => {
    if (customFields && customFields.length > 0 && !hasInitialized) {
      const activeFields = customFields.filter(f => f.isActive);
      if (activeFields.length > 0) {
        const defaultValues = getDefaultCustomFieldValues(activeFields);
        const mergedValues = { ...defaultValues, ...values };
        
        // Only update if there are actual changes
        if (JSON.stringify(mergedValues) !== JSON.stringify(values)) {
          onChange(mergedValues);
        }
        setHasInitialized(true);
      }
    }
  }, [customFields, values, onChange, hasInitialized]);

  // Auto-expand if there are validation errors
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      setIsExpanded(true);
    }
  }, [errors]);

  const activeFields = (customFields || []).filter(f => f.isActive).sort((a, b) => a.sortOrder - b.sortOrder);

  // Don't render if no custom fields are defined
  if (activeFields.length === 0) {
    return null;
  }

  const handleFieldChange = (fieldName: string, value: string | number | null) => {
    const newValues = { ...values, [fieldName]: value };
    onChange(newValues);
  };

  const hasRequiredFields = activeFields.some(field => field.isRequired);
  const hasErrors = Object.keys(errors).length > 0;

  return (
    <div className="bg-purple-50 p-5 rounded-xl border border-purple-200">
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <div className="flex items-center gap-2">
          <FormInput className="w-5 h-5 text-purple-600" />
          <h3 className="text-sm font-semibold text-purple-900">
            Custom Fields
            {hasRequiredFields && (
              <span className="text-red-500 ml-1">*</span>
            )}
          </h3>
          {hasErrors && (
            <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
              {Object.keys(errors).length} error{Object.keys(errors).length > 1 ? 's' : ''}
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-purple-600">
            {activeFields.length} field{activeFields.length > 1 ? 's' : ''}
          </span>
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-purple-600" />
          ) : (
            <ChevronRight className="w-4 h-4 text-purple-600" />
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="mt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activeFields.map((field) => (
              <div key={field.id} className="space-y-1">
                <CustomFieldInput
                  field={field}
                  value={values[field.name] || null}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  error={errors[field.name]}
                  disabled={disabled}
                />
              </div>
            ))}
          </div>
          
          {/* Help text */}
          <div className="text-xs text-purple-600 bg-purple-100 p-3 rounded-lg">
            <p className="font-medium mb-1">About Custom Fields:</p>
            <ul className="space-y-1 text-purple-700">
              <li>• Custom fields help you track additional information specific to your workflow</li>
              <li>• Required fields must be filled before saving</li>
              <li>• These fields can be used for filtering and reporting</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
