import React from 'react';
import { Task } from '../types';

export const getStatusBadge = (status: Task['status'], columns?: any[]) => {
  // Default colors for standard statuses
  const defaultColors: { [key: string]: string } = {
    'todo': 'bg-gray-100 text-gray-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    'review': 'bg-yellow-100 text-yellow-800',
    'done': 'bg-green-100 text-green-800'
  };

  // Try to get color from columns configuration
  let colorClass = defaultColors[status];
  let statusLabel = status.replace('-', ' ');

  if (columns) {
    const column = columns.find(col => col.id === status);
    if (column) {
      colorClass = column.color || colorClass;
      statusLabel = column.title || statusLabel;
    }
  }

  // Fallback for custom statuses
  if (!colorClass) {
    colorClass = 'bg-purple-100 text-purple-800';
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
      {statusLabel}
    </span>
  );
};

export const getPriorityBadge = (priority: Task['priority']) => {
  const colors = {
    'low': 'bg-green-100 text-green-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-red-100 text-red-800',
    'urgent': 'bg-red-200 text-red-900'
  };
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[priority]}`}>
      {priority}
    </span>
  );
};

export const truncateTitle = (title: string, maxLength: number = 50) => {
  if (title.length <= maxLength) {
    return title;
  }
  return title.substring(0, maxLength) + '...';
};
