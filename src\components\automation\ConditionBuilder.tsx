import React, { useState } from 'react';
import { X, Plus, Trash2, Filter } from 'lucide-react';
import { 
  ConditionConfig, 
  ConditionGroup, 
  Condition, 
  ConditionOperator, 
  LogicOperator,
  TASK_FIELDS,
  PROJECT_FIELDS,
  USER_FIELDS
} from '../../types/automation';

interface ConditionBuilderProps {
  config?: ConditionConfig;
  onSave: (config: ConditionConfig) => void;
  onClose: () => void;
}

const OPERATORS: Array<{ value: ConditionOperator; label: string }> = [
  { value: 'equals', label: 'equals' },
  { value: 'not_equals', label: 'does not equal' },
  { value: 'contains', label: 'contains' },
  { value: 'not_contains', label: 'does not contain' },
  { value: 'greater_than', label: 'is greater than' },
  { value: 'less_than', label: 'is less than' },
  { value: 'greater_than_or_equal', label: 'is greater than or equal to' },
  { value: 'less_than_or_equal', label: 'is less than or equal to' },
  { value: 'is_empty', label: 'is empty' },
  { value: 'is_not_empty', label: 'is not empty' },
  { value: 'in', label: 'is in list' },
  { value: 'not_in', label: 'is not in list' }
];

const ALL_FIELDS = [...TASK_FIELDS, ...PROJECT_FIELDS, ...USER_FIELDS];

export default function ConditionBuilder({ config, onSave, onClose }: ConditionBuilderProps) {
  const [conditionConfig, setConditionConfig] = useState<ConditionConfig>(
    config || {
      rootGroup: {
        id: crypto.randomUUID(),
        logic: 'AND',
        conditions: [],
        groups: []
      }
    }
  );

  const handleSave = () => {
    onSave(conditionConfig);
  };

  const addCondition = (groupId: string) => {
    const newCondition: Condition = {
      id: crypto.randomUUID(),
      field: 'title',
      operator: 'equals',
      value: '',
      valueType: 'static',
      entityType: 'task'
    };

    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, groupId, (group) => ({
        ...group,
        conditions: [...group.conditions, newCondition]
      }))
    }));
  };

  const removeCondition = (groupId: string, conditionId: string) => {
    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, groupId, (group) => ({
        ...group,
        conditions: group.conditions.filter(c => c.id !== conditionId)
      }))
    }));
  };

  const updateCondition = (groupId: string, conditionId: string, updates: Partial<Condition>) => {
    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, groupId, (group) => ({
        ...group,
        conditions: group.conditions.map(c => 
          c.id === conditionId ? { ...c, ...updates } : c
        )
      }))
    }));
  };

  const addGroup = (parentGroupId: string) => {
    const newGroup: ConditionGroup = {
      id: crypto.randomUUID(),
      logic: 'AND',
      conditions: [],
      groups: []
    };

    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, parentGroupId, (group) => ({
        ...group,
        groups: [...group.groups, newGroup]
      }))
    }));
  };

  const removeGroup = (parentGroupId: string, groupId: string) => {
    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, parentGroupId, (group) => ({
        ...group,
        groups: group.groups.filter(g => g.id !== groupId)
      }))
    }));
  };

  const updateGroupLogic = (groupId: string, logic: LogicOperator) => {
    setConditionConfig(prev => ({
      ...prev,
      rootGroup: updateGroup(prev.rootGroup, groupId, (group) => ({
        ...group,
        logic
      }))
    }));
  };

  const updateGroup = (
    group: ConditionGroup, 
    targetId: string, 
    updater: (group: ConditionGroup) => ConditionGroup
  ): ConditionGroup => {
    if (group.id === targetId) {
      return updater(group);
    }

    return {
      ...group,
      groups: group.groups.map(g => updateGroup(g, targetId, updater))
    };
  };

  const renderCondition = (condition: Condition, groupId: string) => {
    const fieldOptions = ALL_FIELDS.filter(f => 
      !condition.entityType || f.entityType === condition.entityType
    );

    return (
      <div key={condition.id} className="flex items-center gap-2 p-3 bg-gray-700 rounded-lg">
        <select
          value={condition.field}
          onChange={(e) => updateCondition(groupId, condition.id, { field: e.target.value })}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
        >
          {fieldOptions.map(field => (
            <option key={field.name} value={field.name}>
              {field.label}
            </option>
          ))}
        </select>

        <select
          value={condition.operator}
          onChange={(e) => updateCondition(groupId, condition.id, { operator: e.target.value as ConditionOperator })}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
        >
          {OPERATORS.map(op => (
            <option key={op.value} value={op.value}>
              {op.label}
            </option>
          ))}
        </select>

        {!['is_empty', 'is_not_empty'].includes(condition.operator) && (
          <input
            type="text"
            value={condition.value}
            onChange={(e) => updateCondition(groupId, condition.id, { value: e.target.value })}
            className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm flex-1"
            placeholder="Value"
          />
        )}

        <button
          onClick={() => removeCondition(groupId, condition.id)}
          className="p-1 text-red-400 hover:text-red-300"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    );
  };

  const renderGroup = (group: ConditionGroup, isRoot = false, parentGroupId?: string) => {
    return (
      <div key={group.id} className={`border border-gray-600 rounded-lg p-4 ${isRoot ? '' : 'ml-4'}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="text-gray-300 text-sm">Match:</span>
            <select
              value={group.logic}
              onChange={(e) => updateGroupLogic(group.id, e.target.value as LogicOperator)}
              className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
            >
              <option value="AND">All conditions (AND)</option>
              <option value="OR">Any condition (OR)</option>
            </select>
          </div>

          {!isRoot && parentGroupId && (
            <button
              onClick={() => removeGroup(parentGroupId, group.id)}
              className="p-1 text-red-400 hover:text-red-300"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}
        </div>

        <div className="space-y-3">
          {group.conditions.map(condition => renderCondition(condition, group.id))}
          
          {group.groups.map(nestedGroup => renderGroup(nestedGroup, false, group.id))}
        </div>

        <div className="flex gap-2 mt-4">
          <button
            onClick={() => addCondition(group.id)}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            Add Condition
          </button>
          <button
            onClick={() => addGroup(group.id)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            Add Group
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Filter className="w-6 h-6 text-yellow-400" />
            <h2 className="text-xl font-semibold text-white">Build Conditions</h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-300 text-sm">
              Define conditions that must be met for the workflow to execute. 
              Leave empty to run the workflow for all trigger events.
            </p>
          </div>

          {renderGroup(conditionConfig.rootGroup, true)}

          {conditionConfig.rootGroup.conditions.length === 0 && conditionConfig.rootGroup.groups.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              <Filter className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No conditions defined</p>
              <p className="text-sm">Workflow will run for all trigger events</p>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
          >
            Save Conditions
          </button>
        </div>
      </div>
    </div>
  );
}
