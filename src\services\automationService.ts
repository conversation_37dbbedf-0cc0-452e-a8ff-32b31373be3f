import { supabase, handleSupabaseError } from '../lib/supabase';
import { Database } from '../types/database';
import { AutomationWorkflow, AutomationExecution, AutomationTrigger } from '../types/automation';

type Tables = Database['public']['Tables'];

// Transform database row to application model
const transformWorkflowFromDB = (row: Tables['automation_workflows']['Row']): AutomationWorkflow => ({
  id: row.id,
  name: row.name,
  description: row.description || undefined,
  isActive: row.is_active,
  isTemplate: row.is_template,
  createdBy: row.created_by,
  projectId: row.project_id || undefined,
  folderId: row.folder_id || undefined,
  triggerConfig: row.trigger_config as any,
  conditionConfig: row.condition_config as any,
  actionConfig: row.action_config as any,
  executionCount: row.execution_count,
  lastExecutedAt: row.last_executed_at || undefined,
  createdAt: row.created_at,
  updatedAt: row.updated_at,
});

const transformExecutionFromDB = (row: Tables['automation_executions']['Row']): AutomationExecution => ({
  id: row.id,
  workflowId: row.workflow_id,
  triggerData: row.trigger_data as any,
  conditionResult: row.condition_result as any,
  actionResults: row.action_results as any,
  status: row.status as any,
  errorMessage: row.error_message || undefined,
  executionTimeMs: row.execution_time_ms || undefined,
  executedAt: row.executed_at,
});

const transformTriggerFromDB = (row: Tables['automation_triggers']['Row']): AutomationTrigger => ({
  id: row.id,
  workflowId: row.workflow_id,
  triggerType: row.trigger_type as any,
  triggerConfig: row.trigger_config as any,
  isActive: row.is_active,
  createdAt: row.created_at,
  updatedAt: row.updated_at,
});

// Automation Workflows Service
export const automationWorkflowService = {
  async getWorkflows(projectId?: string, folderId?: string) {
    let query = supabase
      .from('automation_workflows')
      .select('*')
      .order('created_at', { ascending: false });

    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (folderId) {
      query = query.eq('folder_id', folderId);
    }

    const { data, error } = await query;
    if (error) handleSupabaseError(error);
    return (data || []).map(transformWorkflowFromDB);
  },

  async getWorkflow(id: string) {
    const { data, error } = await supabase
      .from('automation_workflows')
      .select('*')
      .eq('id', id)
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformWorkflowFromDB(data) : null;
  },

  async createWorkflow(workflow: Omit<AutomationWorkflow, 'id' | 'createdAt' | 'updatedAt' | 'executionCount' | 'lastExecutedAt'>) {
    const { data, error } = await supabase
      .from('automation_workflows')
      .insert({
        name: workflow.name,
        description: workflow.description || null,
        is_active: workflow.isActive,
        is_template: workflow.isTemplate,
        created_by: workflow.createdBy,
        project_id: workflow.projectId || null,
        folder_id: workflow.folderId || null,
        trigger_config: workflow.triggerConfig as any,
        condition_config: workflow.conditionConfig as any,
        action_config: workflow.actionConfig as any,
      })
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformWorkflowFromDB(data) : null;
  },

  async updateWorkflow(id: string, updates: Partial<AutomationWorkflow>) {
    const updateData: Partial<Tables['automation_workflows']['Update']> = {};
    
    if (updates.name !== undefined) updateData.name = updates.name;
    if (updates.description !== undefined) updateData.description = updates.description || null;
    if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
    if (updates.isTemplate !== undefined) updateData.is_template = updates.isTemplate;
    if (updates.projectId !== undefined) updateData.project_id = updates.projectId || null;
    if (updates.folderId !== undefined) updateData.folder_id = updates.folderId || null;
    if (updates.triggerConfig !== undefined) updateData.trigger_config = updates.triggerConfig as any;
    if (updates.conditionConfig !== undefined) updateData.condition_config = updates.conditionConfig as any;
    if (updates.actionConfig !== undefined) updateData.action_config = updates.actionConfig as any;

    const { data, error } = await supabase
      .from('automation_workflows')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformWorkflowFromDB(data) : null;
  },

  async deleteWorkflow(id: string) {
    const { error } = await supabase
      .from('automation_workflows')
      .delete()
      .eq('id', id);

    if (error) handleSupabaseError(error);
  },

  async incrementExecutionCount(id: string) {
    // First get the current workflow to read the execution count
    const { data: currentWorkflow, error: fetchError } = await supabase
      .from('automation_workflows')
      .select('execution_count')
      .eq('id', id)
      .single();

    if (fetchError) {
      handleSupabaseError(fetchError);
      return null;
    }

    // Then update with incremented count
    const { data, error } = await supabase
      .from('automation_workflows')
      .update({
        execution_count: (currentWorkflow.execution_count || 0) + 1,
        last_executed_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformWorkflowFromDB(data) : null;
  },

  async getActiveWorkflowsByTrigger(triggerType: string, entityType?: string) {
    let query = supabase
      .from('automation_workflows')
      .select('*')
      .eq('is_active', true);

    const { data, error } = await query;
    if (error) handleSupabaseError(error);
    
    // Filter by trigger type in application code since JSONB queries can be complex
    return (data || [])
      .map(transformWorkflowFromDB)
      .filter(workflow => workflow.triggerConfig.type === triggerType);
  },

  async duplicateWorkflow(id: string, newName: string) {
    const original = await this.getWorkflow(id);
    if (!original) throw new Error('Workflow not found');

    const duplicate = {
      ...original,
      name: newName,
      isActive: false, // Start duplicated workflows as inactive
      executionCount: 0,
      lastExecutedAt: undefined,
    };

    // Remove fields that shouldn't be copied
    delete (duplicate as any).id;
    delete (duplicate as any).createdAt;
    delete (duplicate as any).updatedAt;

    return this.createWorkflow(duplicate);
  }
};

// Automation Executions Service
export const automationExecutionService = {
  async getExecutions(workflowId?: string, limit = 100) {
    let query = supabase
      .from('automation_executions')
      .select('*')
      .order('executed_at', { ascending: false })
      .limit(limit);

    if (workflowId) {
      query = query.eq('workflow_id', workflowId);
    }

    const { data, error } = await query;
    if (error) handleSupabaseError(error);
    return (data || []).map(transformExecutionFromDB);
  },

  async createExecution(execution: Omit<AutomationExecution, 'id' | 'executedAt'>) {
    const { data, error } = await supabase
      .from('automation_executions')
      .insert({
        workflow_id: execution.workflowId,
        trigger_data: execution.triggerData as any,
        condition_result: execution.conditionResult as any,
        action_results: execution.actionResults as any,
        status: execution.status,
        error_message: execution.errorMessage || null,
        execution_time_ms: execution.executionTimeMs || null,
      })
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformExecutionFromDB(data) : null;
  },

  async updateExecution(id: string, updates: Partial<AutomationExecution>) {
    const updateData: Partial<Tables['automation_executions']['Update']> = {};

    if (updates.status !== undefined) updateData.status = updates.status;
    if (updates.errorMessage !== undefined) updateData.error_message = updates.errorMessage || null;
    if (updates.executionTimeMs !== undefined) updateData.execution_time_ms = updates.executionTimeMs || null;
    if (updates.actionResults !== undefined) updateData.action_results = updates.actionResults as any;
    if (updates.conditionResult !== undefined) updateData.condition_result = updates.conditionResult as any;

    const { data, error } = await supabase
      .from('automation_executions')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) handleSupabaseError(error);

    // Handle the case where no rows were updated or multiple rows were returned
    if (!data || data.length === 0) {
      console.warn(`No execution found with id: ${id}`);
      return null;
    }

    if (data.length > 1) {
      console.warn(`Multiple executions found with id: ${id}, using first one`);
    }

    return transformExecutionFromDB(data[0]);
  },

  async getExecutionStats(workflowId: string, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('automation_executions')
      .select('status, executed_at')
      .eq('workflow_id', workflowId)
      .gte('executed_at', startDate.toISOString());

    if (error) handleSupabaseError(error);

    const stats = {
      total: data?.length || 0,
      success: data?.filter(e => e.status === 'success').length || 0,
      failed: data?.filter(e => e.status === 'failed').length || 0,
      skipped: data?.filter(e => e.status === 'skipped').length || 0,
    };

    return {
      ...stats,
      successRate: stats.total > 0 ? (stats.success / stats.total) * 100 : 0,
    };
  }
};

// Automation Triggers Service
export const automationTriggerService = {
  async getTriggers(workflowId?: string) {
    let query = supabase
      .from('automation_triggers')
      .select('*')
      .order('created_at', { ascending: false });

    if (workflowId) {
      query = query.eq('workflow_id', workflowId);
    }

    const { data, error } = await query;
    if (error) handleSupabaseError(error);
    return (data || []).map(transformTriggerFromDB);
  },

  async createTrigger(trigger: Omit<AutomationTrigger, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase
      .from('automation_triggers')
      .insert({
        workflow_id: trigger.workflowId,
        trigger_type: trigger.triggerType,
        trigger_config: trigger.triggerConfig as any,
        is_active: trigger.isActive,
      })
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformTriggerFromDB(data) : null;
  },

  async updateTrigger(id: string, updates: Partial<AutomationTrigger>) {
    const updateData: Partial<Tables['automation_triggers']['Update']> = {};
    
    if (updates.triggerType !== undefined) updateData.trigger_type = updates.triggerType;
    if (updates.triggerConfig !== undefined) updateData.trigger_config = updates.triggerConfig as any;
    if (updates.isActive !== undefined) updateData.is_active = updates.isActive;

    const { data, error } = await supabase
      .from('automation_triggers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data ? transformTriggerFromDB(data) : null;
  },

  async deleteTrigger(id: string) {
    const { error } = await supabase
      .from('automation_triggers')
      .delete()
      .eq('id', id);

    if (error) handleSupabaseError(error);
  }
};
