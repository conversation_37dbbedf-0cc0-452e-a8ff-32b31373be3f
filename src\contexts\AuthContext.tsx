import React, { createContext, useContext, useEffect, useState } from 'react';
import { User as SupabaseUser, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { AuthContextType, AuthState, UserProfile } from '../types/auth';
import { userProfileService } from '../services/supabaseService';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
    initialized: false,
  });

  // Flag to track when we're in an actual login/logout operation
  const [isAuthOperation, setIsAuthOperation] = useState(false);
  // Flag to completely disable auth processing after successful login
  const [authProcessingDisabled, setAuthProcessingDisabled] = useState(false);

  const updateProfile = async (user: SupabaseUser) => {
    try {
      const profile = await userProfileService.getProfile(user.id);
      setState(prev => ({ ...prev, profile }));
    } catch (error) {
      console.error('Error fetching user profile:', error);

      // If profile doesn't exist, try to create it (fallback for edge cases)
      try {
        console.log('Attempting to create missing user profile...');
        const newProfile = await userProfileService.createProfile({
          id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
          role: 'user'
        });
        setState(prev => ({ ...prev, profile: newProfile }));
        console.log('User profile created successfully');
      } catch (createError) {
        console.error('Failed to create user profile:', createError);
        setState(prev => ({ ...prev, profile: null }));
      }
    }
  };

  const signIn = async (email: string, password: string) => {
    setState(prev => ({ ...prev, loading: true }));

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.user) {
        console.log('Manual login successful:', data.user.email);
        setState(prev => ({ ...prev, user: data.user }));
        await updateProfile(data.user);
        setState(prev => ({ ...prev, loading: false, initialized: true }));
      }
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false }));
      throw new Error(error.message || 'Failed to sign in');
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) throw error;

      if (data.user) {
        // Profile will be created automatically by the database trigger
        await updateProfile(data.user);
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign up');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  const signOut = async () => {
    setState(prev => ({ ...prev, loading: true }));

    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      console.log('Manual logout successful');
      setState({
        user: null,
        profile: null,
        loading: false,
        initialized: true,
      });
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false }));
      throw new Error(error.message || 'Failed to sign out');
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send reset email');
    }
  };

  const updateUserProfile = async (updates: Partial<UserProfile>) => {
    if (!state.user) throw new Error('No user logged in');
    
    try {
      const updatedProfile = await userProfileService.updateProfile(state.user.id, updates);
      setState(prev => ({ ...prev, profile: updatedProfile }));
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update profile');
    }
  };

  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
        }

        if (mounted) {
          if (session?.user) {
            setState(prev => ({ ...prev, user: session.user }));
            await updateProfile(session.user);
          }
          setState(prev => ({ ...prev, loading: false, initialized: true }));
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (mounted) {
          setState(prev => ({ ...prev, loading: false, initialized: true }));
        }
      }
    };

    getInitialSession();

    // Manual session management - completely disable auth state change listener
    console.log('Auth state change listener completely disabled to prevent database interference');

    // Manual session check on mount
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;

        if (session?.user) {
          console.log('Manual session check: user found', session.user.email);
          setState(prev => ({ ...prev, user: session.user }));
          await updateProfile(session.user);
        } else {
          console.log('Manual session check: no user found');
        }

        setState(prev => ({ ...prev, loading: false, initialized: true }));
      } catch (error) {
        console.error('Manual session check failed:', error);
        setState(prev => ({ ...prev, loading: false, initialized: true }));
      }
    };

    checkSession();

    return () => {
      mounted = false;
      // No subscription to unsubscribe - auth state change listener completely disabled
    };
  }, []);

  const value: AuthContextType = {
    ...state,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile: updateUserProfile,
    isAdmin: state.profile?.role === 'admin',
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
