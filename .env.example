# Supabase Configuration
# Get these values from your Supabase project dashboard > Settings > API
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Enable debug logging for troubleshooting
# VITE_DEBUG=true

# Example values (replace with your actual values):
# VITE_SUPABASE_URL=https://your-project-id.supabase.co
# VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Instructions:
# 1. Copy this file to .env.local
# 2. Replace the placeholder values with your actual Supabase credentials
# 3. Never commit .env.local to version control
