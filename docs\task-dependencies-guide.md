# Task Dependencies Guide

## Overview

The Task Dependencies system enables project managers to define relationships between tasks and automatically calculate optimal project schedules. This feature implements finish-to-start dependencies with automatic date propagation.

## 🔗 Key Features

### Dependency Types
- **Finish-to-Start (FS)**: The most common dependency type where a predecessor task must finish before a successor task can start
- **Start-to-Start (SS)**: The successor task starts when the predecessor task starts
- **Finish-to-Finish (FF)**: The successor task finishes when the predecessor task finishes
- **Start-to-Finish (SF)**: The successor task finishes when the predecessor task starts

### Automatic Date Calculations
- When a predecessor task's due date changes, all dependent tasks automatically adjust their start and due dates
- Maintains project timeline integrity and prevents scheduling conflicts
- Respects working days and business calendar settings

### Visual Dependency Tracking
- Gantt chart displays dependency relationships with connecting lines
- Visual indicators show task relationships and critical path
- Easy identification of bottlenecks and scheduling conflicts

## 📋 How to Use Dependencies

### Creating Dependencies

1. **Open Task Edit Form**
   - Navigate to any task and click "Edit"
   - Scroll to the "Dependencies" section

2. **Add Predecessor Tasks**
   - Click "Add Dependency"
   - Select the predecessor task from the dropdown
   - Choose the dependency type (FS, SS, FF, or SF)
   - Optionally set lag days for delays

3. **Save Changes**
   - Click "Save" to create the dependency
   - The system will automatically calculate new dates if needed

### Viewing Dependencies

1. **Gantt Chart View**
   - Navigate to the "Tasks" section
   - Click the "Gantt Chart" tab
   - Dependency connectors appear as lines between related tasks

2. **Task Details**
   - View dependencies in the task edit form
   - See both predecessor and successor relationships

### Managing Dependencies

1. **Removing Dependencies**
   - Open the task edit form
   - Click the "×" button next to any dependency
   - Save to remove the relationship

2. **Modifying Dependencies**
   - Remove existing dependencies and add new ones
   - The system will recalculate dates automatically

## ⚙️ Automatic Date Calculations

### How It Works

1. **Trigger Events**
   - When a predecessor task's due date changes
   - When a new dependency is created
   - When task durations are modified

2. **Calculation Logic**
   - Successor task start date = Predecessor due date + 1 day
   - Successor due date = New start date + original task duration
   - Cascades through entire dependency chain

3. **Conflict Detection**
   - System identifies when dependencies create impossible schedules
   - Highlights conflicts in the UI
   - Provides suggestions for resolution

### Working with Calculated Dates

- **Automatic Updates**: Dates update automatically when dependencies change
- **Manual Overrides**: You can manually adjust dates, but conflicts will be flagged
- **Duration Preservation**: Task durations are maintained during date calculations

## 🎯 Best Practices

### Planning Dependencies

1. **Start Early**: Define dependencies during project planning phase
2. **Keep It Simple**: Avoid overly complex dependency chains
3. **Review Regularly**: Check dependencies when project scope changes
4. **Use Gantt View**: Regularly review the visual representation

### Common Patterns

1. **Sequential Tasks**: A → B → C (linear workflow)
2. **Parallel Branches**: A → B, A → C (parallel work streams)
3. **Convergence**: A → C, B → C (multiple inputs to one task)

### Avoiding Issues

1. **Circular Dependencies**: System prevents A → B → A scenarios
2. **Over-constraining**: Don't create unnecessary dependencies
3. **Critical Path**: Monitor tasks that affect project completion date

## 🔧 Technical Implementation

### Database Structure

```sql
CREATE TABLE task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  predecessor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  successor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  dependency_type dependency_type DEFAULT 'finish_to_start',
  lag_days INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  UNIQUE(predecessor_task_id, successor_task_id)
);
```

### API Endpoints

- `GET /api/dependencies` - List all dependencies
- `POST /api/dependencies` - Create new dependency
- `DELETE /api/dependencies/:id` - Remove dependency
- `POST /api/dependencies/calculate-dates` - Trigger date recalculation

### Services

- **dependencyService**: Core dependency management
- **dateCalculationService**: Automatic date calculations
- **ganttService**: Visual representation logic

## 🐛 Troubleshooting

### Common Issues

1. **Dependencies Not Saving**
   - Check that both tasks exist and are accessible
   - Verify user has permission to edit both tasks
   - Ensure no circular dependencies are being created

2. **Dates Not Updating**
   - Verify the date calculation service is running
   - Check for conflicts in manual date overrides
   - Review dependency chain for issues

3. **Visual Issues in Gantt Chart**
   - Refresh the page to reload dependency connectors
   - Check that tasks have valid start and due dates
   - Verify Gantt chart is displaying the correct date range

### Error Messages

- **"Circular dependency detected"**: Cannot create A → B → A relationships
- **"Invalid task selection"**: Cannot create dependency to the same task
- **"Date conflict detected"**: Manual dates conflict with dependency requirements

## 🚀 Future Enhancements

### Planned Features

1. **Advanced Scheduling**
   - Resource-constrained scheduling
   - Critical path analysis
   - Advanced lag time configuration

2. **Enhanced Visualization**
   - Improved Gantt chart positioning
   - Dependency highlighting
   - Critical path visualization

### Current Limitations

- Gantt chart visual positioning needs optimization
- Limited lag time configuration in UI (basic implementation exists)
- Limited conflict resolution options

## 📚 Related Documentation

- [Functionality Guide](functionality-guide.md) - Complete feature overview
- [Database Schema](database-schema.md) - Technical database details
- [Setup Checklist](setup-checklist.md) - Installation verification
- [Troubleshooting](troubleshooting.md) - General issue resolution

For technical support or feature requests, please refer to the main project documentation or create an issue in the project repository.
