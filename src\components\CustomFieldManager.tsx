import React, { useState, useEffect } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { CustomField, CustomFieldType, CustomFieldFormData } from '../types/customFields';
import { Plus, Edit2, Trash2, GripVertical, Settings, AlertCircle, Check, X } from 'lucide-react';

export default function CustomFieldManager() {
  const {
    customFields,
    loading,
    loadCustomFields,
    addCustomField,
    updateCustomField,
    deleteCustomField,
    reorderCustomFields
  } = useSupabaseStore();

  const [showForm, setShowForm] = useState(false);
  const [editingField, setEditingField] = useState<CustomField | null>(null);
  const [draggedField, setDraggedField] = useState<CustomField | null>(null);
  const [formData, setFormData] = useState<CustomFieldFormData>({
    name: '',
    label: '',
    fieldType: 'text',
    isRequired: false,
    dropdownOptions: [],
    defaultValue: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [newDropdownOption, setNewDropdownOption] = useState('');

  useEffect(() => {
    loadCustomFields();
  }, [loadCustomFields]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Name validation
    if (!formData.name.trim()) {
      errors.name = 'Field name is required';
    } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(formData.name)) {
      errors.name = 'Field name must start with a letter and contain only letters, numbers, and underscores';
    } else if (formData.name.length > 50) {
      errors.name = 'Field name must be 50 characters or less';
    }

    // Label validation
    if (!formData.label.trim()) {
      errors.label = 'Field label is required';
    } else if (formData.label.length > 100) {
      errors.label = 'Field label must be 100 characters or less';
    }

    // Dropdown options validation
    if (formData.fieldType === 'dropdown') {
      if (formData.dropdownOptions.length === 0) {
        errors.dropdownOptions = 'Dropdown fields must have at least one option';
      } else if (formData.dropdownOptions.length > 50) {
        errors.dropdownOptions = 'Dropdown fields cannot have more than 50 options';
      }
    }

    // Check for duplicate names (excluding current field when editing)
    const existingField = (customFields || []).find(field =>
      field.name === formData.name &&
      field.isActive &&
      (!editingField || field.id !== editingField.id)
    );
    if (existingField) {
      errors.name = 'A field with this name already exists';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      if (editingField) {
        await updateCustomField(editingField.id, {
          label: formData.label,
          field_type: formData.fieldType,
          is_required: formData.isRequired,
          dropdown_options: formData.fieldType === 'dropdown' ? formData.dropdownOptions : undefined,
          default_value: formData.defaultValue || undefined,
          description: formData.description || undefined,
        });
      } else {
        await addCustomField({
          name: formData.name,
          label: formData.label,
          field_type: formData.fieldType,
          is_required: formData.isRequired,
          dropdown_options: formData.fieldType === 'dropdown' ? formData.dropdownOptions : undefined,
          default_value: formData.defaultValue || undefined,
          description: formData.description || undefined,
          sort_order: (customFields || []).filter(f => f.isActive).length,
        });
      }

      resetForm();
    } catch (error) {
      console.error('Failed to save custom field:', error);
      alert('Failed to save custom field. Please try again.');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      label: '',
      fieldType: 'text',
      isRequired: false,
      dropdownOptions: [],
      defaultValue: '',
      description: '',
    });
    setFormErrors({});
    setEditingField(null);
    setShowForm(false);
    setNewDropdownOption('');
  };

  const handleEdit = (field: CustomField) => {
    setFormData({
      name: field.name,
      label: field.label,
      fieldType: field.fieldType,
      isRequired: field.isRequired,
      dropdownOptions: field.dropdownOptions || [],
      defaultValue: field.defaultValue || '',
      description: field.description || '',
    });
    setEditingField(field);
    setShowForm(true);
  };

  const handleDelete = async (field: CustomField) => {
    if (window.confirm(`Are you sure you want to delete the custom field "${field.label}"? This action cannot be undone.`)) {
      try {
        await deleteCustomField(field.id);
      } catch (error) {
        console.error('Failed to delete custom field:', error);
        alert('Failed to delete custom field. Please try again.');
      }
    }
  };

  const handleDragStart = (field: CustomField) => {
    setDraggedField(field);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (targetField: CustomField) => {
    if (!draggedField || draggedField.id === targetField.id) return;

    const activeFields = (customFields || []).filter(f => f.isActive).sort((a, b) => a.sortOrder - b.sortOrder);
    const draggedIndex = activeFields.findIndex(f => f.id === draggedField.id);
    const targetIndex = activeFields.findIndex(f => f.id === targetField.id);

    if (draggedIndex === -1 || targetIndex === -1) return;

    // Reorder the fields
    const reorderedFields = [...activeFields];
    const [removed] = reorderedFields.splice(draggedIndex, 1);
    reorderedFields.splice(targetIndex, 0, removed);

    try {
      await reorderCustomFields(reorderedFields.map(f => f.id));
    } catch (error) {
      console.error('Failed to reorder custom fields:', error);
      alert('Failed to reorder custom fields. Please try again.');
    }

    setDraggedField(null);
  };

  const addDropdownOption = () => {
    if (newDropdownOption.trim() && !formData.dropdownOptions.includes(newDropdownOption.trim())) {
      setFormData({
        ...formData,
        dropdownOptions: [...formData.dropdownOptions, newDropdownOption.trim()]
      });
      setNewDropdownOption('');
    }
  };

  const removeDropdownOption = (index: number) => {
    setFormData({
      ...formData,
      dropdownOptions: formData.dropdownOptions.filter((_, i) => i !== index)
    });
  };

  const activeFields = (customFields || []).filter(f => f.isActive).sort((a, b) => a.sortOrder - b.sortOrder);

  if (loading.customFields) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-white">Loading custom fields...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white flex items-center gap-2">
          <Settings className="w-6 h-6" />
          Custom Fields
        </h2>
        <button
          onClick={() => setShowForm(true)}
          className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Custom Field
        </button>
      </div>

      {/* Custom Fields List */}
      <div className="space-y-2 mb-6">
        {activeFields.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No custom fields created yet.</p>
            <p className="text-sm">Click "Add Custom Field" to get started.</p>
          </div>
        ) : (
          activeFields.map((field) => (
            <div
              key={field.id}
              draggable
              onDragStart={() => handleDragStart(field)}
              onDragOver={handleDragOver}
              onDrop={() => handleDrop(field)}
              className="flex items-center justify-between p-4 bg-gray-800 rounded-lg shadow-sm cursor-move text-white hover:bg-gray-750 transition-colors"
            >
              <div className="flex items-center gap-3">
                <GripVertical className="w-5 h-5 text-gray-400" />
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{field.label}</h3>
                    <span className="text-xs px-2 py-1 bg-gray-700 rounded">
                      {field.fieldType}
                    </span>
                    {field.isRequired && (
                      <span className="text-xs px-2 py-1 bg-red-600 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400">
                    Field name: {field.name}
                    {field.description && ` • ${field.description}`}
                  </p>
                  {field.fieldType === 'dropdown' && field.dropdownOptions && (
                    <p className="text-xs text-gray-500 mt-1">
                      Options: {field.dropdownOptions.join(', ')}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => handleEdit(field)}
                  className="p-2 hover:bg-gray-700 rounded text-blue-400"
                >
                  <Edit2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(field)}
                  className="p-2 hover:bg-gray-700 rounded text-red-400"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Custom Field Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={resetForm}>
          <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-md mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                {editingField ? 'Edit Custom Field' : 'Add Custom Field'}
              </h3>
              <button onClick={resetForm} className="p-1 hover:bg-gray-100 rounded">
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Field Name */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  Field Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  disabled={!!editingField} // Can't change name when editing
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="e.g., priority_score"
                />
                {formErrors.name && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {formErrors.name}
                  </p>
                )}
              </div>

              {/* Field Label */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  Field Label <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.label}
                  onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Priority Score"
                />
                {formErrors.label && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {formErrors.label}
                  </p>
                )}
              </div>

              {/* Field Type */}
              <div>
                <label className="block text-sm font-medium mb-1">Field Type</label>
                <select
                  value={formData.fieldType}
                  onChange={(e) => setFormData({ ...formData, fieldType: e.target.value as CustomFieldType })}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="dropdown">Dropdown</option>
                </select>
              </div>

              {/* Dropdown Options */}
              {formData.fieldType === 'dropdown' && (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Dropdown Options <span className="text-red-500">*</span>
                  </label>
                  <div className="space-y-2">
                    {formData.dropdownOptions.map((option, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <span className="flex-1 px-3 py-2 bg-gray-50 rounded border">
                          {option}
                        </span>
                        <button
                          type="button"
                          onClick={() => removeDropdownOption(index)}
                          className="p-2 text-red-500 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newDropdownOption}
                        onChange={(e) => setNewDropdownOption(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDropdownOption())}
                        className="flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Add option..."
                      />
                      <button
                        type="button"
                        onClick={addDropdownOption}
                        className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  {formErrors.dropdownOptions && (
                    <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.dropdownOptions}
                    </p>
                  )}
                </div>
              )}

              {/* Default Value */}
              <div>
                <label className="block text-sm font-medium mb-1">Default Value</label>
                {formData.fieldType === 'dropdown' ? (
                  <select
                    value={formData.defaultValue}
                    onChange={(e) => setFormData({ ...formData, defaultValue: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">No default</option>
                    {formData.dropdownOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={formData.fieldType === 'number' ? 'number' : formData.fieldType === 'date' ? 'date' : 'text'}
                    value={formData.defaultValue}
                    onChange={(e) => setFormData({ ...formData, defaultValue: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Optional default value"
                  />
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows={2}
                  placeholder="Optional description for this field"
                />
              </div>

              {/* Required Checkbox */}
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isRequired"
                  checked={formData.isRequired}
                  onChange={(e) => setFormData({ ...formData, isRequired: e.target.checked })}
                  className="rounded"
                />
                <label htmlFor="isRequired" className="text-sm">
                  This field is required
                </label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-2 pt-4">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
                >
                  <Check className="w-4 h-4" />
                  {editingField ? 'Update' : 'Create'} Field
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}