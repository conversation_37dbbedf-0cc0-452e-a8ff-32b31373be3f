# Setup Checklist

Use this checklist to ensure your TaskFlow project is properly set up and configured.

## 📋 Pre-Setup Requirements

- [ ] Node.js 18+ installed
- [ ] npm or yarn package manager
- [ ] Supabase account created
- [ ] Modern web browser (Chrome, Firefox, Safari, Edge)

## 🚀 Project Setup

### 1. Repository Setup
- [ ] Repository cloned locally
- [ ] Dependencies installed (`npm install`)
- [ ] `.env.local` file created from `.env.example`

### 2. Supabase Project Creation
- [ ] New Supabase project created
- [ ] Project name set (e.g., "taskflow-pm")
- [ ] Database password saved securely
- [ ] Region selected (closest to users)

### 3. Environment Configuration
- [ ] `VITE_SUPABASE_URL` set in `.env.local`
- [ ] `VITE_SUPABASE_ANON_KEY` set in `.env.local`
- [ ] Environment variables verified (no trailing spaces)

## 🗄️ Database Setup

### SQL Files Execution (CRITICAL ORDER)
- [ ] `supabase-schema.sql` - Core database schema
- [ ] `supabase-rls-policies.sql` - Security policies
- [ ] `supabase-triggers.sql` - User creation triggers
- [ ] `supabase-add-subtasks.sql` - Subtask support
- [ ] `supabase-task-dependencies.sql` - Task dependencies functionality
- [ ] `supabase-custom-fields.sql` - Custom fields functionality

### Database Verification
- [ ] All tables created successfully
- [ ] RLS enabled on all tables
- [ ] Triggers active and working
- [ ] No SQL errors in execution

## 👤 User Management Setup

### Admin User Creation
- [ ] Admin user created in Supabase Auth
- [ ] Email: `<EMAIL>`
- [ ] Password set and saved
- [ ] Email confirmed
- [ ] Admin role automatically assigned

### User Groups Creation
- [ ] Default user groups created:
  - [ ] Developers
  - [ ] Project Managers
  - [ ] Campaign Managers
  - [ ] Team Leaders

### Skillset Groups
- [ ] Skillset groups available for assignment
- [ ] Skills properly categorized

## 🔐 Security Verification

### Authentication
- [ ] User registration works
- [ ] Email confirmation required
- [ ] User profiles created automatically
- [ ] Login/logout functionality working

### Permissions
- [ ] Admin can access all features
- [ ] Admin can manage other users
- [ ] Regular users have appropriate restrictions
- [ ] RLS policies enforcing data isolation

## ✅ Feature Testing

### Core Functionality
- [ ] Task creation and editing
- [ ] Project and folder management
- [ ] Kanban board drag-and-drop
- [ ] User assignment to tasks
- [ ] Task dependencies creation and management

### Advanced Features
- [ ] Rich text editor for task descriptions (formatting, lists, links, code)
- [ ] Subtask creation and management with rich text support
- [ ] Comments on tasks and subtasks
- [ ] Custom fields creation and usage
- [ ] User capacity management
- [ ] Skillset assignment to users
- [ ] Group management
- [ ] Gantt chart with dependency visualization

### Data Persistence
- [ ] Data saves correctly to database
- [ ] Page refresh preserves data
- [ ] Multiple users can collaborate
- [ ] Changes sync across sessions

## 🔧 Development Environment

### Application Startup
- [ ] Development server starts (`npm run dev`)
- [ ] Application loads at `http://localhost:5173`
- [ ] No console errors on startup
- [ ] All routes accessible

### Browser Testing
- [ ] Application works in Chrome
- [ ] Application works in Firefox
- [ ] Application works in Safari (if on Mac)
- [ ] Responsive design on mobile

## 🐛 Troubleshooting Verification

### Common Issues Resolved
- [ ] User registration creates profiles
- [ ] Admin can update user profiles
- [ ] Subtasks save and load correctly
- [ ] No infinite loops in console
- [ ] No 406 errors on profile updates

### Error Handling
- [ ] Graceful error messages shown
- [ ] Network errors handled properly
- [ ] Invalid data rejected appropriately
- [ ] Loading states displayed

## 📊 Performance Check

### Database Performance
- [ ] Queries execute quickly (<1 second)
- [ ] Large datasets load efficiently
- [ ] Indexes working properly
- [ ] No unnecessary API calls

### Application Performance
- [ ] UI responds quickly to interactions
- [ ] No memory leaks detected
- [ ] Smooth animations and transitions
- [ ] Efficient state management

## 🔍 Final Verification

### Admin Dashboard Test
- [ ] Login as admin user
- [ ] Access Users tab
- [ ] Create/edit user profiles
- [ ] Assign groups and skillsets
- [ ] Manage user capacities
- [ ] Create and manage custom fields

### User Workflow Test
- [ ] Create new project
- [ ] Add folders and tasks
- [ ] Create subtasks with comments
- [ ] Use custom fields in tasks and subtasks
- [ ] Assign tasks to users
- [ ] Set up task dependencies
- [ ] Test kanban board functionality
- [ ] View Gantt chart with dependencies

### Multi-User Test
- [ ] Create additional test user
- [ ] Login with different users
- [ ] Verify data isolation
- [ ] Test collaborative features

## 📝 Documentation Review

### Setup Documentation
- [ ] README.md reviewed and accurate
- [ ] SUPABASE_SETUP.md followed completely
- [ ] All SQL files documented
- [ ] Environment variables documented

### Feature Documentation
- [ ] Database schema documented
- [ ] API endpoints documented
- [ ] Component structure clear
- [ ] Troubleshooting guide available

## 🎯 Production Readiness

### Security Checklist
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] User permissions properly configured
- [ ] No sensitive data in logs

### Deployment Preparation
- [ ] Build process works (`npm run build`)
- [ ] Production environment variables set
- [ ] Supabase project configured for production
- [ ] Domain and SSL configured (if applicable)

## ✅ Setup Complete

Once all items are checked:
- [ ] Application fully functional
- [ ] All features tested and working
- [ ] Documentation up to date
- [ ] Ready for development/production use

## 🆘 If Issues Persist

If you encounter problems after following this checklist:

1. **Review the [Troubleshooting Guide](./troubleshooting.md)**
2. **Check Supabase dashboard logs**
3. **Verify all SQL files ran successfully**
4. **Ensure environment variables are correct**
5. **Create an issue with detailed error information**

Remember: Most issues are caused by incorrect SQL file execution order or missing environment variables.
