# Multi-User Troubleshooting Guide

## 🚨 Common Issues and Quick Fixes

### Issue: "Cannot Update Task" (406 Errors)

**Symptoms:**
- User gets 406 (Not Acceptable) errors when trying to move or edit tasks
- <PERSON><PERSON><PERSON> shows "Version conflict" messages
- Changes don't save despite multiple attempts

**Root Cause:** User lacks permission due to Row Level Security (RLS) policy

**Quick Diagnosis:**
```javascript
// Check task ownership and assignments
window.supabase.from('tasks').select('id, title, created_by, assigned_user_id, owner_id, assigned_users').eq('id', 'TASK_ID_HERE').single().then(console.log);

// Check current user
window.supabase.auth.getUser().then(console.log);
```

**Solutions:**
1. **Assign user to task** (Recommended)
2. **Grant admin role** (For system administrators)
3. **Set user as task owner**
4. **Add to assigned_users array**

### Issue: Real-time Updates Not Syncing

**Symptoms:**
- Changes made by one user don't appear for others
- Users see different data states
- Manual refresh shows correct data

**Quick Diagnosis:**
```javascript
// Check real-time connection
window.useSupabaseStore.getState().testRealtimeConnection();

// Expected output: "✅ All real-time subscriptions working"
```

**Solutions:**
1. **Check network connection**
2. **Refresh browser** (F5)
3. **Force data refresh**: `window.useSupabaseStore.getState().forceRefresh()`
4. **Enable polling fallback**: `window.useSupabaseStore.getState().startPolling()`

### Issue: Version Conflicts in Concurrent Updates

**Symptoms:**
- Multiple users editing same task simultaneously
- "Version conflict detected, retry X/3" messages
- Updates eventually succeed but with delays

**This is Normal Behavior** - The system is working correctly!

**What's Happening:**
- Optimistic locking prevents data corruption
- Automatic retry resolves conflicts
- Users see eventual consistency

**No Action Needed** unless retries consistently fail.

## 🔧 Admin Tools and Commands

### Grant Admin Access
```sql
-- Make user an admin (run in Supabase SQL editor)
UPDATE user_profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### Assign User to Task
```sql
-- Assign user as primary assignee
UPDATE tasks 
SET assigned_user_id = 'USER_ID_HERE'
WHERE id = 'TASK_ID_HERE';

-- Add user to assigned users array
UPDATE tasks 
SET assigned_users = assigned_users || ARRAY['USER_ID_HERE']
WHERE id = 'TASK_ID_HERE';
```

### Check User Permissions
```javascript
// Get user role and ID
window.supabase.from('user_profiles').select('id, email, name, role').then(console.log);

// Check specific user's tasks
window.supabase.from('tasks').select('id, title, created_by, assigned_user_id').eq('created_by', 'USER_ID_HERE').then(console.log);
```

## 🔍 Debugging Steps

### Step 1: Identify the User
```javascript
// Get current user info
const user = await window.supabase.auth.getUser();
console.log('Current user:', user.data.user?.id);

// Get user profile
const profile = await window.supabase.from('user_profiles').select('*').eq('id', user.data.user?.id).single();
console.log('User profile:', profile.data);
```

### Step 2: Check Task Permissions
```javascript
// Check specific task
const taskId = 'TASK_ID_HERE';
const task = await window.supabase.from('tasks').select('*').eq('id', taskId).single();
console.log('Task details:', task.data);

// Check if user can update this task
const userId = user.data.user?.id;
const canUpdate = 
  task.data.created_by === userId ||
  task.data.assigned_user_id === userId ||
  task.data.owner_id === userId ||
  task.data.assigned_users?.includes(userId) ||
  profile.data.role === 'admin';

console.log('Can user update task?', canUpdate);
```

### Step 3: Test Real-time Connection
```javascript
// Check subscription status
window.useSupabaseStore.getState().testRealtimeConnection();

// Check active channels
const channels = window.supabase.getChannels();
console.log('Active channels:', channels.length);
channels.forEach(ch => console.log(`${ch.topic}: ${ch.state}`));
```

### Step 4: Force Refresh if Needed
```javascript
// Refresh all data
await window.useSupabaseStore.getState().forceRefresh();

// Refresh specific task
await window.useSupabaseStore.getState().refreshTask('TASK_ID_HERE');
```

## 📋 Permission Matrix

| Action | Creator | Assigned User | Owner | In Assigned Array | Admin |
|--------|---------|---------------|-------|-------------------|-------|
| View Task | ✅ | ✅ | ✅ | ✅ | ✅ |
| Update Task | ✅ | ✅ | ✅ | ✅ | ✅ |
| Delete Task | ✅ | ❌ | ❌ | ❌ | ✅ |
| View Project | ✅ | ✅ | ✅ | ✅ | ✅ |
| Update Project | ✅ | ❌ | ❌ | ❌ | ✅ |
| Create Column | ✅ | ✅ | ✅ | ✅ | ✅ |
| Update Column | ✅ | ❌ | ❌ | ❌ | ✅ |

## 🛠️ Emergency Procedures

### Complete System Reset (Last Resort)
```javascript
// Clear all local data and force refresh
localStorage.clear();
sessionStorage.clear();
window.location.reload();
```

### Database Health Check
```sql
-- Check for orphaned tasks
SELECT id, title, created_by, assigned_user_id 
FROM tasks 
WHERE created_by NOT IN (SELECT id FROM user_profiles);

-- Check user roles
SELECT email, name, role, created_at 
FROM user_profiles 
ORDER BY created_at;

-- Check version consistency
SELECT id, title, version, updated_at 
FROM tasks 
ORDER BY updated_at DESC 
LIMIT 10;
```

### Re-enable Real-time (If Broken)
```javascript
// Restart real-time subscriptions
window.useSupabaseStore.getState().setupRealtimeSubscriptions();

// Enable polling as fallback
window.useSupabaseStore.getState().startPolling();
```

## 📞 When to Contact Support

Contact system administrator if:

1. **Persistent 406 errors** after checking permissions
2. **Real-time completely broken** across all users
3. **Data corruption** or inconsistent states
4. **Performance issues** with large teams
5. **Security concerns** about user access

## 📚 Related Documentation

- [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md) - Complete security and collaboration reference
- [Resource Capacity Management Guide](./resource-capacity-management-guide.md) - Team resource planning
- [Database Schema](../supabase-schema.sql) - Complete database structure and policies
