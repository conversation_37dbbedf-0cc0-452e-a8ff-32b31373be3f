// formatValue.ts

interface ColumnType {
    id: string;
    title: string;
  }
  
  interface UserType {
    id: string;
    name: string;
  }

export function formatValue(field: string, value: string, columns: ColumnType[], users: UserType[]) {
    try {
        let parsed: string;
        try {
            parsed = JSON.parse(value);
         } catch {
        parsed = value;
      }
  
      // Handle status field to show column labels
      if (field === 'status') {
        const column = columns.find(col => col.id === parsed);
        return column ? column.title : parsed;
      }
  
      // Handle user ID fields
      if (field === 'assignedUserId' || field === 'userId') {
        const user = users.find(u => u.id === parsed);
        return user ? user.name : parsed;
      }
  
      // Handle assignedUsers field
      if (field === 'assignedUsers' && Array.isArray(parsed)) {
        const userNames = parsed.map((id: string) => {
          const user = users.find(u => u.id === id);
          return user ? user.name : id;
        });
        return JSON.stringify(userNames);
      }
  
      if (typeof parsed === 'object') {
        return JSON.stringify(parsed);
      }
      return parsed;
    } catch {
      return value;
    }
  }
  