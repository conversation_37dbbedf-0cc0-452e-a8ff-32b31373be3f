// Automation system type definitions

export type TriggerType = 
  | 'task_created'
  | 'task_updated'
  | 'task_status_changed'
  | 'task_assigned'
  | 'task_due_date_reached'
  | 'comment_added'
  | 'project_created'
  | 'project_updated'
  | 'custom_event'
  | 'schedule'
  | 'webhook';

export type ConditionOperator = 
  | 'equals'
  | 'not_equals'
  | 'contains'
  | 'not_contains'
  | 'greater_than'
  | 'less_than'
  | 'greater_than_or_equal'
  | 'less_than_or_equal'
  | 'is_empty'
  | 'is_not_empty'
  | 'in'
  | 'not_in';

export type LogicOperator = 'AND' | 'OR';

export type ActionType = 
  | 'update_task_status'
  | 'assign_task'
  | 'reassign_task'
  | 'update_task_field'
  | 'add_comment'
  | 'send_notification'
  | 'send_email'
  | 'create_task'
  | 'create_subtask'
  | 'move_task'
  | 'update_due_date'
  | 'add_tag'
  | 'remove_tag'
  | 'update_priority';

export interface TriggerConfig {
  type: TriggerType;
  entityType?: 'task' | 'project' | 'comment' | 'user';
  entityId?: string;
  fieldName?: string;
  scheduleConfig?: {
    frequency: 'once' | 'daily' | 'weekly' | 'monthly';
    time?: string;
    dayOfWeek?: number;
    dayOfMonth?: number;
    timezone?: string;
  };
  webhookConfig?: {
    url: string;
    secret?: string;
  };
  customEventConfig?: {
    eventName: string;
    eventData?: Record<string, any>;
  };
}

export interface Condition {
  id: string;
  field: string;
  operator: ConditionOperator;
  value: any;
  valueType: 'static' | 'dynamic' | 'field_reference';
  entityType?: 'task' | 'project' | 'user' | 'comment';
}

export interface ConditionGroup {
  id: string;
  logic: LogicOperator;
  conditions: Condition[];
  groups: ConditionGroup[];
}

export interface ConditionConfig {
  rootGroup: ConditionGroup;
}

export interface ActionConfig {
  type: ActionType;
  targetField?: string;
  value?: any;
  valueType?: 'static' | 'dynamic' | 'field_reference';
  targetEntityType?: 'task' | 'project' | 'user';
  targetEntityId?: string;
  templateConfig?: {
    subject?: string;
    body?: string;
    recipients?: string[];
  };
  assignmentConfig?: {
    userId?: string;
    groupId?: string;
    assignmentType?: 'replace' | 'add' | 'remove';
  };
  taskCreationConfig?: {
    title: string;
    description?: string;
    projectId?: string;
    folderId?: string;
    priority?: 'low' | 'medium' | 'high';
    dueDate?: string;
    assignedUsers?: string[];
    assignedGroups?: string[];
  };
}

export interface AutomationWorkflow {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  isTemplate: boolean;
  createdBy: string;
  projectId?: string;
  folderId?: string;
  triggerConfig: TriggerConfig;
  conditionConfig?: ConditionConfig;
  actionConfig: ActionConfig[];
  executionCount: number;
  lastExecutedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AutomationExecution {
  id: string;
  workflowId: string;
  triggerData: Record<string, any>;
  conditionResult?: {
    passed: boolean;
    evaluationDetails: Record<string, any>;
  };
  actionResults: {
    actionType: ActionType;
    success: boolean;
    result?: any;
    error?: string;
  }[];
  status: 'pending' | 'success' | 'failed' | 'skipped';
  errorMessage?: string;
  executionTimeMs?: number;
  executedAt: string;
}

export interface AutomationTrigger {
  id: string;
  workflowId: string;
  triggerType: 'webhook' | 'schedule' | 'manual';
  triggerConfig: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// UI-specific types for the workflow builder
export interface WorkflowNode {
  id: string;
  type: 'trigger' | 'condition' | 'action';
  position: { x: number; y: number };
  data: TriggerConfig | ConditionConfig | ActionConfig;
  connections: string[];
}

export interface WorkflowCanvas {
  nodes: WorkflowNode[];
  connections: Array<{
    id: string;
    sourceId: string;
    targetId: string;
    type: 'success' | 'failure' | 'default';
  }>;
}

// Field definitions for dynamic form building
export interface FieldDefinition {
  name: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect' | 'user' | 'group';
  options?: Array<{ value: string; label: string }>;
  required?: boolean;
  description?: string;
  entityType?: 'task' | 'project' | 'user' | 'comment';
}

// Available fields for different entity types
export const TASK_FIELDS: FieldDefinition[] = [
  { name: 'title', label: 'Title', type: 'text', entityType: 'task' },
  { name: 'description', label: 'Description', type: 'text', entityType: 'task' },
  { name: 'status', label: 'Status', type: 'select', entityType: 'task' },
  { name: 'priority', label: 'Priority', type: 'select', entityType: 'task' },
  { name: 'assignedUsers', label: 'Assigned Users', type: 'multiselect', entityType: 'task' },
  { name: 'assignedGroups', label: 'Assigned Groups', type: 'multiselect', entityType: 'task' },
  { name: 'dueDate', label: 'Due Date', type: 'date', entityType: 'task' },
  { name: 'startDate', label: 'Start Date', type: 'date', entityType: 'task' },
  { name: 'tags', label: 'Tags', type: 'multiselect', entityType: 'task' },
  { name: 'projectId', label: 'Project', type: 'select', entityType: 'task' },
  { name: 'folderId', label: 'Folder', type: 'select', entityType: 'task' },
];

export const PROJECT_FIELDS: FieldDefinition[] = [
  { name: 'name', label: 'Name', type: 'text', entityType: 'project' },
  { name: 'description', label: 'Description', type: 'text', entityType: 'project' },
  { name: 'color', label: 'Color', type: 'text', entityType: 'project' },
  { name: 'startDate', label: 'Start Date', type: 'date', entityType: 'project' },
  { name: 'endDate', label: 'End Date', type: 'date', entityType: 'project' },
  { name: 'folderId', label: 'Folder', type: 'select', entityType: 'project' },
];

export const USER_FIELDS: FieldDefinition[] = [
  { name: 'name', label: 'Name', type: 'text', entityType: 'user' },
  { name: 'email', label: 'Email', type: 'text', entityType: 'user' },
  { name: 'role', label: 'Role', type: 'select', entityType: 'user' },
  { name: 'groupId', label: 'Group', type: 'select', entityType: 'user' },
];

// Automation execution context
export interface AutomationContext {
  triggerData: Record<string, any>;
  currentUser: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  entities: {
    task?: any;
    project?: any;
    user?: any;
    comment?: any;
  };
  timestamp: string;
}
