import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://vurejcredrimoswoecnt.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // You'll need to set this

if (!supabaseServiceKey) {
  console.error('SUPABASE_SERVICE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applySchema() {
  try {
    console.log('Reading SQL file...');
    const sql = fs.readFileSync('./supabase-task-durations.sql', 'utf8');
    
    console.log('Applying schema changes...');
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error applying schema:', error);
      process.exit(1);
    }
    
    console.log('Schema applied successfully!');
    console.log('Result:', data);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

applySchema();
