# Custom Fields User Guide

## Overview

Custom Fields allow you to extend tasks and subtasks with additional data fields tailored to your organization's needs. You can create text, number, date, and dropdown fields that appear in all task and subtask forms.

## 🎯 Key Features

- **Multiple Field Types**: Text, Number, Date, and Dropdown fields
- **Admin Management**: Only admin users can create and manage field definitions
- **Universal Application**: Custom fields apply to both tasks and subtasks
- **Automatic Loading**: Fields are available immediately without manual refresh
- **Data Persistence**: Values are stored securely in the database
- **Validation**: Built-in validation for required fields and data types

## 👨‍💼 For Administrators

### Creating Custom Fields

1. **Navigate to Admin Settings**
   - Click on your profile in the top-right corner
   - Select "Admin Settings" from the dropdown
   - Click on "Custom Fields" in the sidebar

2. **Add a New Custom Field**
   - Click the "Add Custom Field" button
   - Fill in the required information:
     - **Name**: Unique identifier (letters, numbers, underscores only)
     - **Label**: Display name shown to users
     - **Field Type**: Choose from Text, Number, Date, or Dropdown
     - **Required**: Check if the field must be filled
     - **Description**: Optional help text for users

3. **Field Type Specific Options**
   - **Text Fields**: Simple text input
   - **Number Fields**: Numeric input with validation
   - **Date Fields**: Date picker with proper formatting
   - **Dropdown Fields**: Provide comma-separated options

4. **Save and Order**
   - Click "Create Custom Field" to save
   - Drag and drop fields to reorder them
   - Fields appear in forms in the order you set

### Managing Existing Fields

- **Edit Fields**: Click the edit icon to modify field properties
- **Delete Fields**: Click the delete icon to remove fields (soft delete)
- **Reorder Fields**: Drag and drop to change the display order
- **View Usage**: See which tasks use specific custom field values

### Best Practices

- **Naming Convention**: Use clear, descriptive names (e.g., "client_priority", "estimated_hours")
- **Field Labels**: Make labels user-friendly (e.g., "Client Priority", "Estimated Hours")
- **Required Fields**: Only mark fields as required if truly necessary
- **Dropdown Options**: Keep dropdown lists concise and relevant
- **Field Descriptions**: Provide helpful descriptions for complex fields

## 👥 For Users

### Using Custom Fields in Tasks

1. **Creating a New Task**
   - Open the task creation form
   - Scroll down to the "Custom Fields" section
   - Click to expand the section
   - Fill in any custom field values as needed
   - Required fields are marked with an asterisk (*)

2. **Editing Existing Tasks**
   - Open any task for editing
   - Find the "Custom Fields" expandable section
   - Modify values as needed
   - Save the task to persist changes

3. **Working with Subtasks**
   - Custom fields work identically in subtask forms
   - Each subtask has its own custom field values
   - Values are independent between tasks and subtasks

### Field Types and Input Methods

- **Text Fields**: Type any text content
- **Number Fields**: Enter numeric values only
- **Date Fields**: Use the date picker or type YYYY-MM-DD format
- **Dropdown Fields**: Select from predefined options

### Validation and Error Handling

- **Required Fields**: Must be filled before saving
- **Data Type Validation**: Fields validate appropriate data types
- **Error Messages**: Clear feedback for validation issues
- **Auto-Save**: Values are preserved during form editing

## 🔧 Technical Details

### Data Storage

- Custom field definitions are stored in the `custom_fields` table
- Field values are stored as JSONB in the `custom_field_values` column of tasks
- This provides flexibility and performance for querying

### Security

- **Admin Only Management**: Only admin users can create/edit field definitions
- **Row Level Security**: Database-level security protects field definitions
- **Data Validation**: Both client and server-side validation
- **Audit Trail**: Changes are tracked in task history

### Performance

- **Automatic Loading**: Fields load with the application startup
- **Efficient Storage**: JSONB provides optimal storage and querying
- **Indexed Access**: Database indexes support fast field access
- **Minimal Overhead**: No performance impact on existing functionality

## 🚀 Advanced Usage

### Future Enhancements

The custom fields system is designed to support future enhancements:

- **Filtering**: Filter tasks by custom field values
- **Reporting**: Include custom fields in reports and analytics
- **Bulk Operations**: Mass update custom field values
- **Field Dependencies**: Conditional fields based on other values
- **Import/Export**: Include custom fields in data operations

### API Integration

Custom fields are fully integrated into the application's data model:

- Available in all task and subtask operations
- Included in data exports and backups
- Accessible via future API endpoints
- Compatible with automation systems

## 🐛 Troubleshooting

### Common Issues

1. **Custom Fields Not Appearing**
   - Ensure you have admin access to create fields
   - Check that fields are marked as active
   - Refresh the browser if fields don't appear immediately

2. **Validation Errors**
   - Check required field constraints
   - Verify data types match field requirements
   - Ensure dropdown values match available options

3. **Saving Issues**
   - Check browser console for error messages
   - Verify database connection is stable
   - Contact admin if persistent issues occur

### Database Issues

If you encounter database errors:

1. **"Case not found" errors**: Contact your administrator to remove validation triggers
2. **Permission errors**: Verify your user role and permissions
3. **Data corruption**: Check the database logs and contact support

## 📝 Examples

### Example Custom Fields

1. **Client Priority**
   - Type: Dropdown
   - Options: "Low", "Medium", "High", "Critical"
   - Use: Categorize tasks by client importance

2. **Estimated Hours**
   - Type: Number
   - Required: Yes
   - Use: Track time estimates for planning

3. **Due Date Reason**
   - Type: Text
   - Use: Explain why a specific due date was chosen

4. **Review Date**
   - Type: Date
   - Use: Schedule follow-up reviews

### Sample Workflow

1. Admin creates "Client Priority" dropdown field
2. Users see the field in all task forms
3. Users select appropriate priority levels
4. Data is stored and available for future filtering/reporting
5. Admin can modify field options as business needs change

## 🎉 Success Tips

- **Start Simple**: Begin with a few essential fields
- **Get User Feedback**: Ask team members what fields they need
- **Iterate**: Add and refine fields based on usage patterns
- **Document**: Maintain clear descriptions for all custom fields
- **Train Users**: Ensure team members understand how to use custom fields effectively

The custom fields system provides powerful flexibility while maintaining the application's clean, intuitive interface. Use it to capture the specific data your organization needs for effective project management.
