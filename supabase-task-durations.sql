-- Task durations table to track time spent in each status
CREATE TABLE task_durations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  status TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for better query performance
CREATE INDEX idx_task_durations_task_id ON task_durations(task_id);
CREATE INDEX idx_task_durations_status ON task_durations(status);

-- RLS policies for task_durations
ALTER TABLE task_durations ENABLE ROW LEVEL SECURITY;

-- Users can view task durations for tasks they have access to
CREATE POLICY "Users can view task durations for accessible tasks" ON task_durations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_durations.task_id
    )
  );

-- Users can insert task durations for tasks they can modify
CREATE POLICY "Users can insert task durations for modifiable tasks" ON task_durations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_durations.task_id
    )
  );

-- Users can update task durations for tasks they can modify
CREATE POLICY "Users can update task durations for modifiable tasks" ON task_durations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_durations.task_id
    )
  );

-- Users can delete task durations for tasks they can modify
CREATE POLICY "Users can delete task durations for modifiable tasks" ON task_durations
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_durations.task_id
    )
  );

-- Function to automatically create duration entry when task status changes
CREATE OR REPLACE FUNCTION handle_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- If status changed, end the current duration and start a new one
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    -- End the current duration for the old status
    UPDATE task_durations 
    SET end_time = NOW(), updated_at = NOW()
    WHERE task_id = NEW.id 
      AND status = OLD.status 
      AND end_time IS NULL;
    
    -- Start a new duration for the new status
    INSERT INTO task_durations (task_id, status, start_time)
    VALUES (NEW.id, NEW.status, NOW());
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for task status changes
CREATE TRIGGER task_status_change_trigger
  AFTER UPDATE ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION handle_task_status_change();

-- Function to create initial duration when task is created
CREATE OR REPLACE FUNCTION handle_task_creation()
RETURNS TRIGGER AS $$
BEGIN
  -- Create initial duration entry for new task
  INSERT INTO task_durations (task_id, status, start_time)
  VALUES (NEW.id, NEW.status, NOW());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for task creation
CREATE TRIGGER task_creation_trigger
  AFTER INSERT ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION handle_task_creation();
