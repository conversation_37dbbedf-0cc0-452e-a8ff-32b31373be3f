import { Task, Project, Subtask, TaskComment, TaskHistoryEntry } from '../types';
import { TaskDependency } from '../types/dependencies';
import { taskService, projectService } from './supabaseService';
import { dependencyService } from './dependencyService';
import { getCurrentUser } from '../lib/supabase';

export interface CloneOptions {
  includeSubtasks?: boolean;
  includeDependencies?: boolean;
  includeComments?: boolean;
  includeHistory?: boolean;
  targetProjectId?: string;
  targetFolderId?: string;
}

export interface CloneResult {
  success: boolean;
  data?: Task | Project;
  error?: string;
  clonedTaskIds?: Map<string, string>; // Original ID -> Cloned ID mapping
}

export class CloneService {
  
  /**
   * Generate a unique clone name with incremental numbering
   */
  private static async generateCloneName(originalName: string, type: 'task' | 'project'): Promise<string> {
    const basePrefix = 'Clone_';
    let counter = 1;
    let cloneName = `${basePrefix}${counter}_${originalName}`;
    
    // Check for existing names and increment counter
    while (await this.nameExists(cloneName, type)) {
      counter++;
      cloneName = `${basePrefix}${counter}_${originalName}`;
    }
    
    return cloneName;
  }
  
  /**
   * Check if a name already exists for tasks or projects
   */
  private static async nameExists(name: string, type: 'task' | 'project'): Promise<boolean> {
    try {
      if (type === 'task') {
        const tasks = await taskService.getTasks();
        return tasks.some(task => task.title === name);
      } else {
        const projects = await projectService.getProjects();
        return projects.some(project => project.name === name);
      }
    } catch (error) {
      console.warn('Error checking name existence:', error);
      return false;
    }
  }
  
  /**
   * Create fresh time tracking data for a cloned task
   */
  private static createFreshTimeTracking(status: string) {
    const now = new Date().toISOString();
    return {
      durations: [{
        status,
        startTime: now,
        endTime: undefined
      }],
      history: [{
        id: crypto.randomUUID(),
        taskId: '', // Will be set after task creation
        timestamp: now,
        field: 'created',
        oldValue: '',
        newValue: 'Task cloned',
        userId: 'system'
      }]
    };
  }
  
  /**
   * Clone a single task with all its properties and subtasks
   */
  static async cloneTask(
    taskId: string, 
    options: CloneOptions = {}
  ): Promise<CloneResult> {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }
      
      // Get the original task with all related data
      const tasks = await taskService.getTasks();
      const originalTask = tasks.find(t => t.id === taskId);
      
      if (!originalTask) {
        return { success: false, error: 'Task not found' };
      }


      
      // Generate clone name
      const cloneName = await this.generateCloneName(originalTask.title, 'task');
      
      // Create fresh time tracking
      const freshTracking = this.createFreshTimeTracking(originalTask.status);
      
      // Prepare cloned task data (using database field names)
      // originalTask is raw database data, so use snake_case field names
      const clonedTaskData = {
        title: cloneName,
        description: originalTask.description,
        status: originalTask.status,
        priority: originalTask.priority,
        assigned_user_id: originalTask.assigned_user_id,
        assigned_users: [...(originalTask.assigned_users || [])],
        assigned_groups: [...(originalTask.assigned_groups || [])],
        owner_id: originalTask.owner_id,
        due_date: originalTask.due_date,
        start_date: originalTask.start_date,
        tags: [...(originalTask.tags || [])],
        project_id: options.targetProjectId || originalTask.project_id,
        folder_id: options.targetFolderId || originalTask.folder_id,
        effort: originalTask.effort ? JSON.parse(JSON.stringify(originalTask.effort)) : undefined,
        custom_field_values: originalTask.custom_field_values || {},
        subtasks: originalTask.subtasks || []
      };
      
      // Create the cloned task
      const clonedTask = await taskService.cloneTask(clonedTaskData);
      
      // Update history with correct task ID
      clonedTask.history = freshTracking.history.map(h => ({ ...h, taskId: clonedTask.id }));
      
      // Clone subtasks if requested
      if (options.includeSubtasks !== false && originalTask.subtasks?.length > 0) {
        const clonedSubtasks = await this.cloneSubtasks(originalTask.subtasks, clonedTask.id);
        clonedTask.subtasks = clonedSubtasks;
        
        // Update task with cloned subtasks
        await taskService.updateTask(clonedTask.id, { 
          subtasks: JSON.parse(JSON.stringify(clonedSubtasks))
        });
      }
      
      return { 
        success: true, 
        data: clonedTask,
        clonedTaskIds: new Map([[originalTask.id, clonedTask.id]])
      };
      
    } catch (error) {
      console.error('Failed to clone task:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }
  
  /**
   * Clone subtasks for a parent task
   */
  private static async cloneSubtasks(originalSubtasks: Subtask[], parentTaskId: string): Promise<Subtask[]> {
    const clonedSubtasks: Subtask[] = [];

    for (const originalSubtask of originalSubtasks) {
      const cloneName = await this.generateCloneName(originalSubtask.title, 'task');
      const freshTracking = this.createFreshTimeTracking(originalSubtask.status);

      const clonedSubtask: Subtask = {
        id: crypto.randomUUID(),
        title: cloneName,
        description: originalSubtask.description,
        status: originalSubtask.status,
        priority: originalSubtask.priority,
        assignedUserId: originalSubtask.assignedUserId,
        assignedUsers: [...(originalSubtask.assignedUsers || [])],
        assignedGroups: [...(originalSubtask.assignedGroups || [])],
        ownerId: originalSubtask.ownerId,
        dueDate: originalSubtask.dueDate,
        startDate: originalSubtask.startDate,
        tags: [...(originalSubtask.tags || [])],
        effort: originalSubtask.effort ? JSON.parse(JSON.stringify(originalSubtask.effort)) : undefined,
        customFieldValues: originalSubtask.customFieldValues ?
          JSON.parse(JSON.stringify(originalSubtask.customFieldValues)) : {},
        // Fresh tracking data
        durations: freshTracking.durations,
        history: freshTracking.history.map(h => ({ ...h, taskId: parentTaskId })),
        comments: []
      };

      clonedSubtasks.push(clonedSubtask);
    }

    return clonedSubtasks;
  }

  /**
   * Clone an entire project with all its tasks and subtasks
   */
  static async cloneProject(
    projectId: string,
    options: CloneOptions = {}
  ): Promise<CloneResult> {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get the original project
      const projects = await projectService.getProjects();
      const originalProject = projects.find(p => p.id === projectId);

      if (!originalProject) {
        return { success: false, error: 'Project not found' };
      }

      // Generate clone name for project
      const cloneName = await this.generateCloneName(originalProject.name, 'project');

      // Prepare cloned project data (using database field names)
      // originalProject is raw database data, so use snake_case field names
      const clonedProjectData = {
        name: cloneName,
        description: originalProject.description,
        color: originalProject.color,
        start_date: originalProject.start_date,
        end_date: originalProject.end_date,
        folder_id: options.targetFolderId || originalProject.folder_id,
        effort: originalProject.effort ? JSON.parse(JSON.stringify(originalProject.effort)) : undefined
      };

      // Create the cloned project
      const clonedProject = await projectService.cloneProject(clonedProjectData);

      // Get all tasks in the original project
      const allTasks = await taskService.getTasks();
      const projectTasks = allTasks.filter(task => task.project_id === projectId);



      // Clone all tasks in the project
      const taskIdMapping = new Map<string, string>();

      for (const originalTask of projectTasks) {
        const taskCloneResult = await this.cloneTask(originalTask.id, {
          ...options,
          targetProjectId: clonedProject.id,
          targetFolderId: undefined // Tasks inherit project's folder
        });

        if (taskCloneResult.success && taskCloneResult.data) {
          taskIdMapping.set(originalTask.id, taskCloneResult.data.id);
        }
      }

      // Handle dependencies between cloned tasks (if requested)
      if (options.includeDependencies !== false && taskIdMapping.size > 0) {
        await this.cloneDependencies(Array.from(taskIdMapping.keys()), taskIdMapping);
      }

      return {
        success: true,
        data: clonedProject,
        clonedTaskIds: taskIdMapping
      };

    } catch (error) {
      console.error('Failed to clone project:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Clone dependencies between tasks, mapping old task IDs to new ones
   */
  private static async cloneDependencies(
    originalTaskIds: string[],
    taskIdMapping: Map<string, string>
  ): Promise<void> {
    try {
      // Get all dependencies for the original tasks
      const dependenciesResult = await dependencyService.getBatchDependencies(originalTaskIds);

      if (!dependenciesResult.success || !dependenciesResult.data) {
        console.warn('Could not retrieve dependencies for cloning');
        return;
      }

      // Filter dependencies that are internal to the cloned tasks
      const internalDependencies = dependenciesResult.data.filter(dep =>
        taskIdMapping.has(dep.predecessorTaskId) && taskIdMapping.has(dep.successorTaskId)
      );

      // Create new dependencies with mapped task IDs
      for (const originalDep of internalDependencies) {
        const newPredecessorId = taskIdMapping.get(originalDep.predecessorTaskId);
        const newSuccessorId = taskIdMapping.get(originalDep.successorTaskId);

        if (newPredecessorId && newSuccessorId) {
          await dependencyService.createDependency({
            predecessorTaskId: newPredecessorId,
            successorTaskId: newSuccessorId,
            dependencyType: originalDep.dependencyType,
            lagDays: originalDep.lagDays
          });
        }
      }

    } catch (error) {
      console.warn('Failed to clone dependencies:', error);
      // Don't throw error - dependencies are optional
    }
  }
}

export const cloneService = CloneService;
