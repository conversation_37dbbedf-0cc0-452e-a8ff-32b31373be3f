import React, { useState, useRef, useEffect } from 'react';
import { Search, ChevronDown, AlertCircle, X } from 'lucide-react';
import { createDebouncedSearch, validateRegexPattern } from '../utils/searchUtils';

interface SearchFilterProps {
  searchType: 'task-title' | 'project-name';
  searchTerm: string;
  isRegex: boolean;
  onSearchTypeChange: (type: 'task-title' | 'project-name') => void;
  onSearchTermChange: (term: string) => void;
  onRegexModeChange: (isRegex: boolean) => void;
  onClear: () => void;
}

export default function SearchFilter({
  searchType,
  searchTerm,
  isRegex,
  onSearchTypeChange,
  onSearchTermChange,
  onRegexModeChange,
  onClear
}: SearchFilterProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [regexError, setRegexError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debouncedSearch = createDebouncedSearch(300);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Validate regex when in regex mode
  useEffect(() => {
    if (isRegex && searchTerm) {
      const validation = validateRegexPattern(searchTerm);
      setRegexError(validation.isValid ? null : validation.error || 'Invalid regex pattern');
    } else {
      setRegexError(null);
    }
  }, [isRegex, searchTerm]);

  const handleSearchTermChange = (value: string) => {
    debouncedSearch(onSearchTermChange, value);
  };

  const searchTypeOptions = [
    { value: 'task-title' as const, label: 'Task Title Contains' },
    { value: 'project-name' as const, label: 'Project Name Contains' }
  ];

  const selectedOption = searchTypeOptions.find(option => option.value === searchType);
  const hasActiveSearch = searchTerm.trim().length > 0;

  return (
    <div className="flex items-center gap-2 p-3 bg-white border border-gray-200 rounded-lg">
      {/* Search Icon */}
      <Search className="w-4 h-4 text-gray-400 flex-shrink-0" />

      {/* Search Type Dropdown */}
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className={`flex items-center justify-between px-3 py-1.5 text-sm border rounded-md hover:bg-gray-50 transition-colors min-w-[160px] ${
            hasActiveSearch ? 'border-blue-300 bg-blue-50' : 'border-gray-300 bg-white'
          }`}
        >
          <span className={hasActiveSearch ? 'text-blue-700 font-medium' : 'text-gray-700'}>
            {selectedOption?.label}
          </span>
          <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
        </button>

        {isDropdownOpen && (
          <div className="absolute top-full mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            {searchTypeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => {
                  onSearchTypeChange(option.value);
                  setIsDropdownOpen(false);
                }}
                className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${
                  searchType === option.value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Search Input */}
      <div className="flex-1 relative">
        <input
          type="text"
          placeholder={`Enter ${isRegex ? 'regex pattern' : 'search text'}...`}
          defaultValue={searchTerm}
          onChange={(e) => handleSearchTermChange(e.target.value)}
          className={`w-full px-3 py-1.5 text-sm border rounded-md transition-colors ${
            regexError 
              ? 'border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-200' 
              : hasActiveSearch
                ? 'border-blue-300 bg-blue-50 focus:border-blue-500 focus:ring-blue-200'
                : 'border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-200'
          } focus:outline-none focus:ring-2`}
        />
        
        {regexError && (
          <div className="absolute top-full mt-1 left-0 right-0 bg-red-50 border border-red-200 rounded-md p-2 text-xs text-red-700 z-10">
            <div className="flex items-center gap-1">
              <AlertCircle className="w-3 h-3 flex-shrink-0" />
              <span>{regexError}</span>
            </div>
          </div>
        )}
      </div>

      {/* Regex Mode Toggle */}
      <div className="flex items-center gap-2">
        <label className="flex items-center gap-1.5 text-sm text-gray-600 cursor-pointer">
          <input
            type="checkbox"
            checked={isRegex}
            onChange={(e) => onRegexModeChange(e.target.checked)}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          />
          <span className={isRegex ? 'text-blue-700 font-medium' : ''}>
            Regex
          </span>
        </label>
      </div>

      {/* Clear Button */}
      {hasActiveSearch && (
        <button
          onClick={onClear}
          className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
          title="Clear search"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}
