import React, { useState } from 'react';
import { Filter, X, Calendar, Users, User, Building, ChevronDown, ChevronUp } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface KanbanFiltersProps {
  selectedProject: string;
  setSelectedProject: (project: string) => void;
  selectedUsers: string[];
  setSelectedUsers: (users: string[]) => void;
  selectedGroups: string[];
  setSelectedGroups: (groups: string[]) => void;
  selectedOwners: string[];
  setSelectedOwners: (owners: string[]) => void;
  dueDateFilter: 'all' | 'overdue' | 'today' | 'week' | 'month';
  setDueDateFilter: (filter: 'all' | 'overdue' | 'today' | 'week' | 'month') => void;
  onClearFilters: () => void;
}

export default function KanbanFilters({
  selectedProject,
  setSelectedProject,
  selectedUsers,
  setSelectedUsers,
  selectedGroups,
  setSelectedGroups,
  selectedOwners,
  setSelectedOwners,
  dueDateFilter,
  setDueDateFilter,
  onClearFilters
}: KanbanFiltersProps) {
  const { projects, users, userGroups } = useSupabaseStore();
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleUser = (userId: string) => {
    setSelectedUsers(
      selectedUsers.includes(userId)
        ? selectedUsers.filter(id => id !== userId)
        : [...selectedUsers, userId]
    );
  };

  const toggleGroup = (groupId: string) => {
    setSelectedGroups(
      selectedGroups.includes(groupId)
        ? selectedGroups.filter(id => id !== groupId)
        : [...selectedGroups, groupId]
    );
  };

  const toggleOwner = (ownerId: string) => {
    setSelectedOwners(
      selectedOwners.includes(ownerId)
        ? selectedOwners.filter(id => id !== ownerId)
        : [...selectedOwners, ownerId]
    );
  };

  const hasActiveFilters = selectedProject !== 'all' || 
    selectedUsers.length > 0 || 
    selectedGroups.length > 0 || 
    selectedOwners.length > 0 || 
    dueDateFilter !== 'all';

  return (
    <div className="bg-white border rounded-lg mb-4">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full">
                Active
              </span>
            )}
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1"
            >
              <X className="w-3 h-3" />
              Clear all
            </button>
          )}
        </div>
      </div>

      {/* Filter Controls */}
      {isExpanded && (
        <div className="px-4 pb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Project Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <Building className="w-4 h-4" />
                Project
              </label>
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Projects</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Assigned Users Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <User className="w-4 h-4" />
                Assigned Users
              </label>
              <div className="relative">
                <div className="border rounded-lg p-2 max-h-32 overflow-y-auto">
                  {users.length === 0 ? (
                    <p className="text-sm text-gray-500">No users available</p>
                  ) : (
                    users.map((user) => (
                      <label key={user.id} className="flex items-center gap-2 py-1 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => toggleUser(user.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{user.name}</span>
                      </label>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* User Groups Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <Users className="w-4 h-4" />
                Teams/Groups
              </label>
              <div className="relative">
                <div className="border rounded-lg p-2 max-h-32 overflow-y-auto">
                  {userGroups.length === 0 ? (
                    <p className="text-sm text-gray-500">No groups available</p>
                  ) : (
                    userGroups.map((group) => (
                      <label key={group.id} className="flex items-center gap-2 py-1 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedGroups.includes(group.id)}
                          onChange={() => toggleGroup(group.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{group.name}</span>
                      </label>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Owners Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <User className="w-4 h-4" />
                Owners
              </label>
              <div className="relative">
                <div className="border rounded-lg p-2 max-h-32 overflow-y-auto">
                  {users.length === 0 ? (
                    <p className="text-sm text-gray-500">No users available</p>
                  ) : (
                    users.map((user) => (
                      <label key={user.id} className="flex items-center gap-2 py-1 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedOwners.includes(user.id)}
                          onChange={() => toggleOwner(user.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{user.name}</span>
                      </label>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Due Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                Due Date
              </label>
              <select
                value={dueDateFilter}
                onChange={(e) => setDueDateFilter(e.target.value as any)}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Tasks</option>
                <option value="overdue">Overdue</option>
                <option value="today">Due Today</option>
                <option value="week">Due This Week</option>
                <option value="month">Due This Month</option>
              </select>
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex flex-wrap gap-2">
                {selectedProject !== 'all' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    Project: {projects.find(p => p.id === selectedProject)?.name}
                    <button onClick={() => setSelectedProject('all')} className="hover:bg-blue-200 rounded-full p-0.5">
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
                {selectedUsers.map(userId => (
                  <span key={userId} className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    User: {users.find(u => u.id === userId)?.name}
                    <button onClick={() => toggleUser(userId)} className="hover:bg-green-200 rounded-full p-0.5">
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
                {selectedGroups.map(groupId => (
                  <span key={groupId} className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                    Group: {userGroups.find(g => g.id === groupId)?.name}
                    <button onClick={() => toggleGroup(groupId)} className="hover:bg-purple-200 rounded-full p-0.5">
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
                {selectedOwners.map(ownerId => (
                  <span key={ownerId} className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                    Owner: {users.find(u => u.id === ownerId)?.name}
                    <button onClick={() => toggleOwner(ownerId)} className="hover:bg-orange-200 rounded-full p-0.5">
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
                {dueDateFilter !== 'all' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                    Due: {dueDateFilter}
                    <button onClick={() => setDueDateFilter('all')} className="hover:bg-red-200 rounded-full p-0.5">
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
