export interface Comment {
  id: string;
  content: string;
  userId: string;
  timestamp: string | Date;
  edited?: boolean;
  replies?: Comment[];
}
export interface TaskComment {
  id: string;
  taskId: string;
  userId: string;
  content: string;
  timestamp: string;
  edited?: boolean;
  parentId?: string;
  replies?: TaskComment[];
}

export interface Notification {
  id: string;
  recipientId: string;
  senderId: string;
  type: string;
  title: string;
  content: string;
  taskId?: string;
  commentId?: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TaskHistoryEntry {
  id: string;
  taskId: string;
  timestamp: string;
  field: string;
  oldValue: string;
  newValue: string;
  userId: string;
}

// Re-export automation types
export * from './automation';

// Re-export dependency types
export * from './dependencies';

export interface TaskDuration {
  status: string;
  startTime: string;
  endTime?: string;
}

// Rich text content types
export interface RichTextContent {
  html: string;
  plainText: string;
}

export type ContentFormat = 'html' | 'plaintext';

export interface Subtask {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  assignedUserId?: string;
  assignedUsers?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  startDate?: string;
  dueDate?: string;
  assignedGroups?: string[];
  owner?: string;
  ownerId?: string;
  status: string;
  comments: TaskComment[];
  history: TaskHistoryEntry[];
  durations: TaskDuration[];
  effort?: TaskEffort;
}

export interface User {
  id: string;
  name: string;
  email: string;
  groupId: string;
  avatar?: string;
  skillsetIds: string[];
  capacity?: UserCapacity;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: 'low' | 'medium' | 'high';
  assignedUserId?: string;
  dueDate?: string;
  startDate?: string;
  tags: string[];
  projectId?: string;
  assignedGroups?: string[];
  assignedUsers?: string[];
  ownerId?: string;
  folderId?: string;
  comments: TaskComment[];
  history: TaskHistoryEntry[];
  durations: TaskDuration[];
  subtasks: Subtask[];
  effort?: TaskEffort;

  // Dependency-related fields (optional for backward compatibility)
  dependencies?: import('./dependencies').TaskDependency[];
  dependents?: import('./dependencies').TaskDependency[];
  calculatedStartDate?: string;
  calculatedDueDate?: string;
  isOnCriticalPath?: boolean;
  totalFloat?: number;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  color: string;
  tasks: string[];
  startDate?: string;
  endDate?: string;
  folderId?: string;
  effort?: {
    skillsetEstimates: {
      skillsetId: string;
      estimatedHours: number;
    }[];
    totalEstimatedHours: number;
  };
}

export interface UserGroup {
  id: string;
  name: string;
  color: string;
}

export interface KanbanColumn {
  id: string; // Status-based ID for compatibility (todo, in-progress, etc.)
  title: string;
  color: string;
  dbId?: string; // Database UUID for updates
}

export interface Folder {
  id: string;
  name: string;
  parentId?: string;
}

export interface TaskFilter {
  assignedUsers: string[];
  status: string[];
  priority: string[];
  owners: string[];
  assignedGroups: string[];
  tags: string[];
  dueDateRange: {
    start?: string;
    end?: string;
  };
  hasOverdueTasks?: boolean;
  search?: {
    type: 'task-title' | 'project-name';
    term: string;
    isRegex: boolean;
  };
}

export interface SkillsetGroup {
  id: string;
  name: string;
  description?: string;
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserCapacity {
  id?: string; // Database ID for updates
  userId: string;
  dailyHours: number;
  weeklyHours: number;
  workingDays: number[]; // 0-6 (Sunday-Saturday)
  effectiveFrom: string;
  effectiveTo?: string;
}

export interface TaskEffort {
  taskId: string;
  estimatedHours: number;
  actualHours?: number;
  assignedUserId?: string;
  requiredSkillsets: string[]; // Array of skillset IDs required for this task
}