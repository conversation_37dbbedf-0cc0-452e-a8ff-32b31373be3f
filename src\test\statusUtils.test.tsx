import React from 'react';
import { render } from '@testing-library/react';
import { getStatusBadge, getPriorityBadge, truncateTitle } from '../utils/statusUtils';

describe('Status Utils', () => {
  describe('getStatusBadge', () => {
    it('should render status badge with default colors', () => {
      const badge = getStatusBadge('todo');
      const { container } = render(<div>{badge}</div>);
      
      expect(container.querySelector('span')).toHaveClass('bg-gray-100', 'text-gray-800');
      expect(container.textContent).toBe('todo');
    });

    it('should render status badge with custom column colors', () => {
      const columns = [
        { id: 'todo', title: 'To Do', color: 'bg-red-100 text-red-800' }
      ];
      const badge = getStatusBadge('todo', columns);
      const { container } = render(<div>{badge}</div>);
      
      expect(container.querySelector('span')).toHaveClass('bg-red-100', 'text-red-800');
      expect(container.textContent).toBe('To Do');
    });
  });

  describe('getPriorityBadge', () => {
    it('should render priority badge with correct colors', () => {
      const badge = getPriorityBadge('high');
      const { container } = render(<div>{badge}</div>);
      
      expect(container.querySelector('span')).toHaveClass('bg-red-100', 'text-red-800');
      expect(container.textContent).toBe('high');
    });
  });

  describe('truncateTitle', () => {
    it('should not truncate short titles', () => {
      expect(truncateTitle('Short title')).toBe('Short title');
    });

    it('should truncate long titles', () => {
      const longTitle = 'This is a very long title that should be truncated';
      expect(truncateTitle(longTitle, 20)).toBe('This is a very long ...');
    });

    it('should use default max length of 50', () => {
      const longTitle = 'This is a very long title that should be truncated because it exceeds the default limit';
      const result = truncateTitle(longTitle);
      expect(result).toBe('This is a very long title that should be truncat...');
      expect(result.length).toBe(53); // 50 + '...'
    });
  });
});
