/* Custom styles for React Quill to match existing form styling */

.rich-text-editor {
  margin-bottom: 4rem; /* Ensure proper spacing after the editor */
}

.rich-text-editor .ql-container {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border-color: #d1d5db;
  font-size: 0.875rem;
  margin-bottom: 4rem; /* Additional space after the editor container */
}

.rich-text-editor .ql-toolbar {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.rich-text-editor .ql-editor {
  min-height: 160px;
  padding: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.rich-text-editor .ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
}

.rich-text-editor .ql-toolbar .ql-stroke {
  stroke: #6b7280;
}

.rich-text-editor .ql-toolbar .ql-fill {
  fill: #6b7280;
}

.rich-text-editor .ql-toolbar button:hover .ql-stroke {
  stroke: #374151;
}

.rich-text-editor .ql-toolbar button:hover .ql-fill {
  fill: #374151;
}

.rich-text-editor .ql-toolbar button.ql-active .ql-stroke {
  stroke: #2563eb;
}

.rich-text-editor .ql-toolbar button.ql-active .ql-fill {
  fill: #2563eb;
}

.rich-text-editor .ql-snow .ql-picker {
  color: #6b7280;
}

.rich-text-editor .ql-snow .ql-picker-options {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Focus styles */
.rich-text-editor .ql-container.ql-snow {
  border-color: #d1d5db;
}

.rich-text-editor .ql-editor:focus {
  outline: none;
}

.rich-text-editor:focus-within .ql-toolbar,
.rich-text-editor:focus-within .ql-container {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Disabled state */
.rich-text-editor .ql-toolbar.ql-disabled {
  background-color: #f3f4f6;
  opacity: 0.6;
}

.rich-text-editor .ql-editor.ql-disabled {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Custom styling for code format */
.rich-text-editor .ql-editor code {
  background-color: #f3f4f6;
  color: #e11d48;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

/* Strikethrough styling (Quill handles this automatically, but we can enhance it) */
.rich-text-editor .ql-editor s {
  text-decoration: line-through;
  opacity: 0.7;
}
