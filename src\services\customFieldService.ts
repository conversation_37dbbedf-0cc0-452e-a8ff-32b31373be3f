import { supabase, handleSupabaseError, getCurrentUser } from '../lib/supabase';
import {
  CustomField,
  SupabaseCustomField,
  CreateCustomFieldRequest,
  UpdateCustomFieldRequest,
  transformSupabaseCustomField
} from '../types/customFields';

export const customFieldService = {
  /**
   * Get all custom fields
   */
  async getCustomFields(): Promise<SupabaseCustomField[]> {
    const { data, error } = await supabase
      .from('custom_fields')
      .select('*')
      .order('sort_order', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) handleSupabaseError(error);
    return data || [];
  },

  /**
   * Get active custom fields only
   */
  async getActiveCustomFields(): Promise<SupabaseCustomField[]> {
    const { data, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) handleSupabaseError(error);
    return data || [];
  },

  /**
   * Get a single custom field by ID
   */
  async getCustomField(id: string): Promise<SupabaseCustomField | null> {
    const { data, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      handleSupabaseError(error);
    }
    return data;
  },

  /**
   * Create a new custom field
   */
  async createCustomField(customField: CreateCustomFieldRequest): Promise<SupabaseCustomField> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('custom_fields')
      .insert({
        ...customField,
        created_by: user.id,
      })
      .select('*')
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  /**
   * Update an existing custom field
   */
  async updateCustomField(id: string, updates: UpdateCustomFieldRequest): Promise<SupabaseCustomField> {
    const { data, error } = await supabase
      .from('custom_fields')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select('*')
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  /**
   * Delete a custom field (soft delete by setting is_active to false)
   */
  async deleteCustomField(id: string): Promise<void> {
    const { error } = await supabase
      .from('custom_fields')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) handleSupabaseError(error);
  },

  /**
   * Hard delete a custom field (permanently remove from database)
   */
  async hardDeleteCustomField(id: string): Promise<void> {
    const { error } = await supabase
      .from('custom_fields')
      .delete()
      .eq('id', id);

    if (error) handleSupabaseError(error);
  },

  /**
   * Reorder custom fields
   */
  async reorderCustomFields(fieldIds: string[]): Promise<void> {
    const updates = fieldIds.map((id, index) => ({
      id,
      sort_order: index,
      updated_at: new Date().toISOString(),
    }));

    for (const update of updates) {
      const { error } = await supabase
        .from('custom_fields')
        .update({
          sort_order: update.sort_order,
          updated_at: update.updated_at,
        })
        .eq('id', update.id);

      if (error) handleSupabaseError(error);
    }
  },

  /**
   * Check if a custom field name is available
   */
  async isFieldNameAvailable(name: string, excludeId?: string): Promise<boolean> {
    let query = supabase
      .from('custom_fields')
      .select('id')
      .eq('name', name);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) handleSupabaseError(error);
    return !data || data.length === 0;
  },

  /**
   * Validate custom field name format
   */
  validateFieldName(name: string): string | null {
    if (!name || name.trim().length === 0) {
      return 'Field name is required';
    }

    if (name.length > 50) {
      return 'Field name must be 50 characters or less';
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(name)) {
      return 'Field name must start with a letter and contain only letters, numbers, and underscores';
    }

    // Check for reserved names
    const reservedNames = [
      'id', 'title', 'description', 'status', 'priority', 'assignedUserId',
      'assignedUsers', 'assignedGroups', 'ownerId', 'dueDate', 'startDate',
      'tags', 'projectId', 'folderId', 'comments', 'history', 'durations',
      'subtasks', 'effort', 'customFieldValues', 'createdAt', 'updatedAt',
      'createdBy', 'completed', 'dependencies', 'dependents'
    ];

    if (reservedNames.includes(name)) {
      return 'Field name conflicts with a system field';
    }

    return null;
  },

  /**
   * Validate dropdown options
   */
  validateDropdownOptions(options: string[]): string | null {
    if (!options || options.length === 0) {
      return 'Dropdown fields must have at least one option';
    }

    if (options.length > 50) {
      return 'Dropdown fields cannot have more than 50 options';
    }

    const uniqueOptions = new Set(options.map(opt => opt.trim()));
    if (uniqueOptions.size !== options.length) {
      return 'Dropdown options must be unique';
    }

    for (const option of options) {
      if (!option || option.trim().length === 0) {
        return 'Dropdown options cannot be empty';
      }
      if (option.length > 100) {
        return 'Dropdown options must be 100 characters or less';
      }
    }

    return null;
  },

  /**
   * Get custom field usage statistics
   */
  async getCustomFieldUsage(fieldName: string): Promise<{ totalTasks: number; tasksWithValue: number }> {
    // Get total tasks
    const { count: totalTasks, error: totalError } = await supabase
      .from('tasks')
      .select('*', { count: 'exact', head: true });

    if (totalError) handleSupabaseError(totalError);

    // Get tasks with this custom field value
    const { count: tasksWithValue, error: valueError } = await supabase
      .from('tasks')
      .select('*', { count: 'exact', head: true })
      .not('custom_field_values', 'is', null)
      .neq('custom_field_values->' + fieldName, null);

    if (valueError) handleSupabaseError(valueError);

    return {
      totalTasks: totalTasks || 0,
      tasksWithValue: tasksWithValue || 0,
    };
  },
};
