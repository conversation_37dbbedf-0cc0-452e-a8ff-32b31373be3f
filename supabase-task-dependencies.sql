-- Task Dependencies Migration
-- This script creates the task_dependencies table and related infrastructure
-- for supporting task dependency management and Gantt chart functionality

-- Create task_dependencies table
CREATE TABLE IF NOT EXISTS task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  predecessor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  successor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  dependency_type TEXT DEFAULT 'finish_to_start' CHECK (dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')),
  lag_days INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  UNIQUE(predecessor_task_id, successor_task_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_dependencies_predecessor ON task_dependencies(predecessor_task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_successor ON task_dependencies(successor_task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_type ON task_dependencies(dependency_type);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_created_by ON task_dependencies(created_by);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_task_dependencies_updated_at BEFORE UPDATE ON task_dependencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on task_dependencies table
ALTER TABLE task_dependencies ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view dependencies for tasks they have access to
CREATE POLICY "Users can view task dependencies" ON task_dependencies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.predecessor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[]) OR
        EXISTS (
          SELECT 1 FROM user_profiles 
          WHERE user_profiles.id = auth.uid() 
          AND user_profiles.group_id = ANY(tasks.assigned_groups::uuid[])
        )
      )
    ) OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.successor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[]) OR
        EXISTS (
          SELECT 1 FROM user_profiles 
          WHERE user_profiles.id = auth.uid() 
          AND user_profiles.group_id = ANY(tasks.assigned_groups::uuid[])
        )
      )
    )
  );

-- Policy: Users can create dependencies for tasks they can edit
CREATE POLICY "Users can create task dependencies" ON task_dependencies
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.predecessor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    ) AND
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.successor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    )
  );

-- Policy: Users can update dependencies they created or for tasks they can edit
CREATE POLICY "Users can update task dependencies" ON task_dependencies
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.predecessor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    ) OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.successor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    )
  );

-- Policy: Users can delete dependencies they created or for tasks they can edit
CREATE POLICY "Users can delete task dependencies" ON task_dependencies
  FOR DELETE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.predecessor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    ) OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.successor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        auth.uid() = ANY(tasks.assigned_users::uuid[])
      )
    )
  );

-- Comments for documentation
COMMENT ON TABLE task_dependencies IS 'Stores task dependency relationships for project management and Gantt chart functionality';
COMMENT ON COLUMN task_dependencies.dependency_type IS 'Type of dependency: finish_to_start, start_to_start, finish_to_finish, start_to_finish';
COMMENT ON COLUMN task_dependencies.lag_days IS 'Number of days to wait after dependency condition is met before successor can start/finish';

SELECT 'Task dependencies schema created successfully!' as message;
