import React, { useEffect, useState } from 'react';
import { X, Clock, CheckCircle, XCircle, AlertCircle, Eye, RefreshCw } from 'lucide-react';
import { useAutomationStore } from '../../store/useAutomationStore';
import { AutomationExecution } from '../../types/automation';
// Simple date formatting utility
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

interface ExecutionLogProps {
  workflowId?: string;
  onClose: () => void;
}

export default function ExecutionLog({ workflowId, onClose }: ExecutionLogProps) {
  const { executions, loading, loadExecutions } = useAutomationStore();
  const [selectedExecution, setSelectedExecution] = useState<AutomationExecution | null>(null);
  const [filter, setFilter] = useState<'all' | 'success' | 'failed' | 'pending' | 'skipped'>('all');

  useEffect(() => {
    loadExecutions(workflowId);
  }, [loadExecutions, workflowId]);

  const filteredExecutions = executions.filter(execution => {
    if (filter === 'all') return true;
    return execution.status === filter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'skipped':
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-400 bg-green-500/20';
      case 'failed':
        return 'text-red-400 bg-red-500/20';
      case 'pending':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'skipped':
        return 'text-gray-400 bg-gray-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  const renderExecutionDetails = (execution: AutomationExecution) => {
    return (
      <div className="space-y-6">
        <div>
          <h4 className="text-white font-medium mb-3">Execution Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Status:</span>
              <div className="flex items-center gap-2 mt-1">
                {getStatusIcon(execution.status)}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>
                  {execution.status}
                </span>
              </div>
            </div>
            <div>
              <span className="text-gray-400">Execution Time:</span>
              <div className="text-white mt-1">
                {execution.executionTimeMs ? `${execution.executionTimeMs}ms` : 'N/A'}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Started:</span>
              <div className="text-white mt-1">
                {formatDate(execution.executedAt)}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Workflow:</span>
              <div className="text-white mt-1">{execution.workflowId}</div>
            </div>
          </div>
        </div>

        {execution.errorMessage && (
          <div>
            <h4 className="text-red-400 font-medium mb-3">Error Message</h4>
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
              <p className="text-red-300 text-sm">{execution.errorMessage}</p>
            </div>
          </div>
        )}

        <div>
          <h4 className="text-white font-medium mb-3">Trigger Data</h4>
          <div className="bg-gray-700 rounded-lg p-3">
            <pre className="text-gray-300 text-xs overflow-auto">
              {JSON.stringify(execution.triggerData, null, 2)}
            </pre>
          </div>
        </div>

        {execution.conditionResult && (
          <div>
            <h4 className="text-white font-medium mb-3">Condition Result</h4>
            <div className="bg-gray-700 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                {execution.conditionResult.passed ? (
                  <CheckCircle className="w-4 h-4 text-green-400" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-400" />
                )}
                <span className="text-white">
                  {execution.conditionResult.passed ? 'Conditions Met' : 'Conditions Not Met'}
                </span>
              </div>
              <pre className="text-gray-300 text-xs overflow-auto">
                {JSON.stringify(execution.conditionResult.evaluationDetails, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div>
          <h4 className="text-white font-medium mb-3">Action Results</h4>
          <div className="space-y-3">
            {execution.actionResults.map((result, index) => (
              <div key={index} className="bg-gray-700 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">{result.actionType}</span>
                  <div className="flex items-center gap-2">
                    {result.success ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-400" />
                    )}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      result.success ? 'text-green-400 bg-green-500/20' : 'text-red-400 bg-red-500/20'
                    }`}>
                      {result.success ? 'Success' : 'Failed'}
                    </span>
                  </div>
                </div>
                
                {result.error && (
                  <div className="mb-2">
                    <p className="text-red-300 text-sm">{result.error}</p>
                  </div>
                )}
                
                {result.result && (
                  <pre className="text-gray-300 text-xs overflow-auto">
                    {JSON.stringify(result.result, null, 2)}
                  </pre>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] flex">
        {/* Execution List */}
        <div className="w-1/3 border-r border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-white">Execution Log</h2>
              <button
                onClick={() => loadExecutions(workflowId)}
                disabled={loading.executions}
                className="p-2 text-gray-400 hover:text-white disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${loading.executions ? 'animate-spin' : ''}`} />
              </button>
            </div>
            
            <div className="flex gap-1">
              {['all', 'success', 'failed', 'pending', 'skipped'].map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status as any)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    filter === status
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {status}
                </button>
              ))}
            </div>
          </div>
          
          <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
            {loading.executions ? (
              <div className="p-4 text-center text-gray-400">
                Loading executions...
              </div>
            ) : filteredExecutions.length === 0 ? (
              <div className="p-4 text-center text-gray-400">
                No executions found
              </div>
            ) : (
              <div className="space-y-1 p-2">
                {filteredExecutions.map((execution) => (
                  <div
                    key={execution.id}
                    onClick={() => setSelectedExecution(execution)}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedExecution?.id === execution.id
                        ? 'bg-blue-600/20 border border-blue-500'
                        : 'hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(execution.status)}
                        <span className="text-white text-sm font-medium">
                          {new Date(execution.executedAt).toLocaleTimeString()}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>
                        {execution.status}
                      </span>
                    </div>
                    <div className="text-gray-400 text-xs">
                      {new Date(execution.executedAt).toLocaleDateString()}
                    </div>
                    {execution.executionTimeMs && (
                      <div className="text-gray-500 text-xs mt-1">
                        {execution.executionTimeMs}ms
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Execution Details */}
        <div className="flex-1 flex flex-col">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">
              {selectedExecution ? 'Execution Details' : 'Select an execution'}
            </h3>
            <button onClick={onClose} className="text-gray-400 hover:text-white">
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {selectedExecution ? (
              renderExecutionDetails(selectedExecution)
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <div className="text-center">
                  <Eye className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Select an execution to view details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
