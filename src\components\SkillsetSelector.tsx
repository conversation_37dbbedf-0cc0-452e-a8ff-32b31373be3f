import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check, X } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface SkillsetSelectorProps {
  selectedSkillsetIds: string[];
  onSelectionChange: (skillsetIds: string[]) => void;
  placeholder?: string;
  label?: string;
  disabled?: boolean;
}

export default function SkillsetSelector({ 
  selectedSkillsetIds, 
  onSelectionChange, 
  placeholder = 'Select skillsets...',
  label = 'Skillsets',
  disabled = false
}: SkillsetSelectorProps) {
  const { skillsetGroups } = useSupabaseStore();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggleSkillset = (skillsetId: string) => {
    if (selectedSkillsetIds.includes(skillsetId)) {
      onSelectionChange(selectedSkillsetIds.filter(id => id !== skillsetId));
    } else {
      onSelectionChange([...selectedSkillsetIds, skillsetId]);
    }
  };

  const handleRemoveSkillset = (skillsetId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onSelectionChange(selectedSkillsetIds.filter(id => id !== skillsetId));
  };

  const selectedSkillsets = skillsetGroups.filter(skillset => 
    selectedSkillsetIds.includes(skillset.id)
  );

  const getDisplayText = () => {
    if (selectedSkillsets.length === 0) return placeholder;
    if (selectedSkillsets.length === 1) return selectedSkillsets[0].name;
    return `${selectedSkillsets.length} skillsets selected`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium mb-1 text-gray-700">{label}</label>
      
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full flex items-center justify-between px-3 py-2 text-sm border rounded-lg transition-colors ${
          disabled 
            ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
            : selectedSkillsets.length > 0 
              ? 'border-blue-300 bg-blue-50 hover:bg-blue-100' 
              : 'border-gray-300 bg-white hover:bg-gray-50'
        }`}
      >
        <span className={selectedSkillsets.length > 0 ? 'text-blue-700' : 'text-gray-500'}>
          {getDisplayText()}
        </span>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Selected Skillsets Display */}
      {selectedSkillsets.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedSkillsets.map((skillset) => (
            <span
              key={skillset.id}
              className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              <div className={`w-2 h-2 rounded-full ${skillset.color}`} />
              {skillset.name}
              {!disabled && (
                <button
                  type="button"
                  onClick={(e) => handleRemoveSkillset(skillset.id, e)}
                  className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </span>
          ))}
        </div>
      )}

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div className="absolute top-full mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {skillsetGroups.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              No skillset groups available. Create some first.
            </div>
          ) : (
            skillsetGroups.map((skillset) => {
              const isSelected = selectedSkillsetIds.includes(skillset.id);
              return (
                <button
                  key={skillset.id}
                  type="button"
                  onClick={() => handleToggleSkillset(skillset.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${skillset.color}`} />
                    <div className="text-left">
                      <div className="font-medium">{skillset.name}</div>
                      {skillset.description && (
                        <div className="text-xs text-gray-500">{skillset.description}</div>
                      )}
                    </div>
                  </div>
                  {isSelected && <Check className="w-4 h-4 text-blue-600" />}
                </button>
              );
            })
          )}
        </div>
      )}
    </div>
  );
}
