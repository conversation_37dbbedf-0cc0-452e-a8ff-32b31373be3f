-- Fix task_dependencies RLS policies to match tasks table permissions
-- This allows users to see and manage dependencies for all tasks they can see

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view task dependencies" ON task_dependencies;
DROP POLICY IF EXISTS "Users can create task dependencies" ON task_dependencies;
DROP POLICY IF EXISTS "Users can update task dependencies" ON task_dependencies;
DROP POLICY IF EXISTS "Users can delete task dependencies" ON task_dependencies;

-- Create new liberal policies that match the tasks table approach

-- Policy: Users can view all task dependencies (matches "Users can view all tasks")
CREATE POLICY "Users can view all task dependencies" ON task_dependencies
  FOR SELECT USING (true);

-- Policy: Authenticated users can create task dependencies (matches tasks creation policy)
CREATE POLICY "Authenticated users can create task dependencies" ON task_dependencies
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    created_by = auth.uid()
  );

-- Policy: Users can update dependencies they created or for tasks they can edit
-- This matches the tasks update policy logic
CREATE POLICY "Users can update task dependencies" ON task_dependencies
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) OR
    -- Allow if user can edit either the predecessor or successor task
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.predecessor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        tasks.owner_id = auth.uid() OR
        auth.uid()::text = ANY(tasks.assigned_users)
      )
    ) OR
    EXISTS (
      SELECT 1 FROM tasks 
      WHERE tasks.id = task_dependencies.successor_task_id 
      AND (
        tasks.created_by = auth.uid() OR
        tasks.assigned_user_id = auth.uid() OR
        tasks.owner_id = auth.uid() OR
        auth.uid()::text = ANY(tasks.assigned_users)
      )
    )
  );

-- Policy: Users can delete dependencies they created or admins can delete any
-- This matches the tasks deletion policy approach
CREATE POLICY "Users can delete task dependencies" ON task_dependencies
  FOR DELETE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Verify the policies were created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'task_dependencies'
ORDER BY policyname;

SELECT 'Task dependencies RLS policies updated successfully!' as message;
