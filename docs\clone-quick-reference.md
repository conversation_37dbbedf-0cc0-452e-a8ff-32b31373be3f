# Clone Functionality - Quick Reference

## 🚀 Quick Access Guide

### Task Cloning

| Location | How to Access | Result |
|----------|---------------|---------|
| **Task List View** | Click Copy icon (📋) next to task | Clones task in same project |
| **Task Edit Form** | Click ⋮ menu → "Clone Task" | Clones task and closes form |
| **Subtask Edit Form** | Click ⋮ menu → "Clone Parent Task" | Clones entire parent task |

### Project Cloning

| Location | How to Access | Result |
|----------|---------------|---------|
| **Project Tree** | Right-click project → "Clone Project" | Clones entire project |
| **Project Manager** | Hover over project → Click blue Copy icon | Clones entire project |
| **Context Menu** | Click ⋮ next to project → "Clone Project" | Clones entire project |

## ✨ What Gets Cloned

### Tasks
✅ **Included**: Title, description, status, priority, dates, assignments, custom fields, subtasks  
❌ **Excluded**: Comments, time tracking history, task ID

### Projects  
✅ **Included**: Name, description, color, dates, folder, all tasks, task dependencies  
❌ **Excluded**: Comments, time tracking history, project/task IDs

## 🏷️ Naming Convention

- **Format**: `Clone_N_[Original Name]`
- **Examples**: 
  - "Project Alpha" → "Clone_1_Project Alpha"
  - "Bug Fix Task" → "Clone_1_Bug Fix Task"
- **Auto-increment**: System finds next available number automatically

## ⏱️ Time Tracking

- **Fresh Start**: All cloned items start with zero time tracking
- **Independent**: Cloned items track time separately from originals
- **History**: New history entries mark clone creation

## 🔗 Dependencies

- **Within Projects**: Dependencies between tasks in the same project are preserved
- **Cross-Project**: Dependencies to tasks outside the project are not cloned
- **Automatic Mapping**: Internal task IDs are automatically remapped in cloned projects

## 🚨 Common Issues

| Issue | Solution |
|-------|----------|
| Clone button not working | Check user permissions and authentication |
| Three-dot menu not opening | Refresh page, check for JavaScript errors |
| Missing attributes in cloned items | Verify all custom fields are configured |
| Project clone missing tasks | Check database permissions for all project tasks |

## 💡 Best Practices

1. **Template Projects**: Create dedicated template projects for common workflows
2. **Naming**: Use descriptive names for items you plan to clone frequently
3. **Dependencies**: Design projects with self-contained dependencies for better cloning
4. **Custom Fields**: Set up custom fields before cloning to ensure data preservation
5. **Cleanup**: Regularly review and remove unused cloned items

## 🎯 Use Cases

- **Project Templates**: Monthly reports, quarterly reviews, standard processes
- **Task Templates**: Code reviews, testing procedures, recurring activities  
- **Training**: Create example projects for team training
- **Workflows**: Duplicate successful project structures

---

*For detailed documentation, see [Clone Functionality Guide](./clone-functionality-guide.md)*
