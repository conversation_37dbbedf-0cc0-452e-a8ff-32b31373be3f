import React, { useState } from 'react';
import { Folder } from '../types';
import { X } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface FolderFormProps {
  onSubmit: (folder: Omit<Folder, 'id'>) => void;
  onClose: () => void;
  initialData?: Folder;
}

export default function FolderForm({ onSubmit, onClose, initialData }: FolderFormProps) {
  const { folders } = useSupabaseStore();
  
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    parentId: initialData?.parentId || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      name: formData.name,
      parentId: formData.parentId || undefined,
    });
  };

  // Filter out the current folder and its descendants to prevent circular references
  const availableParentFolders = folders.filter(folder => {
    if (initialData && folder.id === initialData.id) return false;
    // Add more sophisticated circular reference checking if needed
    return true;
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            {initialData ? 'Edit Folder' : 'Create New Folder'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Folder Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full border rounded-lg p-2"
              placeholder="Enter folder name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Parent Folder</label>
            <select
              value={formData.parentId}
              onChange={(e) => setFormData({ ...formData, parentId: e.target.value })}
              className="w-full border rounded-lg p-2"
            >
              <option value="">No parent (root level)</option>
              {availableParentFolders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
            >
              {initialData ? 'Update Folder' : 'Create Folder'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
