import React from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import { MessageSquare, CheckCircle, Circle, ExternalLink } from 'lucide-react';
import { Notification } from '../types';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (notificationId: string) => void;
  onMarkAsUnread: (notificationId: string) => void;
  onNavigateToTask: (taskId: string) => void;
}

export default function NotificationItem({ 
  notification, 
  onMarkAsRead, 
  onMarkAsUnread, 
  onNavigateToTask 
}: NotificationItemProps) {
  const handleToggleRead = () => {
    if (notification.isRead) {
      onMarkAsUnread(notification.id);
    } else {
      onMarkAsRead(notification.id);
    }
  };

  const handleTaskClick = () => {
    if (notification.taskId) {
      onNavigateToTask(notification.taskId);
      // Mark as read when user clicks on the task
      if (!notification.isRead) {
        onMarkAsRead(notification.id);
      }
    }
  };

  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'mention':
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div 
      className={`p-3 border-b border-gray-200 hover:bg-gray-50 transition-colors ${
        !notification.isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
      }`}
    >
      <div className="flex items-start gap-3">
        {/* Notification Icon */}
        <div className="flex-shrink-0 mt-1">
          {getNotificationIcon()}
        </div>

        {/* Notification Content */}
        <div className="flex-1 min-w-0">
          {/* Title */}
          <div className="flex items-start justify-between gap-2">
            <h4 className={`text-sm font-medium ${
              !notification.isRead ? 'text-gray-900' : 'text-gray-700'
            }`}>
              {notification.title}
            </h4>
            
            {/* Read/Unread Toggle */}
            <button
              onClick={handleToggleRead}
              className="flex-shrink-0 p-1 hover:bg-gray-200 rounded transition-colors"
              title={notification.isRead ? 'Mark as unread' : 'Mark as read'}
            >
              {notification.isRead ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : (
                <Circle className="w-4 h-4 text-blue-500" />
              )}
            </button>
          </div>

          {/* Content */}
          <p className={`text-sm mt-1 ${
            !notification.isRead ? 'text-gray-800' : 'text-gray-600'
          }`}>
            {notification.content}
          </p>

          {/* Footer with timestamp and task link */}
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-gray-500">
              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
            </span>
            
            {notification.taskId && (
              <button
                onClick={handleTaskClick}
                className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 hover:underline transition-colors"
              >
                <ExternalLink className="w-3 h-3" />
                View Task
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
