import React from 'react';
import { Task } from '../types';
import { Calendar, Clock, Tag, Users } from 'lucide-react';
import { format } from 'date-fns';

interface SearchResultsProps {
  results: Task[];
  onEditTask: (task: Task) => void;
  query: string;
}

export default function SearchResults({ results, onEditTask, query }: SearchResultsProps) {
  if (results.length === 0) {
    return (
      <div className="absolute top-full mt-2 w-full bg-white rounded-lg shadow-lg border p-4 z-50">
        <p className="text-gray-500 text-center">No tasks found matching "{query}"</p>
      </div>
    );
  }

  return (
    <div className="absolute top-full mt-2 w-full bg-white rounded-lg shadow-lg border max-h-[400px] overflow-y-auto z-50">
      {results.map((task) => (
        <button
          key={task.id}
          onClick={() => onEditTask(task)}
          className="w-full text-left p-3 hover:bg-gray-50 border-b last:border-b-0 transition-colors"
        >
          <div className="flex items-start justify-between">
            <div>
              <h4 className="font-medium">{task.title}</h4>
              <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                {task.description}
              </p>
            </div>
            <span className={`
              px-2 py-1 rounded-full text-xs
              ${task.status === 'done' ? 'bg-green-100 text-green-800' :
                task.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                task.status === 'review' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'}
            `}>
              {task.status.replace('-', ' ')}
            </span>
          </div>
          
          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            {task.dueDate && (
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>Due {format(new Date(task.dueDate), 'MMM d')}</span>
              </div>
            )}
            {task.priority && (
              <div className="flex items-center gap-1">
                <Tag className="w-3 h-3" />
                <span className={`
                  ${task.priority === 'high' ? 'text-red-600' :
                    task.priority === 'medium' ? 'text-yellow-600' :
                    'text-green-600'}
                `}>
                  {task.priority}
                </span>
              </div>
            )}
            {task.assignedGroups && task.assignedGroups.length > 0 && (
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                <span>{task.assignedGroups.length} assigned</span>
              </div>
            )}
          </div>
        </button>
      ))}
    </div>
  );
}