import { supabase, handleSupabaseError, getCurrentUser } from '../lib/supabase';
import { Database } from '../types/database';
import {
  Task,
  Project,
  UserGroup,
  KanbanColumn,
  Folder,
  TaskComment,
  TaskHistoryEntry,
  User,
  SkillsetGroup,
  UserCapacity,
  TaskEffort,
  Notification
} from '../types';

// Re-export custom field service
export { customFieldService } from './customFieldService';

type Tables = Database['public']['Tables'];

// User Profiles
export const userProfileService = {
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateProfile(userId: string, updates: Partial<Tables['user_profiles']['Update']>) {
    const { data, error } = await supabase
      .from('user_profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async createProfile(profile: Tables['user_profiles']['Insert']) {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profile)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async getAllUsers() {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  }
};

// Tasks
export const taskService = {
  async getTasks() {
    // Include task_durations, task_history, and task_comments for full tracking functionality
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_durations (
          id,
          status,
          start_time,
          end_time,
          created_at
        ),
        task_history (
          id,
          task_id,
          user_id,
          field,
          old_value,
          new_value,
          created_at
        ),
        task_comments (
          id,
          task_id,
          user_id,
          content,
          created_at,
          updated_at,
          edited,
          parent_id
        )
      `)
      .order('created_at', { ascending: false });

    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createTask(task: Omit<Tables['tasks']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('tasks')
      .insert({ ...task, created_by: user.id })
      .select('*')
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async updateTask(taskId: string, updates: Partial<Tables['tasks']['Update']>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Retry logic for version conflicts
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        // Get current version for optimistic locking (fresh on each retry)
        console.log(`🔍 Fetching current version for task ${taskId} (attempt ${retryCount + 1})`);
        const { data: currentTask, error: fetchError } = await supabase
          .from('tasks')
          .select('updated_at, version, updated_by')
          .eq('id', taskId)
          .single();

        if (fetchError) {
          console.error(`❌ Error fetching task version:`, fetchError);
          handleSupabaseError(fetchError);
        }
        if (!currentTask) throw new Error('Task not found');

        const currentVersion = currentTask.version || 0;
        const newVersion = currentVersion + 1;

        console.log(`🔄 Attempt ${retryCount + 1}: Database version=${currentVersion}, will update to=${newVersion}, last_updated_by=${currentTask.updated_by}`);

        // Add a small delay to avoid race conditions with real-time updates
        if (retryCount > 0) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Try update with optimistic locking
        console.log(`🔄 Executing update: SET version=${newVersion} WHERE id='${taskId}' AND version=${currentVersion}`);
        const { data, error } = await supabase
          .from('tasks')
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
            updated_by: user.id,
            version: newVersion
          })
          .eq('id', taskId)
          .eq('version', currentVersion) // Use fresh version number
          .select('*')
          .single();

        if (error) {
          if (error.code === 'PGRST116') { // No rows updated - version conflict
            // Check what the version actually is now
            const { data: conflictTask } = await supabase
              .from('tasks')
              .select('version, updated_at, updated_by')
              .eq('id', taskId)
              .single();

            console.log(`❌ Version conflict! Expected version ${currentVersion}, but database has version ${conflictTask?.version}, updated by ${conflictTask?.updated_by}`);

            retryCount++;
            if (retryCount < maxRetries) {
              console.log(`🔄 Version conflict on attempt ${retryCount}, retrying ${retryCount}/${maxRetries}...`);
              // Wait progressively longer before retrying
              await new Promise(resolve => setTimeout(resolve, 300 * retryCount));
              continue; // This will fetch fresh version on next iteration
            } else {
              // After max retries, get the latest data and return it
              const { data: latestTask } = await supabase
                .from('tasks')
                .select('*')
                .eq('id', taskId)
                .single();

              if (latestTask) {
                console.log('⚠️ Max retries reached, returning latest data without update');
                return latestTask;
              }

              throw new Error('Task was modified by another user. Please refresh and try again.');
            }
          }
          handleSupabaseError(error);
        }

        // Success! Return the updated data
        console.log(`✅ Task update successful on attempt ${retryCount + 1}`);
        return data;

      } catch (error) {
        if (retryCount >= maxRetries - 1) {
          throw error;
        }
        retryCount++;
        console.log(`🔄 Error during update attempt ${retryCount}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 200 * retryCount));
      }
    }
  },

  async deleteTask(taskId: string) {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);

    if (error) handleSupabaseError(error);
  },

  async cloneTask(taskData: Omit<Tables['tasks']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('tasks')
      .insert({ ...taskData, created_by: user.id })
      .select('*')
      .single();

    if (error) handleSupabaseError(error);
    return data;
  }
};

// Projects
export const projectService = {
  async getProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createProject(project: Omit<Tables['projects']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('projects')
      .insert({ ...project, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateProject(projectId: string, updates: Partial<Tables['projects']['Update']>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Get current version for optimistic locking
    const { data: currentProject, error: fetchError } = await supabase
      .from('projects')
      .select('updated_at, version')
      .eq('id', projectId)
      .single();

    if (fetchError) handleSupabaseError(fetchError);
    if (!currentProject) throw new Error('Project not found');

    const newVersion = (currentProject.version || 0) + 1;

    const { data, error } = await supabase
      .from('projects')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
        version: newVersion
      })
      .eq('id', projectId)
      .eq('version', currentProject.version || 0) // Optimistic locking
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows updated - version conflict
        throw new Error('Project was modified by another user. Please refresh and try again.');
      }
      handleSupabaseError(error);
    }

    return data;
  },

  async deleteProject(projectId: string) {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (error) handleSupabaseError(error);
  },

  async cloneProject(projectData: Omit<Tables['projects']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('projects')
      .insert({ ...projectData, created_by: user.id })
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  }
};

// User Groups
export const userGroupService = {
  async getUserGroups() {
    const { data, error } = await supabase
      .from('user_groups')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createUserGroup(group: Omit<Tables['user_groups']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('user_groups')
      .insert({ ...group, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateUserGroup(groupId: string, updates: Partial<Tables['user_groups']['Update']>) {
    const { data, error } = await supabase
      .from('user_groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', groupId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteUserGroup(groupId: string) {
    const { error } = await supabase
      .from('user_groups')
      .delete()
      .eq('id', groupId);
    
    if (error) handleSupabaseError(error);
  }
};

// Folders
export const folderService = {
  async getFolders() {
    const { data, error } = await supabase
      .from('folders')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createFolder(folder: Omit<Tables['folders']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('folders')
      .insert({ ...folder, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateFolder(folderId: string, updates: Partial<Tables['folders']['Update']>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Get current version for optimistic locking
    const { data: currentFolder, error: fetchError } = await supabase
      .from('folders')
      .select('updated_at, version')
      .eq('id', folderId)
      .single();

    if (fetchError) handleSupabaseError(fetchError);
    if (!currentFolder) throw new Error('Folder not found');

    const newVersion = (currentFolder.version || 0) + 1;

    const { data, error } = await supabase
      .from('folders')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
        version: newVersion
      })
      .eq('id', folderId)
      .eq('version', currentFolder.version || 0) // Optimistic locking
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows updated - version conflict
        throw new Error('Folder was modified by another user. Please refresh and try again.');
      }
      handleSupabaseError(error);
    }

    return data;
  },

  async deleteFolder(folderId: string) {
    const { error } = await supabase
      .from('folders')
      .delete()
      .eq('id', folderId);
    
    if (error) handleSupabaseError(error);
  }
};

// Kanban Columns
export const columnService = {
  async getColumns() {
    const { data, error } = await supabase
      .from('kanban_columns')
      .select('*')
      .order('position');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createColumn(column: Omit<Tables['kanban_columns']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('kanban_columns')
      .insert({ ...column, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateColumn(columnId: string, updates: Partial<Tables['kanban_columns']['Update']>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Get current version for optimistic locking
    const { data: currentColumn, error: fetchError } = await supabase
      .from('kanban_columns')
      .select('updated_at, version')
      .eq('id', columnId)
      .single();

    if (fetchError) handleSupabaseError(fetchError);
    if (!currentColumn) throw new Error('Column not found');

    const newVersion = (currentColumn.version || 0) + 1;

    const { data, error } = await supabase
      .from('kanban_columns')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
        version: newVersion
      })
      .eq('id', columnId)
      .eq('version', currentColumn.version || 0) // Optimistic locking
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows updated - version conflict
        throw new Error('Column was modified by another user. Please refresh and try again.');
      }
      handleSupabaseError(error);
    }

    return data;
  },

  async deleteColumn(columnId: string) {
    const { error } = await supabase
      .from('kanban_columns')
      .delete()
      .eq('id', columnId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task Comments
export const commentService = {
  async addComment(comment: Tables['task_comments']['Insert']) {
    const { data, error } = await supabase
      .from('task_comments')
      .insert(comment)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateComment(commentId: string, content: string) {
    const { data, error } = await supabase
      .from('task_comments')
      .update({ 
        content, 
        edited: true, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', commentId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteComment(commentId: string) {
    const { error } = await supabase
      .from('task_comments')
      .delete()
      .eq('id', commentId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task History
export const historyService = {
  async addHistoryEntry(entry: Tables['task_history']['Insert']) {
    const { data, error } = await supabase
      .from('task_history')
      .insert(entry)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  }
};

// Task Durations
export const durationService = {
  async getTaskDurations(taskId: string) {
    const { data, error } = await supabase
      .from('task_durations')
      .select('*')
      .eq('task_id', taskId)
      .order('start_time', { ascending: true });

    if (error) handleSupabaseError(error);
    return data || [];
  },

  async addDuration(duration: Tables['task_durations']['Insert']) {
    const { data, error } = await supabase
      .from('task_durations')
      .insert(duration)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async updateDuration(durationId: string, updates: Partial<Tables['task_durations']['Update']>) {
    const { data, error } = await supabase
      .from('task_durations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', durationId)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async endCurrentDuration(taskId: string, status: string) {
    const { data, error } = await supabase
      .from('task_durations')
      .update({
        end_time: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('task_id', taskId)
      .eq('status', status)
      .is('end_time', null)
      .select();

    if (error) handleSupabaseError(error);
    return data;
  }
};

// Skillset Groups
export const skillsetService = {
  async getSkillsetGroups() {
    const { data, error } = await supabase
      .from('skillset_groups')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createSkillsetGroup(skillset: Omit<Tables['skillset_groups']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('skillset_groups')
      .insert({ ...skillset, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateSkillsetGroup(skillsetId: string, updates: Partial<Tables['skillset_groups']['Update']>) {
    const { data, error } = await supabase
      .from('skillset_groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', skillsetId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteSkillsetGroup(skillsetId: string) {
    const { error } = await supabase
      .from('skillset_groups')
      .delete()
      .eq('id', skillsetId);
    
    if (error) handleSupabaseError(error);
  }
};

// User Capacities
export const capacityService = {
  async getUserCapacities() {
    const { data, error } = await supabase
      .from('user_capacities')
      .select('*')
      .order('effective_from');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async addUserCapacity(capacity: Tables['user_capacities']['Insert']) {
    const { data, error } = await supabase
      .from('user_capacities')
      .insert(capacity)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateUserCapacity(capacityId: string, updates: Partial<Tables['user_capacities']['Update']>) {
    const { data, error } = await supabase
      .from('user_capacities')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', capacityId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteUserCapacity(capacityId: string) {
    const { error } = await supabase
      .from('user_capacities')
      .delete()
      .eq('id', capacityId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task Efforts
export const effortService = {
  async getTaskEfforts() {
    const { data, error } = await supabase
      .from('task_efforts')
      .select('*');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async addTaskEffort(effort: Tables['task_efforts']['Insert']) {
    const { data, error } = await supabase
      .from('task_efforts')
      .insert(effort)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateTaskEffort(effortId: string, updates: Partial<Tables['task_efforts']['Update']>) {
    const { data, error } = await supabase
      .from('task_efforts')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', effortId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteTaskEffort(effortId: string) {
    const { error } = await supabase
      .from('task_efforts')
      .delete()
      .eq('id', effortId);

    if (error) handleSupabaseError(error);
  }
};

// Notifications
export const notificationService = {
  async getNotifications(userId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('recipient_id', userId)
      .order('created_at', { ascending: false });

    if (error) handleSupabaseError(error);
    return data || [];
  },

  async getUnreadCount(userId: string) {
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('recipient_id', userId)
      .eq('is_read', false);

    if (error) handleSupabaseError(error);
    return count || 0;
  },

  async createNotification(notification: Tables['notifications']['Insert']) {
    const { data, error } = await supabase
      .from('notifications')
      .insert(notification)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async markAsRead(notificationId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async markAsUnread(notificationId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: false })
      .eq('id', notificationId)
      .select()
      .single();

    if (error) handleSupabaseError(error);
    return data;
  },

  async markAllAsRead(userId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('recipient_id', userId)
      .eq('is_read', false)
      .select();

    if (error) handleSupabaseError(error);
    return data || [];
  },

  async deleteNotification(notificationId: string) {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) handleSupabaseError(error);
  },

  async deleteOldNotifications(userId: string, daysOld: number = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('recipient_id', userId)
      .lt('created_at', cutoffDate.toISOString());

    if (error) handleSupabaseError(error);
  }
};

// Re-export automation services
export {
  automationWorkflowService,
  automationExecutionService,
  automationTriggerService
} from './automationService';

// Re-export dependency services
export { dependencyService } from './dependencyService';
export { dateCalculationService } from './dateCalculationService';
