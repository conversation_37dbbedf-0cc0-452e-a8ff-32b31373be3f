import { describe, it, expect, beforeEach, vi } from 'vitest';
import { cloneService } from '../services/cloneService';
import { taskService, projectService } from '../services/supabaseService';

// Mock the services
vi.mock('../services/supabaseService', () => ({
  taskService: {
    cloneTask: vi.fn(),
    getTasksByProject: vi.fn(),
  },
  projectService: {
    cloneProject: vi.fn(),
    getProjectById: vi.fn(),
  },
}));

describe('CloneService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateCloneName', () => {
    it('should generate clone name with prefix for first clone', () => {
      const existingNames = ['Original Task', 'Another Task'];
      const originalName = 'Original Task';
      
      const result = cloneService.generateCloneName(originalName, existingNames);
      
      expect(result).toBe('Clone_1_Original Task');
    });

    it('should increment clone number for subsequent clones', () => {
      const existingNames = [
        'Original Task',
        'Clone_1_Original Task',
        'Clone_2_Original Task',
        'Another Task'
      ];
      const originalName = 'Original Task';
      
      const result = cloneService.generateCloneName(originalName, existingNames);
      
      expect(result).toBe('Clone_3_Original Task');
    });

    it('should handle non-sequential clone numbers', () => {
      const existingNames = [
        'Original Task',
        'Clone_1_Original Task',
        'Clone_5_Original Task',
        'Another Task'
      ];
      const originalName = 'Original Task';
      
      const result = cloneService.generateCloneName(originalName, existingNames);
      
      expect(result).toBe('Clone_6_Original Task');
    });
  });

  describe('cloneTask', () => {
    it('should clone a task with fresh time tracking', async () => {
      const mockTask = {
        id: 'task-1',
        title: 'Original Task',
        description: 'Task description',
        status: 'todo',
        priority: 'medium' as const,
        projectId: 'project-1',
        assignedUsers: ['user-1'],
        assignedGroups: [],
        startDate: '2024-01-01',
        dueDate: '2024-01-31',
        effort: { estimated: 8, actual: 0 },
        customFieldValues: {},
        durations: [{ status: 'todo', startTime: '2024-01-01T00:00:00Z', endTime: undefined }],
        history: [{ id: 'hist-1', taskId: 'task-1', timestamp: '2024-01-01T00:00:00Z', field: 'created', oldValue: '', newValue: 'Task created', userId: 'user-1' }]
      };

      const mockExistingTasks = [mockTask];
      const mockClonedTask = { ...mockTask, id: 'task-2', title: 'Clone_1_Original Task' };

      vi.mocked(taskService.getTasksByProject).mockResolvedValue(mockExistingTasks);
      vi.mocked(taskService.cloneTask).mockResolvedValue(mockClonedTask);

      const result = await cloneService.cloneTask('task-1');

      expect(result.success).toBe(true);
      expect(taskService.cloneTask).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Clone_1_Original Task',
          durations: expect.arrayContaining([
            expect.objectContaining({
              status: 'todo',
              startTime: expect.any(String),
              endTime: undefined
            })
          ]),
          history: expect.arrayContaining([
            expect.objectContaining({
              field: 'created',
              newValue: 'Task cloned'
            })
          ])
        })
      );
    });
  });

  describe('cloneProject', () => {
    it('should clone a project with all tasks and subtasks', async () => {
      const mockProject = {
        id: 'project-1',
        name: 'Original Project',
        description: 'Project description',
        color: 'bg-blue-500',
        folderId: 'folder-1'
      };

      const mockTasks = [
        {
          id: 'task-1',
          title: 'Task 1',
          projectId: 'project-1',
          parentTaskId: null,
          status: 'todo',
          priority: 'medium' as const,
          assignedUsers: [],
          assignedGroups: [],
          startDate: '',
          dueDate: '',
          effort: { estimated: 0, actual: 0 },
          customFieldValues: {},
          durations: [],
          history: []
        },
        {
          id: 'task-2',
          title: 'Subtask 1',
          projectId: 'project-1',
          parentTaskId: 'task-1',
          status: 'todo',
          priority: 'medium' as const,
          assignedUsers: [],
          assignedGroups: [],
          startDate: '',
          dueDate: '',
          effort: { estimated: 0, actual: 0 },
          customFieldValues: {},
          durations: [],
          history: []
        }
      ];

      const mockExistingProjects = [mockProject];
      const mockClonedProject = { ...mockProject, id: 'project-2', name: 'Clone_1_Original Project' };

      vi.mocked(projectService.getProjectById).mockResolvedValue(mockProject);
      vi.mocked(projectService.cloneProject).mockResolvedValue(mockClonedProject);
      vi.mocked(taskService.getTasksByProject).mockResolvedValue(mockTasks);
      vi.mocked(taskService.cloneTask).mockResolvedValue(mockTasks[0]);

      const result = await cloneService.cloneProject('project-1');

      expect(result.success).toBe(true);
      expect(projectService.cloneProject).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Clone_1_Original Project'
        })
      );
    });
  });
});
