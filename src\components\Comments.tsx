import React, { useState } from 'react';
import { Comment } from '../types';
import { formatDistanceToNow } from 'date-fns';
import { Reply, Edit2, Trash2, Send } from 'lucide-react';

interface CommentsProps {
  comments: Comment[];
  onAddComment: (content: string, parentId?: string) => void;
  onEditComment: (commentId: string, content: string) => void;
  onDeleteComment: (commentId: string) => void;
}

export default function Comments({ comments, onAddComment, onEditComment, onDeleteComment }: CommentsProps) {
  const [newComment, setNewComment] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      onAddComment(newComment);
      setNewComment('');
    }
  };

  const handleEdit = (commentId: string) => {
    if (editContent.trim()) {
      onEditComment(commentId, editContent);
      setEditingComment(null);
      setEditContent('');
    }
  };

  const handleReply = (parentId: string) => {
    if (replyContent.trim()) {
      onAddComment(replyContent, parentId);
      setReplyingTo(null);
      setReplyContent('');
    }
  };

  const CommentItem = ({ comment, level = 0 }: { comment: Comment; level?: number }) => (
    <div className="space-y-2" style={{ marginLeft: `${level * 24}px` }}>
      <div className="bg-white p-3 rounded-lg shadow-sm">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium">
                {comment.userId.slice(0, 2).toUpperCase()}
              </span>
            </div>
            <div>
              <span className="font-medium">User {comment.userId}</span>
              <span className="text-xs text-gray-500 ml-2">
                {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true })}
              </span>
              {comment.edited && (
                <span className="text-xs text-gray-400 ml-2">(edited)</span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setReplyingTo(comment.id)}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <Reply className="w-4 h-4" />
            </button>
            <button
              onClick={() => {
                setEditingComment(comment.id);
                setEditContent(comment.content);
              }}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <Edit2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDeleteComment(comment.id)}
              className="p-1 hover:bg-gray-100 rounded text-red-500"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {editingComment === comment.id ? (
          <div className="mt-2">
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full p-2 border rounded-lg"
              rows={2}
            />
            <div className="flex justify-end gap-2 mt-2">
              <button
                onClick={() => setEditingComment(null)}
                className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
              >
                Cancel
              </button>
              <button
                onClick={() => handleEdit(comment.id)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        ) : (
          <p className="mt-2 text-gray-700">{comment.content}</p>
        )}
      </div>

      {replyingTo === comment.id && (
        <div className="flex gap-2 items-start mt-2">
          <textarea
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="Write a reply..."
            className="flex-1 p-2 border rounded-lg"
            rows={2}
          />
          <div className="flex flex-col gap-2">
            <button
              onClick={() => handleReply(comment.id)}
              className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Send className="w-4 h-4" />
            </button>
            <button
              onClick={() => setReplyingTo(null)}
              className="p-2 hover:bg-gray-100 rounded"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {comment.replies?.map((reply) => (
        <CommentItem key={reply.id} comment={reply} level={level + 1} />
      ))}
    </div>
  );

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Comments</h3>
      
      <form onSubmit={handleSubmit} className="flex gap-2 items-start">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Write a comment..."
          className="flex-1 p-2 border rounded-lg"
          rows={2}
        />
        <button
          type="submit"
          className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          <Send className="w-4 h-4" />
        </button>
      </form>

      <div className="space-y-4">
        {comments.map((comment) => (
          <CommentItem key={comment.id} comment={comment} />
        ))}
      </div>
    </div>
  );
}