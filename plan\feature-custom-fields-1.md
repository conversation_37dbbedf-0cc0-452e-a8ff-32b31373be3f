---
goal: Implement Custom Fields Functionality for Tasks and Subtasks with Admin Management
version: 1.0
date_created: 2025-01-09
last_updated: 2025-01-09
owner: Development Team
tags: [feature, admin, forms, database, ui]
---

# Introduction

This plan implements a comprehensive custom fields system that allows admin users to define custom attributes for tasks and subtasks. The system includes an admin interface for managing custom field definitions and extends task/subtask forms with an expandable custom fields section. The implementation follows application-level validation patterns and maintains consistency across all task management interfaces.

## 1. Requirements & Constraints

- **REQ-001**: Admin users must be able to create, edit, and delete custom field definitions
- **REQ-002**: Custom fields must support text, number, date, and dropdown field types
- **REQ-003**: Custom fields must apply to both tasks and subtasks uniformly
- **REQ-004**: Task and subtask forms must include an expandable custom fields section
- **REQ-005**: Custom field values must be stored and retrievable for filtering/reporting
- **REQ-006**: UI must remain modern, clean, and minimal
- **REQ-007**: Implementation must not disturb existing codebase functionality

- **SEC-001**: Only admin users can manage custom field definitions
- **SEC-002**: Custom field values follow existing task/subtask access controls
- **SEC-003**: Custom field data must be validated on both client and server

- **CON-001**: Must use existing Supabase database and authentication system. connect to supabase diredctly using supabase tool to run commands and check things
- **CON-002**: Must follow existing UI patterns and component structure
- **CON-003**: Must maintain backward compatibility with existing tasks/subtasks
- **CON-004**: Must use application-level validation over complex RLS policies

- **GUD-001**: Follow existing admin settings page patterns
- **GUD-002**: Use consistent form field components and styling
- **GUD-003**: Implement modular components that don't affect existing functionality

- **PAT-001**: Follow existing admin page structure (UserManager, ColumnManager patterns)
- **PAT-002**: Use JSONB storage pattern for flexible custom field data
- **PAT-003**: Implement expandable sections pattern used in TaskForm/SubtaskForm

## 2. Implementation Steps

### Phase 1: Database Schema and Types
1. Create custom_fields table in Supabase
2. Add custom_field_values JSONB column to tasks table
3. Update TypeScript interfaces for custom fields
4. Create Supabase service functions for custom fields

### Phase 2: Admin Interface
1. Create CustomFieldManager component
2. Add Custom Fields navigation to admin settings
3. Implement CRUD operations for custom field definitions
4. Add form validation and error handling

### Phase 3: Form Extensions
1. Create CustomFieldsSection component
2. Integrate custom fields into TaskForm
3. Integrate custom fields into SubtaskForm
4. Implement field type-specific input components

### Phase 4: Data Integration
1. Update task/subtask creation and update logic
2. Implement custom field value validation
3. Update data transformation functions
4. Add custom field support to filtering/search

### Phase 5: Testing and Polish
1. Test admin custom field management
2. Test task/subtask form integration
3. Verify data persistence and retrieval
4. Polish UI and user experience

## 3. Alternatives

- **ALT-001**: Store custom fields as separate table with foreign keys - rejected due to complexity and performance concerns
- **ALT-002**: Use PostgreSQL custom types - rejected due to migration complexity and flexibility limitations
- **ALT-003**: Implement as metadata table - rejected due to query complexity and existing JSONB patterns

## 4. Dependencies

- **DEP-001**: Existing Supabase database and authentication system
- **DEP-002**: Current admin settings infrastructure (SupabaseSidebar, admin routing)
- **DEP-003**: Existing TaskForm and SubtaskForm components
- **DEP-004**: Current task/subtask data models and TypeScript interfaces
- **DEP-005**: Existing UI component library and styling patterns

## 5. Files

- **FILE-001**: `supabase-custom-fields.sql` - Database schema for custom fields table
- **FILE-002**: `src/types/customFields.ts` - TypeScript interfaces for custom fields
- **FILE-003**: `src/components/CustomFieldManager.tsx` - Admin interface for managing custom fields
- **FILE-004**: `src/components/CustomFieldsSection.tsx` - Expandable custom fields form section
- **FILE-005**: `src/components/CustomFieldInput.tsx` - Individual custom field input component
- **FILE-006**: `src/services/customFieldService.ts` - Supabase service for custom fields
- **FILE-007**: `src/store/useSupabaseStore.ts` - Updated store with custom fields support
- **FILE-008**: `src/types/index.ts` - Updated Task and Subtask interfaces
- **FILE-009**: `src/components/TaskForm.tsx` - Updated with custom fields section
- **FILE-010**: `src/components/SubtaskForm.tsx` - Updated with custom fields section
- **FILE-011**: `src/components/SupabaseSidebar.tsx` - Updated admin navigation

## 6. Testing

- **TEST-001**: Admin can create custom fields with all supported types
- **TEST-002**: Admin can edit and delete custom field definitions
- **TEST-003**: Custom fields appear in task and subtask forms
- **TEST-004**: Custom field values are saved and retrieved correctly
- **TEST-005**: Custom fields section is expandable/collapsible
- **TEST-006**: Field validation works for all field types
- **TEST-007**: Existing tasks/subtasks work without custom field values
- **TEST-008**: Non-admin users cannot access custom field management

## 7. Risks & Assumptions

- **RISK-001**: JSONB storage may impact query performance with large datasets
- **RISK-002**: Custom field changes may affect existing task data integrity
- **RISK-003**: UI complexity may increase with many custom fields

- **ASSUMPTION-001**: Admin users will manage custom fields responsibly
- **ASSUMPTION-002**: Custom field requirements won't exceed JSONB limitations
- **ASSUMPTION-003**: Existing task/subtask forms can accommodate additional fields
- **ASSUMPTION-004**: Current database performance can handle JSONB queries

## 8. Related Specifications / Further Reading

- Existing admin settings patterns in UserManager.tsx and ColumnManager.tsx
- Current task/subtask form implementations in TaskForm.tsx and SubtaskForm.tsx
- Supabase JSONB documentation for flexible data storage
- Current database schema documentation in docs/database-schema.md
