import React from 'react';
import { prepareContentForEditor, htmlToPlainText, isHtmlContent, getContentPreview } from '../utils/richTextUtils';

interface RichTextDisplayProps {
  content: string;
  className?: string;
  truncate?: boolean;
  maxLength?: number;
  showPlainText?: boolean;
}

export default function RichTextDisplay({
  content,
  className = '',
  truncate = false,
  maxLength = 55,
  showPlainText = false
}: RichTextDisplayProps) {
  
  // If we need to show plain text or truncate, convert to plain text
  if (showPlainText || truncate) {
    const plainText = htmlToPlainText(content);
    
    if (truncate) {
      const preview = getContentPreview(content, maxLength);
      return (
        <span className={`rich-text-display-plain ${className}`}>
          {preview}
        </span>
      );
    }
    
    return (
      <span className={`rich-text-display-plain ${className}`}>
        {plainText || 'No description'}
      </span>
    );
  }

  // For full rich text display
  if (!content) {
    return (
      <div className={`rich-text-display ${className}`}>
        <p className="text-gray-500 italic">No description</p>
      </div>
    );
  }

  // Prepare content for display
  const displayContent = isHtmlContent(content) 
    ? prepareContentForEditor(content)
    : prepareContentForEditor(content); // This will convert plain text to HTML

  return (
    <div 
      className={`rich-text-display ${className}`}
      dangerouslySetInnerHTML={{ __html: displayContent }}
      style={{
        // Styles to match Quill's output but for read-only display
        fontSize: '0.875rem',
        lineHeight: '1.5',
        color: '#374151'
      }}
    />
  );
}

// Separate component for inline rich text display (for cards, lists, etc.)
interface InlineRichTextDisplayProps {
  content: string;
  maxLength?: number;
  className?: string;
}

export function InlineRichTextDisplay({
  content,
  maxLength = 55,
  className = ''
}: InlineRichTextDisplayProps) {
  const preview = getContentPreview(content, maxLength);
  
  return (
    <span className={`inline-rich-text-display ${className}`}>
      {preview}
    </span>
  );
}

// Component for rich text in task cards (maintains existing styling)
interface TaskCardRichTextProps {
  content: string;
  maxLength?: number;
}

export function TaskCardRichText({
  content,
  maxLength = 55
}: TaskCardRichTextProps) {
  const preview = getContentPreview(content, maxLength);
  
  return (
    <p className="text-sm text-gray-600 mt-1">
      {preview}
    </p>
  );
}
