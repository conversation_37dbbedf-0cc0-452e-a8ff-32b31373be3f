import React from 'react';
import { X } from 'lucide-react';

interface FilterChipProps {
  label: string;
  value: string;
  onRemove: (value: string) => void;
  color?: string;
}

export default function FilterChip({ label, value, onRemove, color = 'bg-blue-100 text-blue-800' }: FilterChipProps) {
  return (
    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${color}`}>
      {label}
      <button
        onClick={() => onRemove(value)}
        className="hover:bg-black hover:bg-opacity-10 rounded-full p-0.5 transition-colors"
        aria-label={`Remove ${label} filter`}
      >
        <X className="w-3 h-3" />
      </button>
    </span>
  );
}
