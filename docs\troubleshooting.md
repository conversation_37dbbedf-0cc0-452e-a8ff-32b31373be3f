# Troubleshooting Guide

This guide covers common issues and their solutions for the Project Management Tool.

## 🚨 Critical Setup Issues

### User Registration Fails
**Symptoms**: New users can't log in, "profile not found" errors

**Cause**: User creation trigger not installed

**Solution**:
```sql
-- Check if trigger exists
SELECT trigger_name FROM information_schema.triggers 
WHERE event_object_table = 'users' AND event_object_schema = 'auth';

-- If missing, run supabase-triggers.sql again
```

### Profile Update Errors (406 Error)
**Symptoms**: Admin can't update user profiles, "multiple (or no) rows returned"

**Cause**: Missing admin RLS policy

**Solution**:
```sql
-- Check admin policy exists
SELECT policyname FROM pg_policies 
WHERE tablename = 'user_profiles' AND cmd = 'UPDATE';

-- Should show both user and admin policies
-- If missing admin policy, run:
CREATE POLICY "Admins can update any profile" ON user_profiles 
FOR UPDATE USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### Subtasks Not Working
**Symptoms**: Subtasks don't save, console errors about missing column

**Cause**: Subtasks column missing from tasks table

**Solution**:
```sql
-- Check if subtasks column exists
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'tasks' AND column_name = 'subtasks';

-- If missing, run supabase-add-subtasks.sql
```

## 🔧 Common Application Issues

### Authentication Problems

#### Can't Log In
1. **Check environment variables**: Ensure `.env.local` has correct Supabase URL and key
2. **Verify user exists**: Check Supabase Auth > Users
3. **Check email confirmation**: Ensure user email is confirmed
4. **Browser console**: Look for authentication errors

#### Profile Not Found After Login
1. **Check user_profiles table**: User should have corresponding profile
2. **Verify trigger**: Ensure user creation trigger is active
3. **Manual profile creation**: Create profile manually if needed

### Database Connection Issues

#### "Failed to connect to Supabase"
1. **Check project status**: Ensure Supabase project is not paused
2. **Verify credentials**: Double-check URL and anon key
3. **Network issues**: Check internet connection and firewall

#### RLS Policy Violations
1. **Check user role**: Ensure user has correct permissions
2. **Verify policies**: Check all RLS policies are applied
3. **Admin access**: Ensure admin users have proper policies

### Task Management Issues

#### Tasks Not Saving
1. **Check required fields**: Ensure all required fields are filled
2. **Verify permissions**: Check user can create tasks
3. **Database constraints**: Look for foreign key violations

#### Subtask Comments Not Working
1. **Check subtasks column**: Ensure tasks table has subtasks JSONB column
2. **Verify functions**: Ensure subtask comment functions exist in store
3. **Console errors**: Check browser console for specific errors

### Clone Functionality Issues

#### Clone Button Not Working
1. **Check permissions**: Ensure user has permission to create tasks/projects
2. **Verify authentication**: User must be logged in to clone items
3. **Console errors**: Look for specific error messages in browser console

#### Three-Dot Menu Not Opening
1. **Event bubbling**: Check if parent click handlers are interfering
2. **Menu state**: Verify menu state is being managed correctly
3. **Browser console**: Look for JavaScript errors preventing menu display

#### Cloned Items Missing Attributes
1. **Database field names**: Ensure clone service uses correct snake_case field names
2. **Data transformation**: Verify original data is being accessed correctly
3. **Custom fields**: Check that all custom fields are properly configured

#### Project Clone Not Including Tasks
1. **Task filtering**: Verify tasks are being filtered by correct project_id field
2. **Database permissions**: Ensure user can access all tasks in the project
3. **Transaction integrity**: Check that all clone operations complete successfully

#### Clone Naming Issues
1. **Name collision**: System automatically handles duplicate names with incremental numbering
2. **Character limits**: Ensure cloned names don't exceed database field limits
3. **Special characters**: Verify names with special characters are handled correctly

## 🔍 Debugging Tools

### Database Queries

#### Check Table Structure
```sql
-- List all tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' ORDER BY table_name;

-- Check specific table columns
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'your_table_name';
```

#### Verify RLS Policies
```sql
-- Check all policies
SELECT schemaname, tablename, policyname, cmd 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, cmd;
```

#### Test Triggers
```sql
-- Check active triggers
SELECT trigger_name, event_object_table, action_statement 
FROM information_schema.triggers 
WHERE trigger_schema = 'public' OR event_object_schema = 'auth';
```

### Application Debugging

#### Browser Console
1. Open Developer Tools (F12)
2. Check Console tab for errors
3. Look for network failures in Network tab
4. Check Application tab for localStorage issues

#### Supabase Dashboard
1. Go to Logs section
2. Filter by error level
3. Check API logs for failed requests
4. Monitor real-time activity

## 🛠 Performance Issues

### Slow Loading
1. **Check database indexes**: Ensure proper indexing on frequently queried columns
2. **Optimize queries**: Review complex queries for performance
3. **Network latency**: Consider Supabase region proximity

### Memory Issues
1. **Clear browser cache**: Remove old cached data
2. **Check for memory leaks**: Monitor browser memory usage
3. **Restart application**: Fresh start can resolve state issues

## 📋 Maintenance Tasks

### Regular Checks
- [ ] Monitor Supabase project usage
- [ ] Check for failed authentication attempts
- [ ] Review error logs weekly
- [ ] Verify backup status
- [ ] Test critical user flows

### Database Maintenance
- [ ] Monitor table sizes and growth
- [ ] Check for orphaned records
- [ ] Verify foreign key constraints
- [ ] Review and optimize slow queries

## 🆘 Getting Help

### Before Asking for Help
1. **Check this troubleshooting guide**
2. **Review browser console errors**
3. **Check Supabase dashboard logs**
4. **Verify all setup steps completed**

### When Creating Issues
Include:
- **Error messages** (exact text)
- **Steps to reproduce** the problem
- **Browser and version** information
- **Screenshots** if applicable
- **Database schema** verification results

### Support Resources
- [Supabase Documentation](https://supabase.com/docs)
- [React Documentation](https://react.dev)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- Project GitHub Issues
