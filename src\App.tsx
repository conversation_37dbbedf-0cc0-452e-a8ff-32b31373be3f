import React from 'react';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import SupabaseSidebar from './components/SupabaseSidebar';

function App() {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <SupabaseSidebar />
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
}

export default App;