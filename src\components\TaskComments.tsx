import React, { useState, useEffect, useMemo } from 'react';
import { format } from 'date-fns';
import { TaskComment } from '../types';
import { MessageSquare, Send, Edit2, Trash2, Reply } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { extractMentions, validateMentions, createMentionNotificationContent, shouldNotifyUser } from '../utils/mentionUtils';
import { notificationService } from '../services/supabaseService';



interface TaskCommentsProps {
  taskId: string;
  subtaskId?: string;
  comments: TaskComment[];
}

export default function TaskComments({ taskId, subtaskId, comments }: TaskCommentsProps) {
  const { addComment, updateComment, deleteComment, addSubtaskComment, updateSubtaskComment, deleteSubtaskComment, users, tasks } = useSupabaseStore();
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { getCurrentUser } = await import('../lib/supabase');
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to get current user:', error);
      }
    };
    getCurrentUser();
  }, []);

  // Subtask comment functions are now imported from the store
  const [newComment, setNewComment] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  // Handle undefined comments
  const safeComments = useMemo(() => comments || [], [comments]);
  const [localComments, setLocalComments] = useState<TaskComment[]>(safeComments);

  useEffect(() => {
    setLocalComments(safeComments);
  }, [safeComments]);

  // Helper function to create notifications for mentions
  const createMentionNotifications = async (commentContent: string, commentId: string) => {
    if (!currentUser) return;

    console.log('Debug - Current user for notifications:', {
      id: currentUser.id,
      email: currentUser.email,
      name: currentUser.name
    });

    try {
      // Extract mentions from comment content
      const mentions = extractMentions(commentContent);
      if (mentions.length === 0) return;

      // Validate mentions against existing users
      const validMentionedUsers = validateMentions(mentions, users);
      if (validMentionedUsers.length === 0) return;

      // Get task title for notification
      const task = tasks.find(t => t.id === taskId);
      const taskTitle = task?.title || 'Unknown Task';

      // Create notifications for each mentioned user
      for (const mentionedUser of validMentionedUsers) {
        if (shouldNotifyUser(mentionedUser.id, currentUser.id)) {
          const { title, content } = createMentionNotificationContent(
            currentUser.name || currentUser.email,
            taskTitle,
            commentContent
          );

          const notificationData = {
            recipient_id: mentionedUser.id,
            sender_id: currentUser.id,
            type: 'mention',
            title,
            content,
            task_id: taskId,
            comment_id: commentId
          };

          console.log('Debug - Creating notification:', notificationData);
          await notificationService.createNotification(notificationData);
        }
      }
    } catch (error) {
      console.error('Failed to create mention notifications:', error);
      // Don't throw error to avoid breaking comment creation
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !currentUser) return;

    const commentData: Omit<TaskComment, 'id' | 'timestamp'> = {
      content: newComment,
      userId: currentUser.id,
      taskId: subtaskId || taskId,
    };

    try {
      let createdComment: TaskComment;
      if (subtaskId) {
        createdComment = await addSubtaskComment(taskId, subtaskId, commentData);
      } else {
        createdComment = await addComment(taskId, commentData);
      }

      // Create notifications for mentions after comment is successfully created
      await createMentionNotifications(newComment, createdComment.id);

      setNewComment('');
    } catch (error) {
      console.error('Failed to add comment:', error);
      // Optionally show user feedback about the error
    }
  };

  const handleEdit = (commentId: string) => {
    const comment = localComments.find(c => c.id === commentId);
    if (!comment) {
      console.warn(`Comment with id ${commentId} not found`);
      return;
    }
    
    // Only allow editing if it's the user's own comment
    if (comment.userId !== 'current-user') {
      console.warn('Cannot edit another user\'s comment');
      return;
    }

    setEditingComment(commentId);
    setEditContent(comment.content);
  };

  const handleUpdate = async (commentId: string) => {
    if (!editContent.trim()) return;

    try {
      if (subtaskId) {
        await updateSubtaskComment(taskId, subtaskId, commentId, editContent);
      } else {
        await updateComment(taskId, commentId, editContent);
      }
      setEditingComment(null);
      setEditContent('');
    } catch (error) {
      console.error('Failed to update comment:', error);
      // Optionally show user feedback about the error
    }
  };

  const handleReply = async (commentId: string) => {
    if (!replyContent.trim()) return;

    const replyData: Omit<TaskComment, "id" | "timestamp"> = {
      content: replyContent,
      userId: currentUser?.id || '',
      parentId: commentId,
      taskId: subtaskId || taskId,
    };

    try {
      let createdReply: TaskComment;
      if (subtaskId) {
        createdReply = await addSubtaskComment(taskId, subtaskId, replyData);
      } else {
        createdReply = await addComment(taskId, replyData);
      }

      // Create notifications for mentions in reply
      await createMentionNotifications(replyContent, createdReply.id);

      setReplyingTo(null);
      setReplyContent('');
    } catch (error) {
      console.error('Failed to add reply:', error);
      // Optionally show user feedback about the error
    }
  };

  const handleDelete = async (commentId: string) => {
    try {
      if (subtaskId) {
        await deleteSubtaskComment(taskId, subtaskId, commentId);
      } else {
        await deleteComment(taskId, commentId);
      }
      // Local state will be updated through the comments prop change
    } catch (error) {
      console.error('Failed to delete comment:', error);
      // Optionally show user feedback about the error
    }
  };

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : userId;
  };

  const renderComment = (comment: TaskComment, level = 0) => (
    <div key={comment.id} className="space-y-2" style={{ marginLeft: `${level * 24}px` }}>
      <div className="flex items-start gap-3 group">
        <div className="w-8 h-8 rounded-full bg-gray-200 flex-shrink-0" />
        <div className="flex-1">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{getUserName(comment.userId)}</span>
                <span className="text-sm text-gray-500">
                  {format(new Date(comment.timestamp), 'MMM d, HH:mm')}
                </span>
                {comment.edited && (
                  <span className="text-xs text-gray-400">(edited)</span>
                )}
              </div>
              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => handleEdit(comment.id)}
                  className="p-1 hover:bg-gray-200 rounded"
                >
                  <Edit2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(comment.id)}
                  className="p-1 hover:bg-gray-200 rounded text-red-600"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setReplyingTo(comment.id)}
                  className="p-1 hover:bg-gray-200 rounded"
                >
                  <Reply className="w-4 h-4" />
                </button>
              </div>
            </div>
            {editingComment === comment.id ? (
              <div className="flex gap-2">
                <input
                  type="text"
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="flex-1 border rounded px-2 py-1"
                  autoFocus
                />
                <button
                  onClick={() => handleUpdate(comment.id)}
                  className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Save
                </button>
              </div>
            ) : (
              <p>{comment.content}</p>
            )}
          </div>
          {replyingTo === comment.id && (
            <div className="mt-2 flex gap-2">
              <input
                type="text"
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Write a reply..."
                className="flex-1 border rounded px-3 py-2"
                autoFocus
              />
              <button
                onClick={() => handleReply(comment.id)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Reply
              </button>
            </div>
          )}
        </div>
      </div>
      {localComments
        .filter(c => c.parentId === comment.id)
        .map(reply => renderComment(reply, level + 1))}
    </div>
  );

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2 sticky top-0 bg-white py-2 z-10">
        <MessageSquare className="w-5 h-5" />
        Comments
      </h3>
      
      <form onSubmit={handleSubmit} className="flex gap-2 sticky top-12 bg-white py-2 z-10">
        <input
          type="text"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Write a comment..."
          className="flex-1 border rounded-lg px-3 py-2"
        />
        <button
          type="submit"
          className="px-8 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 whitespace-nowrap min-w-[120px] justify-center"
        >
          <Send className="w-4 h-4" />
          Send
        </button>
      </form>

      <div className="space-y-4">
        {localComments
          .filter(comment => !comment.parentId)
          .map(comment => renderComment(comment))}
      </div>
    </div>
  );
}