import React, { useEffect, useState } from 'react';
import { Copy, Download, Upload, Star, Search, Filter } from 'lucide-react';
import { useAutomationStore } from '../../store/useAutomationStore';
import { AutomationWorkflow } from '../../types/automation';

// Predefined workflow templates
const WORKFLOW_TEMPLATES: Array<Omit<AutomationWorkflow, 'id' | 'createdBy' | 'createdAt' | 'updatedAt' | 'executionCount' | 'lastExecutedAt'>> = [
  {
    name: 'Auto-assign High Priority Tasks',
    description: 'Automatically assign high priority tasks to team leads',
    isActive: false,
    isTemplate: true,
    triggerConfig: {
      type: 'task_created'
    },
    conditionConfig: {
      rootGroup: {
        id: 'root',
        logic: 'AND',
        conditions: [
          {
            id: 'priority-check',
            field: 'priority',
            operator: 'equals',
            value: 'high',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      }
    },
    actionConfig: [
      {
        type: 'assign_task',
        assignmentConfig: {
          userId: 'team-lead-id',
          assignmentType: 'add'
        }
      },
      {
        type: 'send_notification',
        templateConfig: {
          subject: 'High Priority Task Assigned',
          body: 'A high priority task "{{task.title}}" has been assigned to you.',
          recipients: ['team-lead-id']
        }
      }
    ]
  },
  {
    name: 'Status Change Notifications',
    description: 'Notify assignees when task status changes',
    isActive: false,
    isTemplate: true,
    triggerConfig: {
      type: 'task_status_changed'
    },
    actionConfig: [
      {
        type: 'send_notification',
        templateConfig: {
          subject: 'Task Status Updated',
          body: 'Task "{{task.title}}" status changed from {{trigger.oldStatus}} to {{trigger.newStatus}}.',
          recipients: ['{{task.assignedUsers}}']
        }
      }
    ]
  },
  {
    name: 'Overdue Task Reminder',
    description: 'Send daily reminders for overdue tasks',
    isActive: false,
    isTemplate: true,
    triggerConfig: {
      type: 'schedule',
      scheduleConfig: {
        frequency: 'daily',
        time: '09:00'
      }
    },
    conditionConfig: {
      rootGroup: {
        id: 'root',
        logic: 'AND',
        conditions: [
          {
            id: 'overdue-check',
            field: 'dueDate',
            operator: 'less_than',
            value: '{{current_timestamp}}',
            valueType: 'dynamic',
            entityType: 'task'
          },
          {
            id: 'not-done-check',
            field: 'status',
            operator: 'not_equals',
            value: 'done',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      }
    },
    actionConfig: [
      {
        type: 'send_notification',
        templateConfig: {
          subject: 'Overdue Task Reminder',
          body: 'Task "{{task.title}}" is overdue. Please update the status or due date.',
          recipients: ['{{task.assignedUsers}}', '{{task.ownerId}}']
        }
      }
    ]
  },
  {
    name: 'Auto-create Follow-up Tasks',
    description: 'Create follow-up tasks when a task is completed',
    isActive: false,
    isTemplate: true,
    triggerConfig: {
      type: 'task_status_changed'
    },
    conditionConfig: {
      rootGroup: {
        id: 'root',
        logic: 'AND',
        conditions: [
          {
            id: 'status-done',
            field: 'status',
            operator: 'equals',
            value: 'done',
            valueType: 'static',
            entityType: 'task'
          },
          {
            id: 'has-follow-up-tag',
            field: 'tags',
            operator: 'contains',
            value: 'follow-up',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      }
    },
    actionConfig: [
      {
        type: 'create_task',
        taskCreationConfig: {
          title: 'Follow-up: {{task.title}}',
          description: 'Follow-up task for completed task: {{task.title}}',
          priority: 'medium',
          assignedUsers: ['{{task.assignedUsers}}'],
          projectId: '{{task.projectId}}'
        }
      }
    ]
  },
  {
    name: 'Project Completion Notification',
    description: 'Notify team when all project tasks are completed',
    isActive: false,
    isTemplate: true,
    triggerConfig: {
      type: 'task_status_changed'
    },
    conditionConfig: {
      rootGroup: {
        id: 'root',
        logic: 'AND',
        conditions: [
          {
            id: 'status-done',
            field: 'status',
            operator: 'equals',
            value: 'done',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      }
    },
    actionConfig: [
      {
        type: 'send_notification',
        templateConfig: {
          subject: 'Project Task Completed',
          body: 'Task "{{task.title}}" in project {{project.name}} has been completed.',
          recipients: ['project-team']
        }
      }
    ]
  }
];

interface WorkflowTemplatesProps {
  onClose: () => void;
  onSelectTemplate: (template: Omit<AutomationWorkflow, 'id' | 'createdBy' | 'createdAt' | 'updatedAt' | 'executionCount' | 'lastExecutedAt'>) => void;
}

export default function WorkflowTemplates({ onClose, onSelectTemplate }: WorkflowTemplatesProps) {
  const { workflows, createWorkflow } = useAutomationStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'predefined' | 'user'>('all');

  const userTemplates = workflows.filter(w => w.isTemplate);
  
  const filteredTemplates = [
    ...(selectedCategory === 'user' ? [] : WORKFLOW_TEMPLATES),
    ...(selectedCategory === 'predefined' ? [] : userTemplates)
  ].filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUseTemplate = (template: any) => {
    onSelectTemplate({
      ...template,
      isTemplate: false,
      isActive: false
    });
    onClose();
  };

  const handleSaveAsTemplate = async (workflow: AutomationWorkflow) => {
    const templateName = prompt('Enter template name:', `${workflow.name} Template`);
    if (templateName) {
      await createWorkflow({
        ...workflow,
        name: templateName,
        isTemplate: true,
        isActive: false,
        executionCount: 0
      });
    }
  };

  const exportTemplate = (template: any) => {
    const dataStr = JSON.stringify(template, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${template.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_template.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const importTemplate = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const template = JSON.parse(e.target?.result as string);
            onSelectTemplate({
              ...template,
              isTemplate: false,
              isActive: false
            });
            onClose();
          } catch (error) {
            alert('Invalid template file');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const renderTemplate = (template: any, isUserTemplate = false) => {
    return (
      <div key={template.name} className="bg-gray-700 border border-gray-600 rounded-lg p-4 hover:border-gray-500 transition-colors">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-white font-medium mb-1">{template.name}</h3>
            <p className="text-gray-400 text-sm">{template.description}</p>
          </div>
          <div className="flex items-center gap-2">
            {!isUserTemplate && (
              <Star className="w-4 h-4 text-yellow-400" title="Predefined template" />
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <span>Trigger: {template.triggerConfig.type.replace('_', ' ')}</span>
          <span>Actions: {template.actionConfig.length}</span>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => handleUseTemplate(template)}
            className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Use Template
          </button>
          <button
            onClick={() => exportTemplate(template)}
            className="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500"
            title="Export template"
          >
            <Download className="w-4 h-4" />
          </button>
          {isUserTemplate && (
            <button
              onClick={() => exportTemplate(template)}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500"
              title="Share template"
            >
              <Copy className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Workflow Templates</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={importTemplate}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              Import
            </button>
            <button onClick={onClose} className="text-gray-400 hover:text-white">
              ✕
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="flex gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as any)}
              className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="all">All Templates</option>
              <option value="predefined">Predefined</option>
              <option value="user">My Templates</option>
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto">
            {filteredTemplates.length === 0 ? (
              <div className="col-span-2 text-center py-8 text-gray-400">
                <Filter className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No templates found</p>
              </div>
            ) : (
              filteredTemplates.map((template, index) => 
                renderTemplate(template, index >= WORKFLOW_TEMPLATES.length)
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
