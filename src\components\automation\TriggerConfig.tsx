import React, { useState } from 'react';
import { X, Zap, Calendar, Clock, Webhook } from 'lucide-react';
import { TriggerConfig as TriggerConfigType, TriggerType } from '../../types/automation';

interface TriggerConfigProps {
  config?: TriggerConfigType;
  onSave: (config: TriggerConfigType) => void;
  onClose: () => void;
}

const TRIGGER_TYPES: Array<{ value: TriggerType; label: string; description: string; icon: React.ReactNode }> = [
  {
    value: 'task_created',
    label: 'Task Created',
    description: 'When a new task is created',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'task_updated',
    label: 'Task Updated',
    description: 'When any task field is modified',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'task_status_changed',
    label: 'Task Status Changed',
    description: 'When a task status is changed',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'task_assigned',
    label: 'Task Assigned',
    description: 'When a task is assigned to users or groups',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'task_due_date_reached',
    label: 'Due Date Reached',
    description: 'When a task due date is reached',
    icon: <Calendar className="w-4 h-4" />
  },
  {
    value: 'comment_added',
    label: 'Comment Added',
    description: 'When a comment is added to a task',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'project_created',
    label: 'Project Created',
    description: 'When a new project is created',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'project_updated',
    label: 'Project Updated',
    description: 'When a project is modified',
    icon: <Zap className="w-4 h-4" />
  },
  {
    value: 'schedule',
    label: 'Schedule',
    description: 'Run on a schedule (daily, weekly, monthly)',
    icon: <Clock className="w-4 h-4" />
  },
  {
    value: 'webhook',
    label: 'Webhook',
    description: 'Triggered by external webhook call',
    icon: <Webhook className="w-4 h-4" />
  },
  {
    value: 'custom_event',
    label: 'Custom Event',
    description: 'Custom application event',
    icon: <Zap className="w-4 h-4" />
  }
];

export default function TriggerConfig({ config, onSave, onClose }: TriggerConfigProps) {
  const [triggerConfig, setTriggerConfig] = useState<TriggerConfigType>(
    config || { type: 'task_created' }
  );

  const handleSave = () => {
    onSave(triggerConfig);
  };

  const renderScheduleConfig = () => {
    if (triggerConfig.type !== 'schedule') return null;

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Frequency
          </label>
          <select
            value={triggerConfig.scheduleConfig?.frequency || 'daily'}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              scheduleConfig: {
                ...prev.scheduleConfig,
                frequency: e.target.value as 'once' | 'daily' | 'weekly' | 'monthly'
              }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
          >
            <option value="once">Once</option>
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Time
          </label>
          <input
            type="time"
            value={triggerConfig.scheduleConfig?.time || '09:00'}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              scheduleConfig: {
                ...prev.scheduleConfig,
                time: e.target.value
              }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
          />
        </div>

        {triggerConfig.scheduleConfig?.frequency === 'weekly' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Day of Week
            </label>
            <select
              value={triggerConfig.scheduleConfig?.dayOfWeek || 1}
              onChange={(e) => setTriggerConfig(prev => ({
                ...prev,
                scheduleConfig: {
                  ...prev.scheduleConfig,
                  dayOfWeek: parseInt(e.target.value)
                }
              }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              <option value={1}>Monday</option>
              <option value={2}>Tuesday</option>
              <option value={3}>Wednesday</option>
              <option value={4}>Thursday</option>
              <option value={5}>Friday</option>
              <option value={6}>Saturday</option>
              <option value={0}>Sunday</option>
            </select>
          </div>
        )}

        {triggerConfig.scheduleConfig?.frequency === 'monthly' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Day of Month
            </label>
            <input
              type="number"
              min="1"
              max="31"
              value={triggerConfig.scheduleConfig?.dayOfMonth || 1}
              onChange={(e) => setTriggerConfig(prev => ({
                ...prev,
                scheduleConfig: {
                  ...prev.scheduleConfig,
                  dayOfMonth: parseInt(e.target.value)
                }
              }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            />
          </div>
        )}
      </div>
    );
  };

  const renderWebhookConfig = () => {
    if (triggerConfig.type !== 'webhook') return null;

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Webhook URL
          </label>
          <input
            type="url"
            value={triggerConfig.webhookConfig?.url || ''}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              webhookConfig: {
                ...prev.webhookConfig,
                url: e.target.value
              }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            placeholder="https://example.com/webhook"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Secret (Optional)
          </label>
          <input
            type="password"
            value={triggerConfig.webhookConfig?.secret || ''}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              webhookConfig: {
                ...prev.webhookConfig,
                secret: e.target.value
              }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            placeholder="Webhook secret for verification"
          />
        </div>
      </div>
    );
  };

  const renderCustomEventConfig = () => {
    if (triggerConfig.type !== 'custom_event') return null;

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Event Name
          </label>
          <input
            type="text"
            value={triggerConfig.customEventConfig?.eventName || ''}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              customEventConfig: {
                ...prev.customEventConfig,
                eventName: e.target.value
              }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            placeholder="my_custom_event"
          />
        </div>
      </div>
    );
  };

  const renderEntityFilters = () => {
    const entityTypes = ['task', 'project', 'comment', 'user'];
    
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Entity Type (Optional)
          </label>
          <select
            value={triggerConfig.entityType || ''}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              entityType: e.target.value as 'task' | 'project' | 'comment' | 'user' | undefined
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
          >
            <option value="">Any entity</option>
            {entityTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Specific Entity ID (Optional)
          </label>
          <input
            type="text"
            value={triggerConfig.entityId || ''}
            onChange={(e) => setTriggerConfig(prev => ({
              ...prev,
              entityId: e.target.value || undefined
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            placeholder="Leave empty for any entity"
          />
        </div>

        {triggerConfig.type === 'task_updated' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Specific Field (Optional)
            </label>
            <input
              type="text"
              value={triggerConfig.fieldName || ''}
              onChange={(e) => setTriggerConfig(prev => ({
                ...prev,
                fieldName: e.target.value || undefined
              }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              placeholder="e.g., status, priority, assignedUsers"
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Zap className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Configure Trigger</h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Trigger Type
            </label>
            <div className="grid grid-cols-1 gap-3">
              {TRIGGER_TYPES.map((trigger) => (
                <div
                  key={trigger.value}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    triggerConfig.type === trigger.value
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                  onClick={() => setTriggerConfig(prev => ({ ...prev, type: trigger.value }))}
                >
                  <div className="flex items-center gap-3">
                    <div className="text-blue-400">{trigger.icon}</div>
                    <div>
                      <div className="text-white font-medium">{trigger.label}</div>
                      <div className="text-gray-400 text-sm">{trigger.description}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Specific configuration based on trigger type */}
          {renderScheduleConfig()}
          {renderWebhookConfig()}
          {renderCustomEventConfig()}
          
          {/* Entity filters for applicable triggers */}
          {!['schedule', 'webhook', 'custom_event'].includes(triggerConfig.type) && (
            <div>
              <h3 className="text-lg font-medium text-white mb-4">Filters</h3>
              {renderEntityFilters()}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Save Trigger
          </button>
        </div>
      </div>
    </div>
  );
}
