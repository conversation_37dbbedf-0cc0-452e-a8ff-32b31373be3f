import React from 'react';
import { AlertTriangle, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { getCapacityStatus } from '../utils/capacityCalculations';

interface CapacityIndicatorProps {
  utilizationPercentage: number;
  capacity: number;
  demand: number;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
  className?: string;
}

export default function CapacityIndicator({ 
  utilizationPercentage, 
  capacity, 
  demand, 
  size = 'md',
  showDetails = false,
  className = ''
}: CapacityIndicatorProps) {
  const status = getCapacityStatus(utilizationPercentage);
  
  const sizeClasses = {
    sm: 'w-4 h-4 text-xs',
    md: 'w-6 h-6 text-sm',
    lg: 'w-8 h-8 text-base'
  };
  
  const getIcon = () => {
    switch (status.status) {
      case 'under':
        return <CheckCircle className={sizeClasses[size]} />;
      case 'optimal':
        return <Clock className={sizeClasses[size]} />;
      case 'over':
        return <AlertCircle className={sizeClasses[size]} />;
      case 'critical':
        return <AlertTriangle className={sizeClasses[size]} />;
      default:
        return <Clock className={sizeClasses[size]} />;
    }
  };
  
  const getTextColor = () => {
    switch (status.status) {
      case 'under':
        return 'text-green-700';
      case 'optimal':
        return 'text-blue-700';
      case 'over':
        return 'text-yellow-700';
      case 'critical':
        return 'text-red-700';
      default:
        return 'text-gray-700';
    }
  };
  
  const getBgColor = () => {
    switch (status.status) {
      case 'under':
        return 'bg-green-100';
      case 'optimal':
        return 'bg-blue-100';
      case 'over':
        return 'bg-yellow-100';
      case 'critical':
        return 'bg-red-100';
      default:
        return 'bg-gray-100';
    }
  };
  
  if (showDetails) {
    return (
      <div className={`${getBgColor()} ${getTextColor()} p-3 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          {getIcon()}
          <span className="font-medium">{status.label}</span>
        </div>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>Capacity:</span>
            <span className="font-medium">{capacity.toFixed(1)}h</span>
          </div>
          <div className="flex justify-between">
            <span>Demand:</span>
            <span className="font-medium">{demand.toFixed(1)}h</span>
          </div>
          <div className="flex justify-between">
            <span>Utilization:</span>
            <span className="font-bold">{utilizationPercentage.toFixed(0)}%</span>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                status.status === 'under' ? 'bg-green-500' :
                status.status === 'optimal' ? 'bg-blue-500' :
                status.status === 'over' ? 'bg-yellow-500' :
                'bg-red-500'
              }`}
              style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
            />
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div 
      className={`inline-flex items-center gap-1 px-2 py-1 rounded-full ${getBgColor()} ${getTextColor()} ${className}`}
      title={`${status.label}: ${utilizationPercentage.toFixed(0)}% (${demand.toFixed(1)}h / ${capacity.toFixed(1)}h)`}
    >
      {getIcon()}
      <span className="font-medium text-xs">
        {utilizationPercentage.toFixed(0)}%
      </span>
    </div>
  );
}

export function CapacityProgressBar({ 
  utilizationPercentage, 
  capacity, 
  demand,
  height = 'h-2',
  showLabels = false,
  className = ''
}: {
  utilizationPercentage: number;
  capacity: number;
  demand: number;
  height?: string;
  showLabels?: boolean;
  className?: string;
}) {
  const status = getCapacityStatus(utilizationPercentage);
  
  return (
    <div className={className}>
      {showLabels && (
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>{demand.toFixed(1)}h demand</span>
          <span>{capacity.toFixed(1)}h capacity</span>
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${height}`}>
        <div 
          className={`${height} rounded-full transition-all duration-300 ${
            status.status === 'under' ? 'bg-green-500' :
            status.status === 'optimal' ? 'bg-blue-500' :
            status.status === 'over' ? 'bg-yellow-500' :
            'bg-red-500'
          }`}
          style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
        />
        {utilizationPercentage > 100 && (
          <div 
            className="bg-red-600 h-1 rounded-full -mt-1"
            style={{ width: `${Math.min(utilizationPercentage - 100, 50)}%` }}
          />
        )}
      </div>
      {showLabels && (
        <div className="text-center text-xs text-gray-600 mt-1">
          {utilizationPercentage.toFixed(0)}% utilization
        </div>
      )}
    </div>
  );
}
