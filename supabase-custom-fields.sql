-- Custom Fields Schema for Project Management Tool
-- This script adds custom fields functionality to the existing database
--
-- INSTRUCTIONS FOR MA<PERSON>AL APPLICATION:
-- 1. Open Supabase Dashboard > SQL Editor
-- 2. Copy and paste this entire script
-- 3. Run the script to create all necessary tables and functions
-- 4. Verify the tables were created successfully
--
-- TROUBLESHOOTING:
-- If you encounter "case not found" errors when saving tasks, run these commands:
-- DROP TRIGGER IF EXISTS validate_task_custom_fields ON tasks;
-- DROP FUNCTION IF EXISTS validate_custom_field_values();
--
-- The validation triggers are optional and can be removed for stability.

-- Create custom field types enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'custom_field_type') THEN
        CREATE TYPE custom_field_type AS ENUM ('text', 'number', 'date', 'dropdown');
    END IF;
END $$;

-- Create custom_fields table for field definitions
CREATE TABLE IF NOT EXISTS custom_fields (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  label TEXT NOT NULL,
  field_type custom_field_type NOT NULL,
  is_required BOOLEAN DEFAULT FALSE,
  dropdown_options JSONB DEFAULT NULL, -- For dropdown type: ["option1", "option2", ...]
  default_value TEXT DEFAULT NULL,
  description TEXT DEFAULT NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Constraints
  CONSTRAINT valid_dropdown_options CHECK (
    field_type != 'dropdown' OR (dropdown_options IS NOT NULL AND jsonb_array_length(dropdown_options) > 0)
  ),
  CONSTRAINT valid_field_name CHECK (
    name ~ '^[a-zA-Z][a-zA-Z0-9_]*$' AND length(name) <= 50
  )
);

-- Add custom_field_values column to tasks table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tasks' 
        AND column_name = 'custom_field_values'
    ) THEN
        ALTER TABLE tasks ADD COLUMN custom_field_values JSONB DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_custom_fields_active ON custom_fields (is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_custom_fields_name ON custom_fields (name);
CREATE INDEX IF NOT EXISTS idx_tasks_custom_field_values ON tasks USING GIN (custom_field_values);

-- Create function to validate custom field values
CREATE OR REPLACE FUNCTION validate_custom_field_values()
RETURNS TRIGGER AS $$
DECLARE
    field_record RECORD;
    field_value TEXT;
    numeric_value NUMERIC;
BEGIN
    -- Skip validation if custom_field_values is null or empty
    IF NEW.custom_field_values IS NULL OR NEW.custom_field_values = '{}'::jsonb THEN
        RETURN NEW;
    END IF;

    -- Validate each custom field value
    FOR field_record IN 
        SELECT id, name, field_type, is_required, dropdown_options 
        FROM custom_fields 
        WHERE is_active = TRUE
    LOOP
        field_value := NEW.custom_field_values ->> field_record.name;
        
        -- Check required fields
        IF field_record.is_required AND (field_value IS NULL OR field_value = '') THEN
            RAISE EXCEPTION 'Custom field % is required', field_record.name;
        END IF;
        
        -- Skip validation if field is empty and not required
        IF field_value IS NULL OR field_value = '' THEN
            CONTINUE;
        END IF;
        
        -- Type-specific validation
        CASE field_record.field_type
            WHEN 'number' THEN
                BEGIN
                    numeric_value := field_value::NUMERIC;
                EXCEPTION WHEN OTHERS THEN
                    RAISE EXCEPTION 'Custom field % must be a valid number', field_record.name;
                END;
                
            WHEN 'date' THEN
                BEGIN
                    PERFORM field_value::DATE;
                EXCEPTION WHEN OTHERS THEN
                    RAISE EXCEPTION 'Custom field % must be a valid date (YYYY-MM-DD)', field_record.name;
                END;
                
            WHEN 'dropdown' THEN
                IF NOT (field_record.dropdown_options ? field_value) THEN
                    RAISE EXCEPTION 'Custom field % value must be one of the predefined options', field_record.name;
                END IF;
        END CASE;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for custom field validation on tasks
DROP TRIGGER IF EXISTS validate_task_custom_fields ON tasks;
CREATE TRIGGER validate_task_custom_fields
    BEFORE INSERT OR UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION validate_custom_field_values();

-- Insert some default custom fields for demonstration
INSERT INTO custom_fields (name, label, field_type, description, sort_order, created_by)
SELECT 
    'priority_score',
    'Priority Score',
    'number',
    'Numeric priority score (1-10)',
    1,
    (SELECT id FROM auth.users LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM custom_fields WHERE name = 'priority_score');

INSERT INTO custom_fields (name, label, field_type, dropdown_options, description, sort_order, created_by)
SELECT 
    'complexity',
    'Complexity Level',
    'dropdown',
    '["Simple", "Medium", "Complex", "Very Complex"]'::jsonb,
    'Task complexity assessment',
    2,
    (SELECT id FROM auth.users LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM custom_fields WHERE name = 'complexity');

-- Enable RLS on custom_fields table
ALTER TABLE custom_fields ENABLE ROW LEVEL SECURITY;

-- RLS Policies for custom_fields
-- Allow all authenticated users to read custom fields
CREATE POLICY "Allow read access to custom fields" ON custom_fields
    FOR SELECT TO authenticated
    USING (true);

-- Allow only admin users to manage custom fields
CREATE POLICY "Allow admin users to manage custom fields" ON custom_fields
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.id = auth.uid() 
            AND user_profiles.role = 'admin'
        )
    );

-- Update existing tasks to have empty custom field values if null
UPDATE tasks SET custom_field_values = '{}'::jsonb WHERE custom_field_values IS NULL;

-- Add helpful comments
COMMENT ON TABLE custom_fields IS 'Stores custom field definitions that can be applied to tasks and subtasks';
COMMENT ON COLUMN custom_fields.name IS 'Unique identifier for the custom field (used in code)';
COMMENT ON COLUMN custom_fields.label IS 'Human-readable label displayed in forms';
COMMENT ON COLUMN custom_fields.dropdown_options IS 'JSON array of options for dropdown fields';
COMMENT ON COLUMN tasks.custom_field_values IS 'JSON object storing custom field values for this task';

SELECT 'Custom fields schema created successfully!' as message;
