import React from 'react';
import { CustomField } from '../types/customFields';
import { AlertCircle } from 'lucide-react';

interface CustomFieldInputProps {
  field: CustomField;
  value: string | number | null;
  onChange: (value: string | number | null) => void;
  error?: string;
  disabled?: boolean;
}

export default function CustomFieldInput({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false 
}: CustomFieldInputProps) {
  const handleChange = (newValue: string) => {
    if (field.fieldType === 'number') {
      // Handle number conversion
      if (newValue === '') {
        onChange(null);
      } else {
        const numValue = parseFloat(newValue);
        onChange(isNaN(numValue) ? null : numValue);
      }
    } else {
      // Handle string values
      onChange(newValue === '' ? null : newValue);
    }
  };

  const getDisplayValue = (): string => {
    if (value === null || value === undefined) return '';
    return String(value);
  };

  const renderInput = () => {
    const baseClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
      error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
    } ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`;

    switch (field.fieldType) {
      case 'text':
        return (
          <input
            type="text"
            value={getDisplayValue()}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={baseClasses}
            placeholder={field.defaultValue ? `Default: ${field.defaultValue}` : `Enter ${field.label.toLowerCase()}`}
          />
        );

      case 'number':
        return (
          <input
            type="number"
            value={getDisplayValue()}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={baseClasses}
            placeholder={field.defaultValue ? `Default: ${field.defaultValue}` : `Enter ${field.label.toLowerCase()}`}
            step="any"
          />
        );

      case 'date':
        return (
          <input
            type="date"
            value={getDisplayValue()}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={baseClasses}
          />
        );

      case 'dropdown':
        return (
          <select
            value={getDisplayValue()}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={baseClasses}
          >
            <option value="">
              {field.isRequired ? 'Select an option' : 'No selection'}
            </option>
            {field.dropdownOptions?.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );

      default:
        return (
          <div className="text-red-500 text-sm">
            Unsupported field type: {field.fieldType}
          </div>
        );
    }
  };

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">
        {field.label}
        {field.isRequired && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {field.description && (
        <p className="text-xs text-gray-500 mb-1">{field.description}</p>
      )}
      
      {renderInput()}
      
      {error && (
        <p className="text-red-500 text-xs flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          {error}
        </p>
      )}
    </div>
  );
}
