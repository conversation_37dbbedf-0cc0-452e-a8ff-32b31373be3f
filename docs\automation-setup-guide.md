# Automation System Setup Guide

## Database Migration Required

The automation system requires new database tables to be created in your Supabase database. Follow these steps to set up the automation system:

## Option 1: Using Supabase Dashboard (Recommended)

1. **Open Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Sign in to your account
   - Select your project

2. **Navigate to SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Run the Migration**
   - Copy the entire content from `migrations/add-automation-system.sql`
   - Paste it into the SQL editor
   - Click "Run" to execute the migration

4. **Verify Tables Created**
   - Go to "Table Editor" in the left sidebar
   - You should see the new tables:
     - `automation_workflows`
     - `automation_executions`
     - `automation_triggers`

## Option 2: Using Supabase CLI

If you have Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Run the migration
supabase db push

# Or apply the specific migration file
psql -h your-db-host -U postgres -d postgres -f migrations/add-automation-system.sql
```

## Option 3: Manual SQL Execution

If you prefer to run the SQL manually, here's the essential schema:

```sql
-- Create automation_workflows table
CREATE TABLE automation_workflows (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  is_template BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  trigger_config JSONB NOT NULL,
  condition_config JSONB,
  action_config JSONB NOT NULL,
  execution_count INTEGER DEFAULT 0,
  last_executed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create automation_executions table
CREATE TABLE automation_executions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workflow_id UUID REFERENCES automation_workflows(id) ON DELETE CASCADE NOT NULL,
  trigger_data JSONB NOT NULL,
  condition_result JSONB,
  action_results JSONB,
  status TEXT NOT NULL DEFAULT 'pending',
  error_message TEXT,
  execution_time_ms INTEGER,
  executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create automation_triggers table
CREATE TABLE automation_triggers (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workflow_id UUID REFERENCES automation_workflows(id) ON DELETE CASCADE NOT NULL,
  trigger_type TEXT NOT NULL,
  trigger_config JSONB NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE automation_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_triggers ENABLE ROW LEVEL SECURITY;

-- Add basic policies (see full migration file for complete policies)
CREATE POLICY "Users can create workflows" ON automation_workflows FOR INSERT WITH CHECK (
  created_by = auth.uid()
);

CREATE POLICY "Users can view own workflows" ON automation_workflows FOR SELECT USING (
  created_by = auth.uid()
);

CREATE POLICY "Users can update own workflows" ON automation_workflows FOR UPDATE USING (
  created_by = auth.uid()
);

CREATE POLICY "Users can delete own workflows" ON automation_workflows FOR DELETE USING (
  created_by = auth.uid()
);
```

## Verification Steps

After running the migration:

1. **Check Tables Exist**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name LIKE 'automation_%';
   ```

2. **Test Basic Functionality**
   - Open your application
   - Navigate to the Automation section (⚡ icon)
   - Try creating a simple workflow
   - The error "relation does not exist" should be gone

3. **Verify Permissions**
   - Make sure you can create, view, and edit workflows
   - Check that other users can only see their own workflows

## Troubleshooting

### Error: "relation does not exist"
- The migration hasn't been run yet
- Run the migration SQL in Supabase dashboard

### Error: "permission denied"
- RLS policies might not be set up correctly
- Check that you're logged in as a valid user
- Verify the policies were created correctly

### Error: "column does not exist"
- The migration might have been partially applied
- Check which tables exist and compare with the expected schema
- Re-run the migration (it uses IF NOT EXISTS so it's safe)

### Error: "foreign key constraint"
- Make sure your existing tables (users, projects, folders) exist
- The automation tables reference these existing tables

## Next Steps

Once the migration is complete:

1. **Test the Automation System**
   - Create a simple workflow
   - Test trigger configuration
   - Try different action types

2. **Explore Templates**
   - Use the "From Template" button
   - Customize templates for your needs

3. **Monitor Executions**
   - Check the execution logs
   - Monitor workflow performance

4. **Set Up Common Workflows**
   - Auto-assign high priority tasks
   - Status change notifications
   - Overdue task reminders

## Support

If you encounter any issues:

1. Check the browser console for detailed error messages
2. Verify the database migration was successful
3. Ensure you're logged in with proper permissions
4. Check the Supabase logs for any database errors

The automation system should now be fully functional! 🚀
