// Task Dependencies Type Definitions
// Comprehensive type system for task dependency management and Gantt chart functionality

export type DependencyType = 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish';

export interface TaskDependency {
  id: string;
  predecessorTaskId: string;
  successorTaskId: string;
  dependencyType: DependencyType;
  lagDays: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface TaskDependencyInput {
  predecessorTaskId: string;
  successorTaskId: string;
  dependencyType?: DependencyType;
  lagDays?: number;
}

export interface TaskDependencyUpdate {
  dependencyType?: DependencyType;
  lagDays?: number;
}

// Extended task interface with dependency information
export interface TaskWithDependencies {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: 'low' | 'medium' | 'high';
  assignedUserId?: string;
  dueDate?: string;
  startDate?: string;
  tags: string[];
  projectId?: string;
  assignedGroups?: string[];
  assignedUsers?: string[];
  ownerId?: string;
  folderId?: string;
  
  // Dependency-related fields
  dependencies: TaskDependency[];
  dependents: TaskDependency[];
  calculatedStartDate?: string;
  calculatedDueDate?: string;
  isOnCriticalPath?: boolean;
  totalFloat?: number; // Days of slack time
  
  // Existing fields
  comments: any[];
  history: any[];
  durations: any[];
  subtasks: any[];
  effort?: any;
}

// Dependency validation result
export interface DependencyValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  wouldCreateCycle?: boolean;
  affectedTasks?: string[];
}

// Date calculation result
export interface DateCalculationResult {
  taskId: string;
  originalStartDate?: string;
  originalDueDate?: string;
  calculatedStartDate?: string;
  calculatedDueDate?: string;
  hasConflict: boolean;
  conflictReason?: string;
  dependencyChain: string[];
}

// Critical path analysis
export interface CriticalPathNode {
  taskId: string;
  earliestStart: Date;
  earliestFinish: Date;
  latestStart: Date;
  latestFinish: Date;
  totalFloat: number;
  isOnCriticalPath: boolean;
}

export interface CriticalPathAnalysis {
  criticalPath: string[];
  projectDuration: number;
  nodes: CriticalPathNode[];
  longestPath: string[];
}

// Gantt chart data structures
export interface GanttTask {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  progress: number;
  dependencies: string[];
  isOnCriticalPath: boolean;
  totalFloat: number;
  color?: string;
}

export interface GanttDependencyLine {
  id: string;
  fromTaskId: string;
  toTaskId: string;
  dependencyType: DependencyType;
  lagDays: number;
  color?: string;
  style?: 'solid' | 'dashed' | 'dotted';
}

// Dependency management operations
export interface DependencyOperation {
  type: 'add' | 'remove' | 'update';
  dependency: TaskDependency | TaskDependencyInput;
  reason?: string;
}

export interface DependencyBatchOperation {
  operations: DependencyOperation[];
  validateOnly?: boolean;
  skipDateRecalculation?: boolean;
}

// Date propagation configuration
export interface DatePropagationConfig {
  respectManualDates: boolean;
  maxPropagationDepth: number;
  workingDaysOnly: boolean;
  workingDays: number[]; // 0-6 (Sunday-Saturday)
  holidays: string[]; // ISO date strings
}

// Dependency service response types
export interface DependencyServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  warnings?: string[];
  affectedTasks?: string[];
}

// Circular dependency detection
export interface CircularDependencyCheck {
  hasCycle: boolean;
  cyclePath?: string[];
  cycleLength?: number;
}

// Task scheduling constraints
export interface TaskConstraint {
  taskId: string;
  constraintType: 'must_start_on' | 'must_finish_on' | 'start_no_earlier_than' | 'start_no_later_than' | 'finish_no_earlier_than' | 'finish_no_later_than';
  constraintDate: string;
  priority: 'low' | 'medium' | 'high';
}

// Export all dependency-related types
export type {
  TaskDependency,
  TaskDependencyInput,
  TaskDependencyUpdate,
  TaskWithDependencies,
  DependencyValidationResult,
  DateCalculationResult,
  CriticalPathNode,
  CriticalPathAnalysis,
  GanttTask,
  GanttDependencyLine,
  DependencyOperation,
  DependencyBatchOperation,
  DatePropagationConfig,
  DependencyServiceResponse,
  CircularDependencyCheck,
  TaskConstraint
};
