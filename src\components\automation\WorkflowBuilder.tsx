import React, { useState, useRef, useCallback, useEffect } from 'react';
import { X, Plus, Play, Save, Copy, Setting<PERSON>, Zap, Filter, Target } from 'lucide-react';
import { AutomationWorkflow, TriggerConfig, ConditionConfig, ActionConfig, WorkflowNode, WorkflowCanvas } from '../../types/automation';
import { useAutomationStore } from '../../store/useAutomationStore';
import { getCurrentUser } from '../../lib/supabase';
import TriggerConfigModal from './TriggerConfig';
import ConditionBuilder from './ConditionBuilder';
import ActionConfigModal from './ActionConfig';

interface WorkflowBuilderProps {
  workflow?: AutomationWorkflow;
  onClose: () => void;
}

export default function WorkflowBuilder({ workflow, onClose }: WorkflowBuilderProps) {
  const { createWorkflow, updateWorkflow, testWorkflow, loading } = useAutomationStore();

  const [workflowData, setWorkflowData] = useState<Partial<AutomationWorkflow>>({
    name: workflow?.name || '',
    description: workflow?.description || '',
    isActive: workflow?.isActive ?? true,
    triggerConfig: workflow?.triggerConfig || { type: 'task_created' },
    conditionConfig: workflow?.conditionConfig,
    actionConfig: workflow?.actionConfig || [],
  });

  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to load current user:', error);
      }
    };
    loadCurrentUser();
  }, []);
  
  const [canvas, setCanvas] = useState<WorkflowCanvas>({
    nodes: [],
    connections: []
  });
  
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showTriggerConfig, setShowTriggerConfig] = useState(false);
  const [showConditionBuilder, setShowConditionBuilder] = useState(false);
  const [showActionConfig, setShowActionConfig] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleSave = async () => {
    try {
      if (!workflowData.name || !workflowData.triggerConfig || !workflowData.actionConfig?.length) {
        alert('Please provide a name, trigger, and at least one action');
        return;
      }

      if (!currentUser) {
        alert('Please log in to save workflows');
        return;
      }

      const workflowToSave = {
        ...workflowData,
        createdBy: currentUser.id,
        executionCount: 0,
      } as Omit<AutomationWorkflow, 'id' | 'createdAt' | 'updatedAt' | 'lastExecutedAt'>;

      if (workflow?.id) {
        await updateWorkflow(workflow.id, workflowToSave);
      } else {
        await createWorkflow(workflowToSave);
      }

      onClose();
    } catch (error) {
      console.error('Failed to save workflow:', error);
      alert('Failed to save workflow: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const handleTest = async () => {
    if (!workflowData.triggerConfig || !workflowData.actionConfig?.length) {
      alert('Please configure trigger and actions before testing');
      return;
    }

    try {
      const testData = {
        taskId: 'test-task-id',
        projectId: 'test-project-id',
        userId: 'test-user-id',
      };

      const mockWorkflow = {
        ...workflowData,
        id: 'test-workflow',
        createdBy: currentUser?.id || 'test-user',
        executionCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as AutomationWorkflow;

      const result = await testWorkflow(mockWorkflow, testData);
      alert(`Test completed with status: ${result.status}`);
    } catch (error) {
      console.error('Test failed:', error);
      alert('Test failed');
    }
  };

  const handleDragStart = (e: React.DragEvent, itemType: string) => {
    setDraggedItem(itemType);
    e.dataTransfer.effectAllowed = 'copy';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedItem || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const newNode: WorkflowNode = {
      id: crypto.randomUUID(),
      type: draggedItem as 'trigger' | 'condition' | 'action',
      position: { x, y },
      data: {},
      connections: []
    };

    setCanvas(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode]
    }));

    setDraggedItem(null);
  };

  const handleNodeClick = (node: WorkflowNode) => {
    setSelectedNode(node);
    
    switch (node.type) {
      case 'trigger':
        setShowTriggerConfig(true);
        break;
      case 'condition':
        setShowConditionBuilder(true);
        break;
      case 'action':
        setShowActionConfig(true);
        break;
    }
  };

  const renderToolbox = () => (
    <div className="w-64 bg-gray-800 border-r border-gray-700 p-4">
      <h3 className="text-white font-semibold mb-4">Workflow Components</h3>
      
      <div className="space-y-3">
        <div
          draggable
          onDragStart={(e) => handleDragStart(e, 'trigger')}
          className="flex items-center gap-3 p-3 bg-blue-600 rounded-lg cursor-move hover:bg-blue-700 transition-colors"
        >
          <Zap className="w-5 h-5 text-white" />
          <span className="text-white font-medium">Trigger</span>
        </div>
        
        <div
          draggable
          onDragStart={(e) => handleDragStart(e, 'condition')}
          className="flex items-center gap-3 p-3 bg-yellow-600 rounded-lg cursor-move hover:bg-yellow-700 transition-colors"
        >
          <Filter className="w-5 h-5 text-white" />
          <span className="text-white font-medium">Condition</span>
        </div>
        
        <div
          draggable
          onDragStart={(e) => handleDragStart(e, 'action')}
          className="flex items-center gap-3 p-3 bg-green-600 rounded-lg cursor-move hover:bg-green-700 transition-colors"
        >
          <Target className="w-5 h-5 text-white" />
          <span className="text-white font-medium">Action</span>
        </div>
      </div>
      
      <div className="mt-8">
        <h4 className="text-white font-medium mb-3">Quick Setup</h4>
        <div className="space-y-2">
          <button
            onClick={() => setShowTriggerConfig(true)}
            className="w-full text-left p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded"
          >
            Configure Trigger
          </button>
          <button
            onClick={() => setShowConditionBuilder(true)}
            className="w-full text-left p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded"
          >
            Add Conditions
          </button>
          <button
            onClick={() => setShowActionConfig(true)}
            className="w-full text-left p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded"
          >
            Configure Actions
          </button>
        </div>
      </div>
    </div>
  );

  const renderCanvas = () => (
    <div className="flex-1 bg-gray-900 relative overflow-hidden">
      <div
        ref={canvasRef}
        className="w-full h-full"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* Grid background */}
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>
        
        {/* Canvas nodes */}
        {canvas.nodes.map((node) => (
          <div
            key={node.id}
            className={`absolute p-4 rounded-lg cursor-pointer border-2 ${
              node.type === 'trigger' ? 'bg-blue-600 border-blue-500' :
              node.type === 'condition' ? 'bg-yellow-600 border-yellow-500' :
              'bg-green-600 border-green-500'
            } ${selectedNode?.id === node.id ? 'ring-2 ring-white' : ''}`}
            style={{ left: node.position.x, top: node.position.y }}
            onClick={() => handleNodeClick(node)}
          >
            <div className="flex items-center gap-2 text-white">
              {node.type === 'trigger' && <Zap className="w-4 h-4" />}
              {node.type === 'condition' && <Filter className="w-4 h-4" />}
              {node.type === 'action' && <Target className="w-4 h-4" />}
              <span className="font-medium capitalize">{node.type}</span>
            </div>
          </div>
        ))}
        
        {/* Empty state */}
        {canvas.nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">🤖</div>
              <h3 className="text-xl font-semibold mb-2">Build Your Automation</h3>
              <p className="text-gray-500">Drag components from the toolbox to get started</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderProperties = () => (
    <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
      <h3 className="text-white font-semibold mb-4">Workflow Properties</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Workflow Name
          </label>
          <input
            type="text"
            value={workflowData.name}
            onChange={(e) => setWorkflowData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            placeholder="Enter workflow name"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Description
          </label>
          <textarea
            value={workflowData.description}
            onChange={(e) => setWorkflowData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            rows={3}
            placeholder="Describe what this workflow does"
          />
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={workflowData.isActive}
            onChange={(e) => setWorkflowData(prev => ({ ...prev, isActive: e.target.checked }))}
            className="mr-2"
          />
          <label htmlFor="isActive" className="text-sm text-gray-300">
            Active
          </label>
        </div>
        
        <div className="pt-4 border-t border-gray-700">
          <h4 className="text-white font-medium mb-3">Current Configuration</h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Trigger:</span>
              <span className="text-white">{workflowData.triggerConfig?.type || 'Not set'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Conditions:</span>
              <span className="text-white">{workflowData.conditionConfig ? 'Yes' : 'None'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Actions:</span>
              <span className="text-white">{workflowData.actionConfig?.length || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">
              {workflow ? 'Edit Workflow' : 'Create Workflow'}
            </h2>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleTest}
              disabled={loading.executing}
              className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Test
            </button>
            <button
              onClick={handleSave}
              disabled={loading.creating || loading.updating}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex-1 flex overflow-hidden">
          {renderToolbox()}
          {renderCanvas()}
          {renderProperties()}
        </div>
      </div>
      
      {/* Configuration modals */}
      {showTriggerConfig && (
        <TriggerConfigModal
          config={workflowData.triggerConfig}
          onSave={(config) => {
            setWorkflowData(prev => ({ ...prev, triggerConfig: config }));
            setShowTriggerConfig(false);
          }}
          onClose={() => setShowTriggerConfig(false)}
        />
      )}

      {showConditionBuilder && (
        <ConditionBuilder
          config={workflowData.conditionConfig}
          onSave={(config) => {
            setWorkflowData(prev => ({ ...prev, conditionConfig: config }));
            setShowConditionBuilder(false);
          }}
          onClose={() => setShowConditionBuilder(false)}
        />
      )}

      {showActionConfig && (
        <ActionConfigModal
          configs={workflowData.actionConfig || []}
          onSave={(configs) => {
            setWorkflowData(prev => ({ ...prev, actionConfig: configs }));
            setShowActionConfig(false);
          }}
          onClose={() => setShowActionConfig(false)}
        />
      )}
    </div>
  );
}
