# Automation User Guide

## Getting Started with Automation

The Automation system allows you to create workflows that automatically perform actions when certain events occur in your project management system. This guide will help you create your first automation workflow.

## Accessing Automation

1. Click on the **Automation** tab in the left sidebar (⚡ icon)
2. You'll see the Automation dashboard with any existing workflows
3. Click **Create Workflow** to start building your first automation

## Creating Your First Workflow

### Step 1: Choose a Trigger

A trigger defines when your workflow should run. Common triggers include:

- **Task Created**: When a new task is added
- **Task Status Changed**: When a task moves between statuses
- **Task Assigned**: When someone is assigned to a task
- **Comment Added**: When someone comments on a task
- **Schedule**: Run at specific times (daily, weekly, monthly)

**Example**: Choose "Task Created" to run the workflow every time a new task is added.

### Step 2: Add Conditions (Optional)

Conditions filter when your workflow runs. Without conditions, the workflow runs for every trigger event.

**Example Conditions**:
- Only run for high priority tasks
- Only run for tasks in specific projects
- Only run for tasks assigned to certain users

To add conditions:
1. Click **Add Conditions** in the workflow builder
2. Choose the field to check (e.g., "priority")
3. Select the comparison (e.g., "equals")
4. Enter the value (e.g., "high")

### Step 3: Configure Actions

Actions define what happens when the trigger fires and conditions are met.

**Common Actions**:
- **Update Task Status**: Change task status automatically
- **Assign Task**: Assign tasks to specific users or groups
- **Send Notification**: Alert users about important events
- **Add Comment**: Add automated comments to tasks
- **Create Task**: Generate new tasks or subtasks

**Example**: Add a "Send Notification" action to alert the project manager when high-priority tasks are created.

### Step 4: Test and Save

1. Click **Test** to simulate your workflow with sample data
2. Review the test results to ensure it works as expected
3. Click **Save** to activate your workflow
4. Toggle the workflow **Active** to start automation

## Workflow Templates

### Using Predefined Templates

Save time by starting with proven workflow templates:

1. Click **From Template** when creating a workflow
2. Browse available templates
3. Select a template that matches your needs
4. Customize the template for your specific requirements

### Popular Templates

#### Auto-assign High Priority Tasks
- **Trigger**: Task Created
- **Condition**: Priority equals "high"
- **Action**: Assign to team lead + Send notification

#### Status Change Notifications
- **Trigger**: Task Status Changed
- **Action**: Notify assigned users of status updates

#### Overdue Task Reminders
- **Trigger**: Daily schedule at 9 AM
- **Condition**: Due date is past + Status is not "done"
- **Action**: Send reminder notifications

## Advanced Features

### Dynamic Values

Use template variables to make your workflows dynamic:

- `{{task.title}}` - The task's title
- `{{task.assignedUsers}}` - List of assigned users
- `{{currentUser.name}}` - Name of the user who triggered the workflow
- `{{project.name}}` - The project name

**Example**: Create a comment that says "Task {{task.title}} has been completed by {{currentUser.name}}"

### Complex Conditions

Build sophisticated condition logic:

#### AND Logic
All conditions must be true:
- Priority equals "high" AND Status equals "todo"

#### OR Logic
Any condition can be true:
- Priority equals "high" OR Tags contain "urgent"

#### Nested Groups
Combine AND/OR logic:
- (Priority equals "high" OR Tags contain "urgent") AND Status not equals "done"

### Multiple Actions

Chain multiple actions together:
1. Update task status to "in-progress"
2. Assign to team lead
3. Send notification to stakeholders
4. Add comment with timestamp

## Managing Workflows

### Workflow Dashboard

The main dashboard shows:
- **Active/Inactive status**: Toggle workflows on/off
- **Execution count**: How many times the workflow has run
- **Success rate**: Percentage of successful executions
- **Last execution**: When the workflow last ran

### Workflow Actions

- **Edit**: Modify workflow configuration
- **Duplicate**: Create a copy with a new name
- **View Executions**: See detailed execution logs
- **Export**: Save workflow as a template file
- **Delete**: Remove the workflow permanently

### Execution Monitoring

View detailed execution logs:
1. Click the **History** icon next to any workflow
2. See all executions with timestamps and status
3. Click on any execution to see detailed results
4. Review any errors or failures

## Best Practices

### Workflow Design

1. **Start Simple**: Begin with basic workflows and add complexity gradually
2. **Use Descriptive Names**: Make workflow purposes clear
3. **Test Thoroughly**: Always test workflows before activating
4. **Document Purpose**: Add descriptions explaining what each workflow does

### Performance Tips

1. **Specific Triggers**: Use the most specific trigger possible
2. **Efficient Conditions**: Keep condition logic simple and fast
3. **Limit Actions**: Avoid too many actions in a single workflow
4. **Monitor Performance**: Check execution times and success rates

### Security Considerations

1. **Principle of Least Privilege**: Only automate actions you can perform manually
2. **Validate Recipients**: Ensure notification recipients are appropriate
3. **Review Regularly**: Audit workflows periodically for relevance
4. **Test Changes**: Always test workflow modifications

## Common Use Cases

### Project Management

#### New Task Triage
- **Trigger**: Task Created
- **Conditions**: No assignee set
- **Actions**: Assign to project manager + Add "needs-triage" tag

#### Sprint Planning
- **Trigger**: Task Status Changed to "ready"
- **Conditions**: Sprint field is empty
- **Actions**: Add to current sprint + Notify scrum master

### Team Communication

#### Daily Standup Prep
- **Trigger**: Daily schedule at 8 AM
- **Actions**: Send summary of yesterday's completed tasks to team

#### Milestone Notifications
- **Trigger**: Task Status Changed to "done"
- **Conditions**: Task has "milestone" tag
- **Actions**: Notify all project stakeholders

### Quality Assurance

#### Code Review Requests
- **Trigger**: Task Status Changed to "review"
- **Actions**: Assign to code reviewer + Add to review queue

#### Bug Escalation
- **Trigger**: Task Created
- **Conditions**: Priority is "critical" AND Type is "bug"
- **Actions**: Assign to senior developer + Notify management

## Troubleshooting

### Workflow Not Running

**Check These Items**:
1. Is the workflow active? (Toggle should be green)
2. Are the trigger conditions being met?
3. Are the workflow conditions too restrictive?
4. Check the execution log for errors

### Actions Not Working

**Common Issues**:
1. **Permission Errors**: You may not have permission to perform the action
2. **Invalid Values**: Check that field values are valid (e.g., valid user IDs)
3. **Template Syntax**: Ensure template variables use correct syntax `{{field.name}}`
4. **Missing Data**: Verify that referenced data exists

### Performance Issues

**Optimization Steps**:
1. Review condition complexity
2. Check for too many simultaneous executions
3. Simplify action sequences
4. Contact support for system-level issues

## Getting Help

### Resources

1. **Execution Logs**: Check detailed execution information
2. **Test Mode**: Use workflow testing to debug issues
3. **Templates**: Start with proven template patterns
4. **Documentation**: Refer to technical documentation for advanced features

### Support

If you encounter issues:
1. Check the execution logs for error details
2. Try testing the workflow with sample data
3. Simplify the workflow to isolate the problem
4. Contact your system administrator for assistance

## Tips for Success

1. **Start Small**: Begin with simple, single-action workflows
2. **Iterate**: Gradually add complexity as you become comfortable
3. **Monitor**: Regularly check workflow performance and adjust as needed
4. **Share**: Export successful workflows as templates for your team
5. **Learn**: Study the predefined templates to understand best practices

Remember: Automation should save time and reduce errors, not create complexity. Focus on automating repetitive tasks that follow clear, consistent rules.
