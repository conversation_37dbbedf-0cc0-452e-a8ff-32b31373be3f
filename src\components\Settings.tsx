import React, { useRef, useState } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { Download, Upload, AlertCircle, Loader2 } from 'lucide-react';

export default function SettingsPanel() {
  const {
    tasks,
    projects,
    userGroups,
    users,
    columns,
    folders,
    skillsetGroups,
    userCapacities,
    taskEfforts,
    addTask,
    addProject,
    addUserGroup,
    addColumn,
    addFolder,
    addUser,
    addSkillsetGroup,
    addUserCapacity,
    addTaskEffort
  } = useSupabaseStore();

  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const projectsFileInputRef = useRef<HTMLInputElement>(null);
  const tasksFileInputRef = useRef<HTMLInputElement>(null);

  const handleDownload = (type: 'full' | 'projects' | 'tasks') => {
    let data;
    let filename;

    switch (type) {
      case 'full':
        data = {
          tasks: tasks.map(task => ({
            ...task,
            comments: task.comments || [],
            history: task.history || [],
            subtasks: task.subtasks || [],
            effort: task.effort || undefined
          })),
          projects,
          userGroups,
          users,
          columns,
          folders,
          skillsetGroups,
          userCapacities,
          taskEfforts,
          version: '2.0',
          timestamp: new Date().toISOString(),
          features: [
            'resource-management',
            'capacity-planning',
            'skillset-groups',
            'effort-estimation',
            'task-filtering'
          ]
        };
        filename = `taskflow-backup-${new Date().toISOString().split('T')[0]}.json`;
        break;
      case 'projects':
        data = {
          projects,
          folders,
          version: '1.0',
          timestamp: new Date().toISOString()
        };
        filename = `taskflow-projects-${new Date().toISOString().split('T')[0]}.json`;
        break;
      case 'tasks':
        const projectMap = new Map(projects.map(p => [p.id, p]));
        const folderMap = new Map(folders.map(f => [f.id, f]));
        
        const tasksWithRelations = tasks.map(task => ({
          ...task,
          comments: task.comments || [],
          history: task.history || [],
          subtasks: task.subtasks || [],
          projectDetails: task.projectId ? {
            project: projectMap.get(task.projectId),
            folder: projectMap.get(task.projectId)?.folderId ? 
              folderMap.get(projectMap.get(task.projectId)?.folderId || '') : undefined
          } : undefined
        }));

        data = {
          tasks: tasksWithRelations,
          version: '1.0',
          timestamp: new Date().toISOString()
        };
        filename = `taskflow-tasks-${new Date().toISOString().split('T')[0]}.json`;
        break;
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>, type: 'full' | 'projects' | 'tasks') => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);

    try {
      const text = await file.text();
      const backup = JSON.parse(text);
      
      switch (type) {
        case 'full':
          // Validate required arrays
          const requiredArrays = ['tasks', 'projects', 'userGroups', 'users', 'columns', 'folders'];
          for (const arrayName of requiredArrays) {
            if (!Array.isArray(backup[arrayName])) {
              throw new Error(`Invalid backup file format: missing or invalid ${arrayName}`);
            }
          }

          if (!backup.version || !backup.timestamp) {
            throw new Error('Invalid backup file format: missing version or timestamp');
          }

          resetAll();

          // Import skillset groups first (if available)
          if (Array.isArray(backup.skillsetGroups)) {
            for (const skillset of backup.skillsetGroups) {
              await new Promise(resolve => setTimeout(resolve, 0));
              addSkillsetGroup({ ...skillset, id: skillset.id });
            }
          }

          for (const folder of backup.folders) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addFolder({ ...folder, id: folder.id });
          }

          for (const project of backup.projects) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addProject({ ...project, id: project.id });
          }

          for (const group of backup.userGroups) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addUserGroup({ ...group, id: group.id });
          }

          for (const user of backup.users) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addUser({
              ...user,
              id: user.id,
              skillsetIds: user.skillsetIds || [],
              capacity: user.capacity || undefined
            });
          }

          for (const column of backup.columns) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addColumn({ ...column, id: column.id });
          }

          // Import user capacities (if available)
          if (Array.isArray(backup.userCapacities)) {
            for (const capacity of backup.userCapacities) {
              await new Promise(resolve => setTimeout(resolve, 0));
              addUserCapacity(capacity);
            }
          }

          // Import task efforts (if available)
          if (Array.isArray(backup.taskEfforts)) {
            for (const effort of backup.taskEfforts) {
              await new Promise(resolve => setTimeout(resolve, 0));
              addTaskEffort(effort);
            }
          }

          for (const task of backup.tasks) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addTask({
              ...task,
              id: task.id,
              comments: task.comments || [],
              history: task.history || [],
              subtasks: task.subtasks || [],
              effort: task.effort || undefined
            });
          }
          break;

        case 'projects':
          if (!backup.version || !backup.timestamp || 
              !Array.isArray(backup.projects) ||
              !Array.isArray(backup.folders)) {
            throw new Error('Invalid projects backup file format');
          }

          resetProjects();

          for (const folder of backup.folders) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addFolder({ ...folder, id: folder.id });
          }

          for (const project of backup.projects) {
            await new Promise(resolve => setTimeout(resolve, 0));
            addProject({ ...project, id: project.id });
          }
          break;

        case 'tasks':
          if (!backup.version || !backup.timestamp || 
              !Array.isArray(backup.tasks)) {
            throw new Error('Invalid tasks backup file format');
          }

          resetTasks();

          for (const taskData of backup.tasks) {
            const { projectDetails, ...task } = taskData;
            
            if (projectDetails?.project) {
              const projectExists = projects.some(p => p.id === projectDetails.project.id);
              
              if (!projectExists) {
                if (projectDetails.folder && !folders.some(f => f.id === projectDetails.folder.id)) {
                  await new Promise(resolve => setTimeout(resolve, 0));
                  addFolder(projectDetails.folder);
                }
                
                await new Promise(resolve => setTimeout(resolve, 0));
                addProject(projectDetails.project);
              }
            }

            await new Promise(resolve => setTimeout(resolve, 0));
            addTask({
              ...task,
              id: task.id,
              comments: task.comments || [],
              history: task.history || [],
              subtasks: task.subtasks || [],
              effort: task.effort || undefined
            });
          }
          break;
      }

      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} backup restored successfully!`);
    } catch (error) {
      console.error('Error importing backup:', error);
      alert('Error importing backup. Please check the file format.');
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) fileInputRef.current.value = '';
      if (projectsFileInputRef.current) projectsFileInputRef.current.value = '';
      if (tasksFileInputRef.current) tasksFileInputRef.current.value = '';
    }
  };

  const BackupSection = ({ 
    title, 
    description, 
    type, 
    inputRef 
  }: { 
    title: string; 
    description: string; 
    type: 'full' | 'projects' | 'tasks';
    inputRef: React.RefObject<HTMLInputElement>;
  }) => (
    <div className="border-t border-gray-700 pt-6 first:border-t-0 first:pt-0">
      <h4 className="text-md font-medium mb-2 text-gray-300">{title}</h4>
      <p className="text-sm text-gray-400 mb-3">{description}</p>
      <div className="flex gap-3">
        <button
          onClick={() => handleDownload(type)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          Download
        </button>
        <button
          onClick={() => inputRef.current?.click()}
          disabled={isImporting}
          className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isImporting ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Upload className="w-4 h-4" />
          )}
          {isImporting ? 'Importing...' : 'Import'}
        </button>
        <input
          type="file"
          ref={inputRef}
          onChange={(e) => handleImport(e, type)}
          accept=".json"
          className="hidden"
          disabled={isImporting}
        />
      </div>
    </div>
  );

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-6 text-white">Settings</h2>

      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl">
        <h3 className="text-lg font-semibold mb-4 text-white">Backup & Restore</h3>
        
        <div className="space-y-6">
          <BackupSection
            title="Full Backup"
            description="Download or restore a complete backup of all your data including projects, tasks, folders, users, skillset groups, capacity settings, effort estimations, and all resource management data"
            type="full"
            inputRef={fileInputRef}
          />

          <BackupSection
            title="Projects & Folders Backup"
            description="Download or restore only your projects and folder structure (does not include resource management data)"
            type="projects"
            inputRef={projectsFileInputRef}
          />

          <BackupSection
            title="Tasks Backup"
            description="Download or restore only your tasks with effort estimations while maintaining project relationships (includes task-level resource data)"
            type="tasks"
            inputRef={tasksFileInputRef}
          />

          {/* Backup Content Information */}
          <div className="bg-gray-700 rounded-lg p-4 mt-6">
            <h4 className="text-sm font-medium text-gray-300 mb-3">What's included in each backup:</h4>
            <div className="space-y-2 text-xs text-gray-400">
              <div>
                <span className="font-medium text-blue-400">Full Backup:</span> Projects, Tasks, Folders, Users, User Groups, Columns, Skillset Groups, User Capacities, Task Efforts, and all resource management data
              </div>
              <div>
                <span className="font-medium text-green-400">Projects & Folders:</span> Project structure and folder organization only
              </div>
              <div>
                <span className="font-medium text-purple-400">Tasks:</span> Tasks with effort estimations, comments, history, and subtasks
              </div>
            </div>
          </div>

          <div className="flex items-start gap-2 text-sm text-yellow-400 mt-4">
            <AlertCircle className="w-4 h-4 flex-shrink-0 mt-0.5" />
            <div>
              <p className="mb-2">
                <strong>Warning:</strong> Importing a backup will replace the corresponding existing data.
                Make sure to download a backup of your current data first if needed.
              </p>
              <p className="text-xs">
                <strong>Version Compatibility:</strong> Backups from version 2.0+ include resource management features.
                Older backups (v1.0) will import successfully but without skillsets, capacities, or effort data.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}