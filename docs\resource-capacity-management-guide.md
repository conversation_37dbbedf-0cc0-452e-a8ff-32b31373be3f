# Resource and Capacity Management System

> **📋 Related Documentation**: See [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md) for user permissions and security considerations when managing resources across teams.

## Overview

The Resource and Capacity Management System provides comprehensive tools for planning, tracking, and optimizing team capacity across different skillsets. This system enables project managers to:

- Define and manage skillset groups
- Set user capacity and availability
- Estimate task effort per skillset
- Visualize capacity vs. demand in calendar views
- Identify resource bottlenecks and over-allocation

## Key Features

### 1. Skillset Group Management

**Location**: Sidebar → Skillsets

- **Create Skillsets**: Define skillset groups like 'HTML', 'Image Work', 'ESP Integrations', 'Web Development'
- **Color Coding**: Assign colors to skillsets for easy visual identification
- **CRUD Operations**: Add, edit, delete skillset groups
- **Descriptions**: Add optional descriptions for clarity

### 2. User Management with Skillsets

**Location**: Sidebar → Users

- **Skillset Assignment**: Assign multiple skillsets to each user
- **Visual Display**: See user skillsets with color-coded badges
- **Capacity Management**: Set individual user capacity per skillset

### 3. Capacity Planning

**Location**: Users → Capacity Management (Clock icon)

- **Daily/Weekly Hours**: Set general capacity in hours per day/week
- **Working Days**: Define which days of the week the user works
- **Skillset-Specific Capacity**: Allocate capacity hours per skillset
- **Effective Periods**: Set date ranges for capacity changes

### 4. Task Effort Estimation

**Location**: Task Form → Effort Estimation section

- **Per-Skillset Estimates**: Break down task effort by required skillsets
- **User Assignment**: Assign specific users to skillset requirements
- **Actual vs. Estimated**: Track actual hours against estimates
- **Variance Analysis**: See over/under estimation patterns

### 5. Resource Calendar

**Location**: Sidebar → Resource Calendar

- **Multiple Views**: Day, week, and month views
- **Capacity Visualization**: Color-coded capacity status indicators
- **Over-Capacity Alerts**: Highlight days/periods with resource conflicts
- **Skillset Breakdown**: See capacity utilization per skillset
- **Task Distribution**: View tasks due on specific dates

## Getting Started

### Step 1: Set Up Skillsets

1. Navigate to **Sidebar → Skillsets**
2. Click **"Add Skillset Group"**
3. Enter name (e.g., "Frontend Development")
4. Add description (optional)
5. Choose a color for visual identification
6. Save the skillset

### Step 2: Configure Users

1. Navigate to **Sidebar → Users**
2. For each user, click the **Edit** button
3. Select relevant skillsets from the dropdown
4. Click the **Clock icon** to manage capacity
5. Set daily/weekly hours and working days
6. Define skillset-specific capacity allocations

### Step 3: Estimate Task Effort

1. Create or edit a task
2. Scroll to the **"Effort Estimation"** section
3. Click **"Add Skillset"** to add effort estimates
4. Select the required skillset
5. Enter estimated hours
6. Assign a specific user (optional)
7. Save the task

### Step 4: Monitor Capacity

1. Navigate to **Sidebar → Resource Calendar**
2. Switch between Day, Week, and Month views
3. Look for color-coded capacity indicators:
   - **Green**: Under capacity (≤70%)
   - **Blue**: Optimal capacity (71-90%)
   - **Yellow**: Over capacity (91-110%)
   - **Red**: Critical over-capacity (>110%)

## Capacity Status Indicators

### Color Coding
- 🟢 **Green**: Under Capacity (≤70%) - Team has available bandwidth
- 🔵 **Blue**: Optimal Capacity (71-90%) - Good utilization level
- 🟡 **Yellow**: Over Capacity (91-110%) - Approaching limits, monitor closely
- 🔴 **Red**: Critical (>110%) - Over-allocated, requires immediate attention

### Visual Elements
- **Progress Bars**: Show utilization percentage
- **Capacity Chips**: Quick status indicators with percentages
- **Detailed Breakdowns**: Hover or click for specific numbers

## Best Practices

### 1. Skillset Definition
- Keep skillsets specific but not overly granular
- Use consistent naming conventions
- Assign meaningful colors that team members can remember
- Review and update skillsets as team capabilities evolve

### 2. Capacity Planning
- Set realistic daily/weekly hour expectations
- Account for meetings, breaks, and non-project work
- Update capacity when team members are on vacation or training
- Use effective date ranges for temporary capacity changes

### 3. Effort Estimation
- Break down complex tasks into skillset components
- Use historical data to improve estimation accuracy
- Regularly compare actual vs. estimated hours
- Adjust future estimates based on variance patterns

### 4. Resource Monitoring
- Check the Resource Calendar weekly during planning
- Address over-capacity situations proactively
- Balance workload across team members
- Consider cross-training to increase flexibility

## Troubleshooting

### Common Issues

**Q: User doesn't appear in skillset assignment dropdown**
A: Ensure the user has the required skillset assigned in their profile

**Q: Capacity shows as 0 even though user has hours set**
A: Check that the user's working days include the date in question and that the effective date range covers the period

**Q: Task effort doesn't appear in calendar**
A: Verify that the task has a due date and effort estimates with assigned skillsets

**Q: Over-capacity warnings seem incorrect**
A: Review skillset-specific capacity allocations and ensure they don't exceed total daily capacity

### Performance Considerations

- Large numbers of users (>100) may slow calendar calculations
- Complex date ranges with many tasks may impact rendering
- Consider filtering by team or project for better performance

## Security Considerations

### User Permissions for Resource Management

Resource management operations are subject to Row Level Security (RLS) policies:

#### Skillset Groups
- **View**: All authenticated users can view skillset groups
- **Create/Update/Delete**: Only admins and creators can modify skillset groups

#### User Capacity Management
- **View**: Users can view their own capacity; admins can view all
- **Update**: Users can update their own capacity; admins can update any user's capacity
- **Create/Delete**: Admins only

#### Task Effort Estimation
- **View**: All users can view effort estimates for tasks they have access to
- **Update**: Subject to task update permissions (see [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md))
- **Create/Delete**: Follows task creation/deletion permissions

### Best Practices for Multi-User Environments

1. **Admin Assignment**: Ensure at least one admin user for system management
2. **Capacity Updates**: Train users to update their own capacity when availability changes
3. **Permission Awareness**: Users should understand they can only modify resources they own or are assigned to
4. **Regular Audits**: Admins should periodically review user roles and permissions

## Integration Points

### With Existing Features
- **Task Management**: Effort estimates integrate with task creation/editing
- **Project Planning**: Project-level effort rollups from task estimates
- **User Groups**: Skillsets complement existing user group assignments
- **Timeline View**: Capacity data enhances timeline planning

### Data Flow
1. **Skillsets** → Define available capabilities
2. **Users** → Assign skillsets and set capacity
3. **Tasks** → Estimate effort per skillset
4. **Calendar** → Visualize capacity vs. demand
5. **Reports** → Analyze utilization and planning accuracy

## Future Enhancements

- **Automated Scheduling**: Suggest optimal task assignments based on capacity
- **Capacity Forecasting**: Predict future resource needs
- **Skills Gap Analysis**: Identify missing capabilities in the team
- **Integration with Time Tracking**: Automatic actual hours from time logs
- **Resource Optimization**: Recommend workload balancing strategies
