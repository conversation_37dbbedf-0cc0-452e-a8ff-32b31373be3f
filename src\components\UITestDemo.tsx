import React, { useState } from 'react';
import { getStatusBadge, getPriorityBadge } from '../utils/statusUtils';
import CompactTimelineStats from './CompactTimelineStats';
import ClickableStatusBadge from './ClickableStatusBadge';
import ClickablePriorityBadge from './ClickablePriorityBadge';
import ClickableProjectBadge from './ClickableProjectBadge';
import CompactAssignmentField from './CompactAssignmentField';
import { Task } from '../types';

// Demo component to test our new UI elements
export default function UITestDemo() {
  const [demoStatus, setDemoStatus] = useState<Task['status']>('in-progress');
  const [demoPriority, setDemoPriority] = useState<Task['priority']>('high');
  const [demoProjectId, setDemoProjectId] = useState<string>('1');
  const [demoAssigneeId, setDemoAssigneeId] = useState<string>('1');
  const [demoOwnerId, setDemoOwnerId] = useState<string>('2');
  const [demoAdditionalUsers, setDemoAdditionalUsers] = useState<string[]>(['3']);
  const [demoGroups, setDemoGroups] = useState<string[]>(['1']);
  const mockDurations = [
    {
      id: '1',
      taskId: 'task1',
      status: 'todo',
      startTime: '2024-01-01T09:00:00Z',
      endTime: '2024-01-01T11:00:00Z'
    },
    {
      id: '2',
      taskId: 'task1',
      status: 'in-progress',
      startTime: '2024-01-01T11:00:00Z',
      endTime: '2024-01-01T15:30:00Z'
    },
    {
      id: '3',
      taskId: 'task1',
      status: 'review',
      startTime: '2024-01-01T15:30:00Z',
      endTime: '2024-01-01T16:00:00Z'
    }
  ];

  const mockColumns = [
    { id: 'todo', title: 'To Do', color: 'bg-gray-100 text-gray-800' },
    { id: 'in-progress', title: 'In Progress', color: 'bg-blue-100 text-blue-800' },
    { id: 'review', title: 'Review', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'done', title: 'Done', color: 'bg-green-100 text-green-800' }
  ];

  const mockProjects = [
    { id: '1', name: 'Client 1' },
    { id: '2', name: 'Internal Project' },
    { id: '3', name: 'Marketing Campaign' }
  ];

  const mockUsers = [
    { id: '1', email: '<EMAIL>', name: 'John Doe' },
    { id: '2', email: '<EMAIL>', name: 'Jane Smith' },
    { id: '3', email: '<EMAIL>', name: 'Bob Johnson' },
    { id: '4', email: '<EMAIL>', name: 'Alice Brown' }
  ];

  const mockGroups = [
    { id: '1', name: 'Developers', color: 'bg-blue-100 text-blue-800' },
    { id: '2', name: 'Project Managers', color: 'bg-green-100 text-green-800' },
    { id: '3', name: 'Designers', color: 'bg-purple-100 text-purple-800' }
  ];

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-8">UI Components Test Demo</h1>
      
      {/* Status Badges Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Status Badges</h2>
        <div className="flex gap-3">
          {getStatusBadge('todo', mockColumns)}
          {getStatusBadge('in-progress', mockColumns)}
          {getStatusBadge('review', mockColumns)}
          {getStatusBadge('done', mockColumns)}
        </div>
      </div>

      {/* Priority Badges Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Priority Badges</h2>
        <div className="flex gap-3">
          {getPriorityBadge('low')}
          {getPriorityBadge('medium')}
          {getPriorityBadge('high')}
          {getPriorityBadge('urgent')}
        </div>
      </div>

      {/* Compact Timeline Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Compact Timeline</h2>
        <div className="bg-white border rounded-lg p-4">
          <CompactTimelineStats
            durations={mockDurations}
            currentStatus="review"
          />
        </div>
      </div>

      {/* Clickable Badges Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Clickable Badges Demo</h2>
        <div className="flex gap-3 mb-4">
          <ClickableProjectBadge
            projectId={demoProjectId}
            projects={mockProjects}
            onChange={setDemoProjectId}
          />
          <ClickableStatusBadge
            status={demoStatus}
            columns={mockColumns}
            onChange={setDemoStatus}
          />
          <ClickablePriorityBadge
            priority={demoPriority}
            onChange={setDemoPriority}
          />
        </div>
        <p className="text-sm text-gray-600">
          Current: Project = {mockProjects.find(p => p.id === demoProjectId)?.name || 'None'}, Status = {demoStatus}, Priority = {demoPriority}
        </p>
      </div>

      {/* Compact Assignment Fields Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Compact Assignment Fields Demo</h2>
        <div className="bg-gray-50 p-5 rounded-xl border border-gray-200">
          <div className="grid grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Start Date</label>
              <input type="date" className="w-full border rounded-lg p-2 text-sm" defaultValue="2024-01-15" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Due Date</label>
              <input type="date" className="w-full border rounded-lg p-2 text-sm" defaultValue="2024-01-30" />
            </div>
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 -m-3 col-span-2">
              <div className="grid grid-cols-2 gap-4">
                <CompactAssignmentField
                  label="Primary Assignee"
                  selectedUserId={demoAssigneeId}
                  users={mockUsers}
                  additionalUsers={demoAdditionalUsers}
                  onUserChange={setDemoAssigneeId}
                  onAdditionalUsersChange={setDemoAdditionalUsers}
                  showAdvanced={true}
                />
                <CompactAssignmentField
                  label="Owner"
                  selectedUserId={demoOwnerId}
                  users={mockUsers}
                  groups={demoGroups}
                  availableGroups={mockGroups}
                  onUserChange={setDemoOwnerId}
                  onGroupsChange={setDemoGroups}
                  showAdvanced={true}
                />
              </div>
            </div>
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Assignee: {mockUsers.find(u => u.id === demoAssigneeId)?.name || 'None'} |
          Owner: {mockUsers.find(u => u.id === demoOwnerId)?.name || 'None'} |
          Additional: {demoAdditionalUsers.length} users |
          Groups: {demoGroups.length} selected
        </p>
      </div>

      {/* Header Layout Demo */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Header Layout Demo</h2>
        <div className="bg-white border rounded-lg">
          <div className="border-b border-gray-200 p-6">
            <div className="flex items-center justify-between gap-4">
              {/* Left side: Title, Project, Status, Priority */}
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <span className="text-xl font-semibold">Sample Task Title That Might Be Very Long</span>
                <ClickableProjectBadge
                  projectId={demoProjectId}
                  projects={mockProjects}
                  onChange={setDemoProjectId}
                />
                <ClickableStatusBadge
                  status={demoStatus}
                  columns={mockColumns}
                  onChange={setDemoStatus}
                />
                <ClickablePriorityBadge
                  priority={demoPriority}
                  onChange={setDemoPriority}
                />
              </div>

              {/* Right side: Action buttons */}
              <div className="flex items-center gap-2 flex-shrink-0">
                <button className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors font-medium text-sm">
                  Cancel
                </button>
                <button className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors font-medium text-sm shadow-sm">
                  Update Task
                </button>
              </div>
            </div>

            {/* Compact Timeline */}
            <div className="mt-4">
              <CompactTimelineStats
                durations={mockDurations}
                currentStatus={demoStatus}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
