/**
 * Search utility functions for safe text and regex matching
 * Includes protection against ReDoS (Regular Expression Denial of Service) attacks
 */

export interface SearchOptions {
  caseSensitive?: boolean;
  timeout?: number; // milliseconds
}

export interface SearchResult {
  matches: boolean;
  error?: string;
}

/**
 * Safely executes a regex with timeout protection
 */
function safeRegexTest(pattern: RegExp, text: string, timeout: number = 1000): SearchResult {
  return new Promise<SearchResult>((resolve) => {
    const timeoutId = setTimeout(() => {
      resolve({ matches: false, error: 'Regex execution timeout' });
    }, timeout);

    try {
      const matches = pattern.test(text);
      clearTimeout(timeoutId);
      resolve({ matches });
    } catch (error) {
      clearTimeout(timeoutId);
      resolve({ matches: false, error: error instanceof Error ? error.message : 'Regex execution error' });
    }
  });
}

/**
 * Validates if a regex pattern is safe and not overly complex
 */
export function validateRegexPattern(pattern: string): { isValid: boolean; error?: string } {
  try {
    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /\(\?\=.*\)\+/,  // Positive lookahead with quantifier
      /\(\?\!.*\)\+/,  // Negative lookahead with quantifier
      /\(\?\<\=.*\)\+/, // Positive lookbehind with quantifier
      /\(\?\<\!.*\)\+/, // Negative lookbehind with quantifier
      /\(\.\*\)\+/,    // Nested quantifiers
      /\(\.\+\)\+/,    // Nested quantifiers
    ];

    for (const dangerous of dangerousPatterns) {
      if (dangerous.test(pattern)) {
        return { isValid: false, error: 'Pattern contains potentially dangerous constructs' };
      }
    }

    // Try to create the regex
    new RegExp(pattern);
    return { isValid: true };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Invalid regex pattern' 
    };
  }
}

/**
 * Performs case-insensitive text search
 */
export function searchText(text: string, searchTerm: string, options: SearchOptions = {}): SearchResult {
  if (!searchTerm.trim()) {
    return { matches: true };
  }

  try {
    const textToSearch = options.caseSensitive ? text : text.toLowerCase();
    const termToSearch = options.caseSensitive ? searchTerm : searchTerm.toLowerCase();
    
    const matches = textToSearch.includes(termToSearch);
    return { matches };
  } catch (error) {
    return { matches: false, error: error instanceof Error ? error.message : 'Text search error' };
  }
}

/**
 * Performs regex search with safety checks
 */
export async function searchRegex(text: string, pattern: string, options: SearchOptions = {}): Promise<SearchResult> {
  if (!pattern.trim()) {
    return { matches: true };
  }

  // Validate the pattern first
  const validation = validateRegexPattern(pattern);
  if (!validation.isValid) {
    return { matches: false, error: validation.error };
  }

  try {
    const flags = options.caseSensitive ? 'g' : 'gi';
    const regex = new RegExp(pattern, flags);
    
    return await safeRegexTest(regex, text, options.timeout || 1000);
  } catch (error) {
    return { matches: false, error: error instanceof Error ? error.message : 'Regex search error' };
  }
}

/**
 * Main search function that handles both text and regex modes
 */
export async function performSearch(
  text: string, 
  searchTerm: string, 
  isRegex: boolean, 
  options: SearchOptions = {}
): Promise<SearchResult> {
  if (isRegex) {
    return await searchRegex(text, searchTerm, options);
  } else {
    return searchText(text, searchTerm, { ...options, caseSensitive: false });
  }
}

/**
 * Debounced search function for performance optimization
 */
export function createDebouncedSearch(delay: number = 300) {
  let timeoutId: NodeJS.Timeout | null = null;

  return function debouncedSearch<T extends any[]>(
    fn: (...args: T) => void,
    ...args: T
  ): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      fn(...args);
      timeoutId = null;
    }, delay);
  };
}
