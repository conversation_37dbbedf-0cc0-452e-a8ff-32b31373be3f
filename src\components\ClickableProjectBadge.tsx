import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface Project {
  id: string;
  name: string;
}

interface ClickableProjectBadgeProps {
  projectId: string;
  projects: Project[];
  onChange: (projectId: string) => void;
}

export default function ClickableProjectBadge({ projectId, projects, onChange }: ClickableProjectBadgeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current project display info
  const getCurrentProjectInfo = () => {
    if (!projectId) {
      return {
        label: 'No Project',
        color: 'bg-gray-100 text-gray-600'
      };
    }

    const project = projects.find(p => p.id === projectId);
    return {
      label: project ? `Project: ${project.name}` : 'Unknown Project',
      color: 'bg-orange-100 text-orange-800'
    };
  };

  // Get all available project options
  const getProjectOptions = () => {
    const options = [
      { id: '', label: 'No Project', color: 'bg-gray-100 text-gray-600' },
      ...projects.map(project => ({
        id: project.id,
        label: `Project: ${project.name}`,
        color: 'bg-orange-100 text-orange-800'
      }))
    ];
    return options;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentProject = getCurrentProjectInfo();
  const projectOptions = getProjectOptions();

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium transition-all hover:opacity-80 ${currentProject.color}`}
      >
        {currentProject.label}
        <ChevronDown className="w-3 h-3" />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px] max-h-60 overflow-y-auto">
          {projectOptions.map((option) => (
            <button
              key={option.id || 'no-project'}
              type="button"
              onClick={() => {
                onChange(option.id);
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                option.id === projectId ? 'bg-gray-50' : ''
              }`}
            >
              <span className={`inline-flex items-center px-2 py-1 rounded-full ${option.color}`}>
                {option.label}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
