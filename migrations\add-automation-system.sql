-- Migration: Add Automation System Tables
-- Description: Creates tables and policies for the visual workflow automation system
-- Date: 2025-01-18

-- Automation workflows table
CREATE TABLE IF NOT EXISTS automation_workflows (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  is_template BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  trigger_config J<PERSON>N<PERSON> NOT NULL,
  condition_config JSON<PERSON>,
  action_config JSONB NOT NULL,
  execution_count INTEGER DEFAULT 0,
  last_executed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automation execution logs table
CREATE TABLE IF NOT EXISTS automation_executions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workflow_id UUID REFERENCES automation_workflows(id) ON DELETE CASCADE NOT NULL,
  trigger_data JSONB NOT NULL,
  condition_result JSONB,
  action_results JSONB,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, success, failed, skipped
  error_message TEXT,
  execution_time_ms INTEGER,
  executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automation triggers table (for custom/scheduled triggers)
CREATE TABLE IF NOT EXISTS automation_triggers (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workflow_id UUID REFERENCES automation_workflows(id) ON DELETE CASCADE NOT NULL,
  trigger_type TEXT NOT NULL, -- webhook, schedule, manual
  trigger_config JSONB NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_automation_workflows_created_by ON automation_workflows(created_by);
CREATE INDEX IF NOT EXISTS idx_automation_workflows_project_id ON automation_workflows(project_id);
CREATE INDEX IF NOT EXISTS idx_automation_workflows_folder_id ON automation_workflows(folder_id);
CREATE INDEX IF NOT EXISTS idx_automation_workflows_is_active ON automation_workflows(is_active);
CREATE INDEX IF NOT EXISTS idx_automation_executions_workflow_id ON automation_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_automation_executions_status ON automation_executions(status);
CREATE INDEX IF NOT EXISTS idx_automation_executions_executed_at ON automation_executions(executed_at);
CREATE INDEX IF NOT EXISTS idx_automation_triggers_workflow_id ON automation_triggers(workflow_id);
CREATE INDEX IF NOT EXISTS idx_automation_triggers_trigger_type ON automation_triggers(trigger_type);

-- Enable Row Level Security
ALTER TABLE automation_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_triggers ENABLE ROW LEVEL SECURITY;

-- Automation workflows policies
CREATE POLICY "Users can view workflows they created or have access to" ON automation_workflows FOR SELECT USING (
  created_by = auth.uid() OR
  project_id IS NULL OR
  EXISTS (
    SELECT 1 FROM tasks
    WHERE project_id = automation_workflows.project_id
    AND (assigned_user_id = auth.uid() OR auth.uid() = ANY(assigned_users))
  ) OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Users can create workflows" ON automation_workflows FOR INSERT WITH CHECK (
  created_by = auth.uid() AND
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid()
  )
);

CREATE POLICY "Users can update own workflows" ON automation_workflows FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Users can delete own workflows" ON automation_workflows FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Automation executions policies
CREATE POLICY "Users can view executions for workflows they have access to" ON automation_executions FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM automation_workflows
    WHERE id = automation_executions.workflow_id
    AND (
      created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = auth.uid() AND role = 'admin'
      )
    )
  )
);

CREATE POLICY "System can create execution logs" ON automation_executions FOR INSERT WITH CHECK (true);

-- Automation triggers policies
CREATE POLICY "Users can view triggers for workflows they have access to" ON automation_triggers FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM automation_workflows
    WHERE id = automation_triggers.workflow_id
    AND (
      created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = auth.uid() AND role = 'admin'
      )
    )
  )
);

CREATE POLICY "Users can manage triggers for own workflows" ON automation_triggers FOR ALL USING (
  EXISTS (
    SELECT 1 FROM automation_workflows
    WHERE id = automation_triggers.workflow_id
    AND (
      created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = auth.uid() AND role = 'admin'
      )
    )
  )
);

-- Insert sample workflow templates (optional)
INSERT INTO automation_workflows (
  name, 
  description, 
  is_active, 
  is_template, 
  created_by, 
  trigger_config, 
  action_config
) VALUES (
  'Auto-assign High Priority Tasks',
  'Automatically assign high priority tasks to team leads',
  false,
  true,
  (SELECT id FROM auth.users LIMIT 1), -- Use first user as template creator
  '{"type": "task_created"}',
  '[{"type": "assign_task", "assignmentConfig": {"userId": "team-lead-id", "assignmentType": "add"}}]'
) ON CONFLICT DO NOTHING;

-- Migration completed successfully
SELECT 'Automation system tables created successfully' as result;
