---
goal: Add search functionality for tasks and projects in the task list view filters
version: 1.0
date_created: 2025-01-09
owner: AI Agent
tags: [feature, search, filters, ui]
---

# Introduction

This plan implements search functionality for tasks and projects within the existing filter system of the task list view. Users will be able to search by task title or project name using a dropdown selector and text input field, positioned as the first filter option before the assigned users filter.

## 1. Requirements & Constraints

- **REQ-001**: Add search filter as the leftmost filter option in TaskFilterPanel
- **REQ-002**: Provide dropdown to select between "Task Title Contains" and "Project Name Contains"
- **REQ-003**: Include text input field for search term entry
- **REQ-004**: Search must be case-insensitive and support partial matching
- **REQ-005**: Search filter must integrate with existing filter logic (AND operation)
- **REQ-006**: Search filter must be optional and clearable
- **REQ-007**: Search filter must work within current tree node context
- **SEC-001**: Search operations must not expose unauthorized data
- **CON-001**: Must not modify existing filter components or break current functionality
- **CON-002**: Must follow existing UI patterns and styling
- **CON-003**: Must integrate with existing TaskFilter type and store actions
- **GUD-001**: Follow modular implementation approach per new feature rules
- **PAT-001**: Use existing FilterDropdown and input patterns for consistency

## 2. Implementation Steps

### Phase 1: Type and Store Updates
1. Extend TaskFilter interface to include search properties
2. Update useSupabaseStore to handle search filter state
3. Add search filter to clearTaskFilters action

### Phase 2: Search Filter Component
1. Create SearchFilter component with dropdown and text input
2. Implement search type selection (task title vs project name)
3. Add search term input with debouncing for performance
4. Include clear functionality and proper styling

### Phase 3: Integration
1. Add SearchFilter as first component in TaskFilterPanel grid
2. Update filtering logic in TaskListView to handle search
3. Add search filter chips to active filter display
4. Test search functionality with existing filters

## 3. Alternatives

- **ALT-001**: Global search bar in header - rejected due to requirement for filter integration
- **ALT-002**: Combined search field for both tasks and projects - rejected for clarity
- **ALT-003**: Full-text search including descriptions - rejected for scope limitation

## 4. Dependencies

- **DEP-001**: Existing TaskFilterPanel component structure
- **DEP-002**: FilterDropdown component for consistent UI
- **DEP-003**: TaskFilter type definition and store actions
- **DEP-004**: Current filtering logic in TaskListView

## 5. Files

- **FILE-001**: src/types/index.ts - Extend TaskFilter interface with search properties
- **FILE-002**: src/components/SearchFilter.tsx - New search filter component
- **FILE-003**: src/components/TaskFilterPanel.tsx - Integrate SearchFilter component
- **FILE-004**: src/components/TaskListView.tsx - Update filtering logic for search
- **FILE-005**: src/store/useSupabaseStore.ts - Add search filter state and actions

## 6. Testing

- **TEST-001**: Verify search by task title returns correct results
- **TEST-002**: Verify search by project name returns correct results
- **TEST-003**: Test case-insensitive partial matching
- **TEST-004**: Test search filter combination with other filters
- **TEST-005**: Test search filter clearing and reset functionality
- **TEST-006**: Test search filter chips display and removal

## 7. Risks & Assumptions

- **RISK-001**: Performance impact with large task datasets - mitigated by debouncing
- **RISK-002**: UI layout disruption on smaller screens - mitigated by responsive design
- **ASSUMPTION-001**: Users prefer dropdown selection over toggle buttons
- **ASSUMPTION-002**: Case-insensitive search is sufficient for user needs

## 8. Related Specifications / Further Reading

- Existing TaskFilterPanel implementation (src/components/TaskFilterPanel.tsx)
- FilterDropdown component pattern (src/components/FilterDropdown.tsx)
- TaskFilter type definition (src/types/index.ts lines 161-173)
- Current filtering logic (src/components/TaskListView.tsx lines 54-120)
