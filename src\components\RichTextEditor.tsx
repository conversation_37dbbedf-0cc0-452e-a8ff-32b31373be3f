import React, { useMemo } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './RichTextEditor.css';
import { prepareContentForEditor, sanitizeHtml } from '../utils/richTextUtils';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = 'Enter description...',
  className = '',
  disabled = false
}: RichTextEditorProps) {
  
  // Prepare content for the editor (handles both HTML and plain text)
  const editorValue = useMemo(() => {
    return prepareContentForEditor(value);
  }, [value]);

  // Handle content changes
  const handleChange = (content: string) => {
    // Sanitize the content before passing it up
    const sanitizedContent = sanitizeHtml(content);
    onChange(sanitizedContent);
  };

  // Quill modules configuration
  const modules = useMemo(() => ({
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      ['code'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ],
    clipboard: {
      // Strip formatting when pasting
      matchVisual: false,
    }
  }), []);

  // Quill formats configuration
  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'code',
    'list', 'bullet',
    'link'
  ];

  return (
    <div className={`rich-text-editor ${className}`} style={{ marginBottom: '4rem' }}>
      <ReactQuill
        theme="snow"
        value={editorValue}
        onChange={handleChange}
        placeholder={placeholder}
        modules={modules}
        formats={formats}
        readOnly={disabled}
        style={{
          height: '200px', // Approximately equivalent to 10 rows of textarea
          marginBottom: '4rem', // Additional space after the editor
        }}
      />
    </div>
  );
}
