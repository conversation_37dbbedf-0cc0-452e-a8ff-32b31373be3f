import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ConditionEvaluator, ActionExecutor } from '../services/automationEngine';
import { 
  AutomationContext, 
  ConditionConfig, 
  ActionConfig,
  Condition,
  ConditionGroup 
} from '../types/automation';

// Mock services
vi.mock('../services/supabaseService', () => ({
  taskService: {
    updateTask: vi.fn(),
    createTask: vi.fn()
  },
  commentService: {
    addComment: vi.fn()
  },
  notificationService: {
    createNotification: vi.fn()
  }
}));

describe('Automation Engine', () => {
  let mockContext: AutomationContext;

  beforeEach(() => {
    mockContext = {
      triggerData: {
        taskId: 'test-task-id',
        projectId: 'test-project-id'
      },
      currentUser: {
        id: 'user-123',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user'
      },
      entities: {
        task: {
          id: 'test-task-id',
          title: 'Test Task',
          description: 'Test Description',
          status: 'todo',
          priority: 'high',
          assignedUsers: ['user-123'],
          assignedGroups: [],
          tags: ['urgent', 'bug'],
          dueDate: '2024-12-31',
          projectId: 'test-project-id'
        }
      },
      timestamp: '2024-01-15T10:00:00Z'
    };
  });

  describe('ConditionEvaluator', () => {
    it('should evaluate simple equality condition correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'status',
        operator: 'equals',
        value: 'todo',
        valueType: 'static',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate inequality condition correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'status',
        operator: 'not_equals',
        value: 'done',
        valueType: 'static',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate contains condition correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'tags',
        operator: 'contains',
        value: 'urgent',
        valueType: 'static',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate greater than condition correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'assignedUsers',
        operator: 'greater_than',
        value: 0,
        valueType: 'static',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate is_empty condition correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'assignedGroups',
        operator: 'is_empty',
        value: null,
        valueType: 'static',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate AND condition group correctly', () => {
      const conditionGroup: ConditionGroup = {
        id: 'test-group',
        logic: 'AND',
        conditions: [
          {
            id: 'condition-1',
            field: 'status',
            operator: 'equals',
            value: 'todo',
            valueType: 'static',
            entityType: 'task'
          },
          {
            id: 'condition-2',
            field: 'priority',
            operator: 'equals',
            value: 'high',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      };

      const result = ConditionEvaluator.evaluateConditionGroup(conditionGroup, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate OR condition group correctly', () => {
      const conditionGroup: ConditionGroup = {
        id: 'test-group',
        logic: 'OR',
        conditions: [
          {
            id: 'condition-1',
            field: 'status',
            operator: 'equals',
            value: 'done',
            valueType: 'static',
            entityType: 'task'
          },
          {
            id: 'condition-2',
            field: 'priority',
            operator: 'equals',
            value: 'high',
            valueType: 'static',
            entityType: 'task'
          }
        ],
        groups: []
      };

      const result = ConditionEvaluator.evaluateConditionGroup(conditionGroup, mockContext);
      expect(result).toBe(true); // Second condition is true
    });

    it('should handle dynamic values correctly', () => {
      const condition: Condition = {
        id: 'test-condition',
        field: 'assignedUsers',
        operator: 'contains',
        value: '{{currentUser.id}}',
        valueType: 'dynamic',
        entityType: 'task'
      };

      const result = ConditionEvaluator.evaluateCondition(condition, mockContext);
      expect(result).toBe(true);
    });

    it('should evaluate nested condition groups correctly', () => {
      const conditionConfig: ConditionConfig = {
        rootGroup: {
          id: 'root',
          logic: 'AND',
          conditions: [
            {
              id: 'root-condition',
              field: 'status',
              operator: 'not_equals',
              value: 'done',
              valueType: 'static',
              entityType: 'task'
            }
          ],
          groups: [
            {
              id: 'nested-group',
              logic: 'OR',
              conditions: [
                {
                  id: 'nested-condition-1',
                  field: 'priority',
                  operator: 'equals',
                  value: 'high',
                  valueType: 'static',
                  entityType: 'task'
                },
                {
                  id: 'nested-condition-2',
                  field: 'tags',
                  operator: 'contains',
                  value: 'urgent',
                  valueType: 'static',
                  entityType: 'task'
                }
              ],
              groups: []
            }
          ]
        }
      };

      const result = ConditionEvaluator.evaluateConditionConfig(conditionConfig, mockContext);
      expect(result).toBe(true);
    });
  });

  describe('ActionExecutor', () => {
    it('should execute update task status action correctly', async () => {
      const action: ActionConfig = {
        type: 'update_task_status',
        value: 'in-progress'
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
      expect(result.result).toEqual({
        oldStatus: 'todo',
        newStatus: 'in-progress'
      });
    });

    it('should execute assign task action correctly', async () => {
      const action: ActionConfig = {
        type: 'assign_task',
        assignmentConfig: {
          userId: 'user-456',
          assignmentType: 'add'
        }
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
      expect(result.result).toEqual({
        assignedUsers: ['user-123', 'user-456']
      });
    });

    it('should execute add comment action correctly', async () => {
      const action: ActionConfig = {
        type: 'add_comment',
        value: 'Automated comment: Task status updated'
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
    });

    it('should execute send notification action correctly', async () => {
      const action: ActionConfig = {
        type: 'send_notification',
        templateConfig: {
          subject: 'Task Updated',
          body: 'Task {{task.title}} has been updated',
          recipients: ['user-123', 'user-456']
        }
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
    });

    it('should execute create task action correctly', async () => {
      const action: ActionConfig = {
        type: 'create_task',
        taskCreationConfig: {
          title: 'Follow-up: {{task.title}}',
          description: 'Follow-up task for {{task.title}}',
          priority: 'medium',
          assignedUsers: ['user-123'],
          projectId: '{{task.projectId}}'
        }
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
    });

    it('should handle template variables in action values', async () => {
      const action: ActionConfig = {
        type: 'add_comment',
        value: 'Task {{task.title}} priority is {{task.priority}}'
      };

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(true);
    });

    it('should handle action execution errors gracefully', async () => {
      const action: ActionConfig = {
        type: 'update_task_status',
        value: 'invalid-status'
      };

      // Mock the service to throw an error
      const { taskService } = await import('../services/supabaseService');
      vi.mocked(taskService.updateTask).mockRejectedValueOnce(new Error('Invalid status'));

      const result = await ActionExecutor.executeAction(action, mockContext);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid status');
    });
  });

  describe('Integration Tests', () => {
    it('should execute complete workflow correctly', async () => {
      // Test a complete workflow with conditions and multiple actions
      const conditionConfig: ConditionConfig = {
        rootGroup: {
          id: 'root',
          logic: 'AND',
          conditions: [
            {
              id: 'priority-check',
              field: 'priority',
              operator: 'equals',
              value: 'high',
              valueType: 'static',
              entityType: 'task'
            }
          ],
          groups: []
        }
      };

      const actions: ActionConfig[] = [
        {
          type: 'update_task_status',
          value: 'in-progress'
        },
        {
          type: 'add_comment',
          value: 'High priority task automatically moved to in-progress'
        },
        {
          type: 'send_notification',
          templateConfig: {
            subject: 'High Priority Task Alert',
            body: 'Task {{task.title}} has been automatically processed',
            recipients: ['{{task.assignedUsers}}']
          }
        }
      ];

      // Evaluate conditions
      const conditionResult = ConditionEvaluator.evaluateConditionConfig(conditionConfig, mockContext);
      expect(conditionResult).toBe(true);

      // Execute actions
      const actionResults = [];
      for (const action of actions) {
        const result = await ActionExecutor.executeAction(action, mockContext);
        actionResults.push(result);
      }

      // Verify all actions succeeded
      expect(actionResults.every(result => result.success)).toBe(true);
    });
  });
});
