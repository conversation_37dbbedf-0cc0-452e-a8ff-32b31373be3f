import React, { useState, useEffect, useMemo } from 'react';
import { Task, TaskDependency, DependencyType } from '../types';
import { X, Plus, AlertTriangle, Link, Clock } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { dependencyService } from '../services/dependencyService';

interface DependencySelectorProps {
  taskId: string;
  currentDependencies: TaskDependency[];
  onDependenciesChange: (dependencies: TaskDependency[]) => void;
  availableTasks: Task[];
  isAddingDependency: boolean;
  setIsAddingDependency: (isAdding: boolean) => void;
}

export default function DependencySelector({
  taskId,
  currentDependencies,
  onDependenciesChange,
  availableTasks,
  isAddingDependency,
  setIsAddingDependency
}: DependencySelectorProps) {
  const [selectedTaskId, setSelectedTaskId] = useState('');
  const [dependencyType, setDependencyType] = useState<DependencyType>('finish_to_start');
  const [lagDays, setLagDays] = useState(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);



  // Filter out the current task and tasks that already have dependencies
  const availableTasksForDependency = useMemo(() => {
    return availableTasks.filter(task => {
      if (task.id === taskId) return false;
      return !currentDependencies.some(dep => dep.predecessorTaskId === task.id);
    });
  }, [availableTasks, taskId, currentDependencies]);

  const dependencyTypeLabels: Record<DependencyType, string> = {
    finish_to_start: 'Finish-to-Start (FS)',
    start_to_start: 'Start-to-Start (SS)',
    finish_to_finish: 'Finish-to-Finish (FF)',
    start_to_finish: 'Start-to-Finish (SF)'
  };

  const dependencyTypeDescriptions: Record<DependencyType, string> = {
    finish_to_start: 'This task starts after the predecessor finishes',
    start_to_start: 'This task starts when the predecessor starts',
    finish_to_finish: 'This task finishes when the predecessor finishes',
    start_to_finish: 'This task finishes when the predecessor starts'
  };

  const handleAddDependency = async () => {
    if (!selectedTaskId) return;

    setIsLoading(true);
    setValidationErrors([]);

    try {
      // Validate the dependency
      const validation = await dependencyService.validateDependency({
        predecessorTaskId: selectedTaskId,
        successorTaskId: taskId,
        dependencyType,
        lagDays
      });

      if (!validation.isValid) {
        setValidationErrors(validation.errors);
        setIsLoading(false);
        return;
      }

      // Create the dependency
      const result = await dependencyService.createDependency({
        predecessorTaskId: selectedTaskId,
        successorTaskId: taskId,
        dependencyType,
        lagDays
      });

      if (result.success && result.data) {
        console.log('Dependency created successfully:', result.data);
        const updatedDependencies = [...currentDependencies, result.data];

        // Reset form first, then update dependencies
        setSelectedTaskId('');
        setDependencyType('finish_to_start');
        setLagDays(0);
        setIsAddingDependency(false);

        // Update dependencies after a small delay to prevent immediate re-render
        setTimeout(() => {
          onDependenciesChange(updatedDependencies);
        }, 100);
      } else {
        setValidationErrors([result.error || 'Failed to create dependency']);
      }
    } catch (error) {
      console.error('Failed to add dependency:', error);
      setValidationErrors(['Failed to create dependency']);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveDependency = async (dependencyId: string) => {
    setIsLoading(true);
    
    try {
      const result = await dependencyService.deleteDependency(dependencyId);
      
      if (result.success) {
        const updatedDependencies = currentDependencies.filter(dep => dep.id !== dependencyId);
        onDependenciesChange(updatedDependencies);
      } else {
        console.error('Failed to remove dependency:', result.error);
      }
    } catch (error) {
      console.error('Failed to remove dependency:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTaskTitle = (taskId: string) => {
    const task = availableTasks.find(t => t.id === taskId);
    return task?.title || 'Unknown Task';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <Link className="w-5 h-5" />
          Dependencies
        </h3>
        {availableTasksForDependency.length > 0 && (
          <button
            type="button"
            onClick={() => setIsAddingDependency(true)}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            disabled={isLoading}
          >
            <Plus className="w-4 h-4" />
            Add Dependency
          </button>
        )}
      </div>

      {/* Current Dependencies */}
      {currentDependencies.length > 0 ? (
        <div className="space-y-2">
          {currentDependencies.map((dependency) => (
            <div
              key={dependency.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{getTaskTitle(dependency.predecessorTaskId)}</span>
                  <span className="text-gray-500">→</span>
                  <span className="text-sm text-gray-600">
                    {dependencyTypeLabels[dependency.dependencyType]}
                  </span>
                  {dependency.lagDays > 0 && (
                    <span className="flex items-center gap-1 text-sm text-gray-500">
                      <Clock className="w-3 h-3" />
                      +{dependency.lagDays} days
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {dependencyTypeDescriptions[dependency.dependencyType]}
                </p>
              </div>
              <button
                type="button"
                onClick={() => handleRemoveDependency(dependency.id)}
                className="p-1 hover:bg-red-50 rounded text-red-600 transition-colors"
                disabled={isLoading}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <Link className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>No dependencies set</p>
          <p className="text-sm">This task can start independently</p>
        </div>
      )}

      {/* Add Dependency Form */}
      {isAddingDependency && (
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h4 className="font-medium mb-3">Add New Dependency</h4>
          
          {validationErrors.length > 0 && (
            <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800 mb-1">
                <AlertTriangle className="w-4 h-4" />
                <span className="font-medium">Validation Errors</span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">Predecessor Task</label>
              <select
                value={selectedTaskId}
                onChange={(e) => setSelectedTaskId(e.target.value)}
                className="w-full border rounded-lg p-2 text-sm"
                disabled={isLoading}
              >
                <option value="">Select a task...</option>
                {availableTasksForDependency.map((task) => (
                  <option key={task.id} value={task.id}>
                    {task.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Dependency Type</label>
              <select
                value={dependencyType}
                onChange={(e) => setDependencyType(e.target.value as DependencyType)}
                className="w-full border rounded-lg p-2 text-sm"
                disabled={isLoading}
              >
                {Object.entries(dependencyTypeLabels).map(([type, label]) => (
                  <option key={type} value={type}>
                    {label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {dependencyTypeDescriptions[dependencyType]}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Lag Days</label>
              <input
                type="number"
                min="0"
                value={lagDays}
                onChange={(e) => setLagDays(parseInt(e.target.value) || 0)}
                className="w-full border rounded-lg p-2 text-sm"
                placeholder="0"
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 mt-1">
                Additional days to wait after the dependency condition is met
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <button
              type="button"
              onClick={() => {
                setIsAddingDependency(false);
                setValidationErrors([]);
                setSelectedTaskId('');
                setDependencyType('finish_to_start');
                setLagDays(0);
              }}
              className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddDependency}
              disabled={!selectedTaskId || isLoading}
              className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Adding...' : 'Add Dependency'}
            </button>
          </div>
        </div>
      )}

      {availableTasksForDependency.length === 0 && !isAddingDependency && (
        <div className="text-center py-4 text-gray-500 text-sm">
          No more tasks available to add as dependencies
        </div>
      )}
    </div>
  );
}
