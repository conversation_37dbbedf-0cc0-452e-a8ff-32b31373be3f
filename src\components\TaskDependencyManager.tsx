import React, { useState, useEffect } from 'react';
import { Task, TaskDependency, DateCalculationResult } from '../types';
import { Calendar, Clock, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { dependencyService } from '../services/dependencyService';
import { dateCalculationService } from '../services/dateCalculationService';
import DependencySelector from './DependencySelector';

interface TaskDependencyManagerProps {
  task: Task;
  onTaskUpdate?: (task: Task) => void;
  onDependencyFormStateChange?: (isOpen: boolean) => void;
}

export default function TaskDependencyManager({ task, onTaskUpdate, onDependencyFormStateChange }: TaskDependencyManagerProps) {
  const { tasks } = useSupabaseStore();
  const [dependencies, setDependencies] = useState<TaskDependency[]>([]);
  const [dependents, setDependents] = useState<TaskDependency[]>([]);
  const [dateCalculation, setDateCalculation] = useState<DateCalculationResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAddingDependency, setIsAddingDependency] = useState(false);

  useEffect(() => {
    loadDependencies();
  }, [task.id]);

  // Notify parent when dependency form state changes
  useEffect(() => {
    onDependencyFormStateChange?.(isAddingDependency);
  }, [isAddingDependency, onDependencyFormStateChange]);

  const loadDependencies = async () => {
    setIsLoading(true);
    try {
      // Load predecessors (dependencies)
      const predecessorsResult = await dependencyService.getTaskPredecessors(task.id);
      if (predecessorsResult.success) {
        setDependencies(predecessorsResult.data || []);
      }

      // Load dependents (successors)
      const dependentsResult = await dependencyService.getTaskDependents(task.id);
      if (dependentsResult.success) {
        setDependents(dependentsResult.data || []);
      }

      // Calculate dates based on dependencies
      const calculationResult = await dateCalculationService.calculateTaskDates(task.id);
      if (calculationResult.success) {
        setDateCalculation(calculationResult.data || null);
      }
    } catch (error) {
      console.error('Failed to load dependencies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDependenciesChange = async (newDependencies: TaskDependency[]) => {
    setDependencies(newDependencies);

    // Recalculate dates when dependencies change (without full reload)
    try {
      const calculationResult = await dateCalculationService.calculateTaskDates(task.id);
      if (calculationResult.success) {
        setDateCalculation(calculationResult.data || null);
      }
    } catch (error) {
      console.error('Failed to recalculate dates:', error);
    }
  };

  const getTaskTitle = (taskId: string) => {
    const foundTask = tasks.find(t => t.id === taskId);
    return foundTask?.title || 'Unknown Task';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  const getDependencyStatusIcon = () => {
    if (dependencies.length === 0) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    
    if (dateCalculation?.hasConflict) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    
    return <Info className="w-4 h-4 text-blue-500" />;
  };

  const getDependencyStatusText = () => {
    if (dependencies.length === 0) {
      return 'No dependencies - can start independently';
    }
    
    if (dateCalculation?.hasConflict) {
      return 'Date conflict detected - manual dates override calculated dates';
    }
    
    return `${dependencies.length} dependencies found`;
  };

  if (isLoading) {
    return (
      <div className="p-4 border rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Header */}
      <div 
        className="p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getDependencyStatusIcon()}
            <div>
              <h3 className="font-medium">Task Dependencies</h3>
              <p className="text-sm text-gray-600">{getDependencyStatusText()}</p>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            {isExpanded ? 'Click to collapse' : 'Click to expand'}
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Date Calculation Summary */}
          {dateCalculation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Calculated Dates
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 font-medium">Current Start Date:</span>
                  <div className="text-blue-900">{formatDate(dateCalculation.originalStartDate)}</div>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Current Due Date:</span>
                  <div className="text-blue-900">{formatDate(dateCalculation.originalDueDate)}</div>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Calculated Start Date:</span>
                  <div className="text-blue-900">{formatDate(dateCalculation.calculatedStartDate)}</div>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Calculated Due Date:</span>
                  <div className="text-blue-900">{formatDate(dateCalculation.calculatedDueDate)}</div>
                </div>
              </div>
              
              {dateCalculation.hasConflict && (
                <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                  <AlertTriangle className="w-4 h-4 inline mr-1" />
                  {dateCalculation.conflictReason}
                </div>
              )}
              
              {dateCalculation.dependencyChain.length > 0 && (
                <div className="mt-3">
                  <span className="text-blue-700 font-medium text-sm">Dependency Chain:</span>
                  <div className="text-blue-900 text-sm mt-1">
                    {dateCalculation.dependencyChain.join(' → ')} → {task.title}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Dependencies (Predecessors) */}
          <DependencySelector
            taskId={task.id}
            currentDependencies={dependencies}
            onDependenciesChange={handleDependenciesChange}
            availableTasks={tasks}
            isAddingDependency={isAddingDependency}
            setIsAddingDependency={setIsAddingDependency}
          />

          {/* Dependents (Successors) */}
          {dependents.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Tasks Depending on This Task
              </h4>
              <div className="space-y-2">
                {dependents.map((dependent) => (
                  <div
                    key={dependent.id}
                    className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg"
                  >
                    <div>
                      <div className="font-medium text-orange-900">
                        {getTaskTitle(dependent.successorTaskId)}
                      </div>
                      <div className="text-sm text-orange-700">
                        Dependency: {dependent.dependencyType.replace('_', ' ')}
                        {dependent.lagDays > 0 && ` (+${dependent.lagDays} days)`}
                      </div>
                    </div>
                    <div className="text-sm text-orange-600">
                      Will be affected by changes to this task
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Help Text */}
          <div className="bg-gray-50 border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">About Dependencies</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• <strong>Finish-to-Start (FS):</strong> This task starts after the predecessor finishes</p>
              <p>• <strong>Start-to-Start (SS):</strong> This task starts when the predecessor starts</p>
              <p>• <strong>Finish-to-Finish (FF):</strong> This task finishes when the predecessor finishes</p>
              <p>• <strong>Start-to-Finish (SF):</strong> This task finishes when the predecessor starts</p>
              <p className="mt-2 text-xs text-gray-500">
                When predecessor task dates change, this task's dates will be automatically recalculated based on the dependency type and lag days.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
