// Custom Fields Type Definitions

export type CustomFieldType = 'text' | 'number' | 'date' | 'dropdown';

export interface CustomField {
  id: string;
  name: string; // Unique identifier (used in code)
  label: string; // Human-readable label
  fieldType: CustomFieldType;
  isRequired: boolean;
  dropdownOptions?: string[]; // For dropdown type
  defaultValue?: string;
  description?: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CustomFieldValue {
  [fieldName: string]: string | number | null;
}

export interface CustomFieldDefinition {
  name: string;
  label: string;
  fieldType: CustomFieldType;
  isRequired?: boolean;
  dropdownOptions?: string[];
  defaultValue?: string;
  description?: string;
  sortOrder?: number;
}

// Form data for creating/editing custom fields
export interface CustomFieldFormData {
  name: string;
  label: string;
  fieldType: CustomFieldType;
  isRequired: boolean;
  dropdownOptions: string[];
  defaultValue: string;
  description: string;
}

// Validation result for custom field values
export interface CustomFieldValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Custom field input props for form components
export interface CustomFieldInputProps {
  field: CustomField;
  value: string | number | null;
  onChange: (value: string | number | null) => void;
  error?: string;
  disabled?: boolean;
}

// Store state for custom fields
export interface CustomFieldsState {
  customFields: CustomField[];
  loading: {
    customFields: boolean;
  };
}

// Service layer types for Supabase integration
export interface SupabaseCustomField {
  id: string;
  name: string;
  label: string;
  field_type: CustomFieldType;
  is_required: boolean;
  dropdown_options?: string[];
  default_value?: string;
  description?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface CreateCustomFieldRequest {
  name: string;
  label: string;
  field_type: CustomFieldType;
  is_required?: boolean;
  dropdown_options?: string[];
  default_value?: string;
  description?: string;
  sort_order?: number;
}

export interface UpdateCustomFieldRequest {
  label?: string;
  field_type?: CustomFieldType;
  is_required?: boolean;
  dropdown_options?: string[];
  default_value?: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
}

// Utility functions for custom field operations
export const transformSupabaseCustomField = (supabaseField: SupabaseCustomField): CustomField => ({
  id: supabaseField.id,
  name: supabaseField.name,
  label: supabaseField.label,
  fieldType: supabaseField.field_type,
  isRequired: supabaseField.is_required,
  dropdownOptions: supabaseField.dropdown_options,
  defaultValue: supabaseField.default_value,
  description: supabaseField.description,
  sortOrder: supabaseField.sort_order,
  isActive: supabaseField.is_active,
  createdAt: supabaseField.created_at,
  updatedAt: supabaseField.updated_at,
  createdBy: supabaseField.created_by,
});

export const validateCustomFieldValue = (field: CustomField, value: string | number | null): string | null => {
  // Required field validation
  if (field.isRequired && (value === null || value === '' || value === undefined)) {
    return `${field.label} is required`;
  }

  // Skip validation if field is empty and not required
  if (value === null || value === '' || value === undefined) {
    return null;
  }

  // Type-specific validation
  switch (field.fieldType) {
    case 'number':
      if (typeof value === 'string') {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          return `${field.label} must be a valid number`;
        }
      }
      break;

    case 'date':
      if (typeof value === 'string') {
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) {
          return `${field.label} must be a valid date`;
        }
      }
      break;

    case 'dropdown':
      if (field.dropdownOptions && !field.dropdownOptions.includes(String(value))) {
        return `${field.label} must be one of the predefined options`;
      }
      break;

    case 'text':
      // Text fields don't need additional validation beyond required check
      break;

    default:
      return `Unknown field type for ${field.label}`;
  }

  return null;
};

export const validateAllCustomFieldValues = (
  fields: CustomField[],
  values: CustomFieldValue
): CustomFieldValidationResult => {
  const errors: Record<string, string> = {};

  fields.forEach(field => {
    if (!field.isActive) return;

    const value = values[field.name];
    const error = validateCustomFieldValue(field, value);
    if (error) {
      errors[field.name] = error;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const getDefaultCustomFieldValues = (fields: CustomField[]): CustomFieldValue => {
  const defaultValues: CustomFieldValue = {};

  fields.forEach(field => {
    if (field.isActive && field.defaultValue) {
      switch (field.fieldType) {
        case 'number':
          defaultValues[field.name] = parseFloat(field.defaultValue);
          break;
        default:
          defaultValues[field.name] = field.defaultValue;
      }
    }
  });

  return defaultValues;
};
