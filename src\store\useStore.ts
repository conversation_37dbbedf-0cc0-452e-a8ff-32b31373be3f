import { create } from 'zustand';
import { Task, Project, UserGroup, KanbanColumn, Folder, TaskComment, TaskHistoryEntry, TaskDuration, User, Subtask, TaskFilter, SkillsetGroup, UserCapacity, TaskEffort } from '../types';

interface StoreState {
  tasks: Task[];
  projects: Project[];
  userGroups: UserGroup[];
  columns: KanbanColumn[];
  folders: Folder[];
  users: User[];
  skillsetGroups: SkillsetGroup[];
  userCapacities: UserCapacity[];
  taskEfforts: TaskEffort[];
  
  addTask: (task: Omit<Task, 'id'> & { id?: string; comments?: TaskComment[]; history?: TaskHistoryEntry[]; durations?: TaskDuration[] }) => void;
  updateTask: (id: string, task: Partial<Task>) => void;
  moveTask: (taskId: string, newStatus: Task['status']) => void;
  deleteTask: (id: string) => void;
  
  addComment: (taskId: string, comment: Omit<TaskComment, 'id' | 'timestamp'>) => void;
  updateComment: (taskId: string, commentId: string, content: string) => void;
  deleteComment: (taskId: string, commentId: string) => void;
  
  addSubtaskComment: (taskId: string, subtaskId: string, comment: Omit<TaskComment, 'id' | 'timestamp'>) => void;
  updateSubtaskComment: (taskId: string, subtaskId: string, commentId: string, content: string) => void;
  deleteSubtaskComment: (taskId: string, subtaskId: string, commentId: string) => void;
  
  addHistoryEntry: (taskId: string, entry: Omit<TaskHistoryEntry, 'id' | 'timestamp'>) => void;
  
  addUserGroup: (group: Omit<UserGroup, 'id'> & { id?: string }) => void;
  updateUserGroup: (id: string, group: Partial<UserGroup>) => void;
  deleteUserGroup: (id: string) => void;
  
  addProject: (project: Omit<Project, 'id' | 'tasks'> & { id?: string }) => void;
  updateProject: (id: string, project: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  
  addColumn: (column: Omit<KanbanColumn, 'id'> & { id?: string }) => void;
  updateColumn: (id: string, column: Partial<KanbanColumn>) => void;
  deleteColumn: (id: string) => void;
  reorderColumns: (columns: KanbanColumn[]) => void;
  
  addFolder: (folder: Omit<Folder, 'id'> & { id?: string }) => void;
  updateFolder: (id: string, folder: Partial<Folder>) => void;
  deleteFolder: (id: string) => void;

  addUser: (user: Omit<User, 'id'> & { id?: string }) => void;
  updateUser: (id: string, user: Partial<User>) => void;
  deleteUser: (id: string) => void;

  addSkillsetGroup: (skillset: Omit<SkillsetGroup, 'id' | 'createdAt' | 'updatedAt'> & { id?: string }) => void;
  updateSkillsetGroup: (id: string, skillset: Partial<SkillsetGroup>) => void;
  deleteSkillsetGroup: (id: string) => void;

  addUserCapacity: (capacity: UserCapacity) => void;
  updateUserCapacity: (userId: string, capacity: Partial<UserCapacity>) => void;
  deleteUserCapacity: (userId: string) => void;

  addTaskEffort: (effort: TaskEffort) => void;
  updateTaskEffort: (taskId: string, effort: Partial<TaskEffort>) => void;
  deleteTaskEffort: (taskId: string) => void;

  addSubtask: (taskId: string, subtask: Omit<Subtask, 'id'>) => void;
  updateSubtask: (taskId: string, subtaskId: string, updates: Partial<Subtask>) => void;
  deleteSubtask: (taskId: string, subtaskId: string) => void;

  resetTasks: () => void;
  resetProjects: () => void;
  resetUserGroups: () => void;
  resetColumns: () => void;
  resetFolders: () => void;
  resetUsers: () => void;
  resetAll: () => void;

  tasksViewMode: 'kanban' | 'list';
  selectedTreeNode: string | null;
  expandedTreeNodes: Set<string>;
  taskFilters: TaskFilter;

  setTasksViewMode: (mode: 'kanban' | 'list') => void;
  setSelectedTreeNode: (nodeId: string | null) => void;
  toggleTreeNode: (nodeId: string) => void;
  setTaskFilters: (filters: Partial<TaskFilter>) => void;
  clearTaskFilters: () => void;
  toggleFilterValue: (filterKey: keyof TaskFilter, value: string) => void;
}



export const useStore = create<StoreState>((set, get) => ({
  tasks: [],
  projects: [],
  userGroups: [
    { id: 'devs', name: 'Developers', color: 'bg-blue-500' },
    { id: 'pm', name: 'Project Managers', color: 'bg-green-500' },
    { id: 'cm', name: 'Campaign Managers', color: 'bg-purple-500' },
    { id: 'leaders', name: 'Team Leaders', color: 'bg-red-500' },
  ],
  columns: [
    { id: 'todo', title: 'To Do', color: 'bg-gray-100' },
    { id: 'in-progress', title: 'In Progress', color: 'bg-blue-50' },
    { id: 'review', title: 'Review', color: 'bg-yellow-50' },
    { id: 'done', title: 'Done', color: 'bg-green-50' },
  ],
  folders: [],
  users: [],
  skillsetGroups: [],
  userCapacities: [],
  taskEfforts: [],
  tasksViewMode: 'kanban',
  selectedTreeNode: null,
  expandedTreeNodes: new Set(),
  taskFilters: {
    assignedUsers: [],
    status: [],
    priority: [],
    owners: [],
    assignedGroups: [],
    tags: [],
    dueDateRange: {},
    hasOverdueTasks: false,
  },

  addTask: (task) => set((state) => ({
    tasks: [...state.tasks, {
      ...task,
      id: task.id || Math.random().toString(36).substr(2, 9),
      comments: task.comments || [],
      history: task.history || [],
      durations: task.durations || [{
        status: task.status || 'todo',
        startTime: new Date().toISOString()
      }],
      subtasks: task.subtasks || []
    }],
  })),

  updateTask: (id, updatedTask) => set((state) => {
    const task = state.tasks.find(t => t.id === id);
    if (!task) return state;

    const historyEntries: TaskHistoryEntry[] = [];
    Object.entries(updatedTask).forEach(([field, newValue]) => {
      const oldValue = task[field as keyof Task];
      if (newValue !== oldValue) {
        historyEntries.push({
          id: Math.random().toString(36).substr(2, 9),
          taskId: id,
          timestamp: new Date().toISOString(),
          field,
          oldValue: JSON.stringify(oldValue),
          newValue: JSON.stringify(newValue),
          userId: 'system'
        });
      }
    });

    return {
      tasks: state.tasks.map((task) =>
        task.id === id ? { 
          ...task, 
          ...updatedTask,
          history: [...task.history, ...historyEntries]
        } : task
      ),
    };
  }),

  moveTask: (taskId, newStatus) => set((state) => {
    const task = state.tasks.find(t => t.id === taskId);
    if (!task) return state;

    const now = new Date().toISOString();

    const updatedDurations = task.durations.map(duration => 
      !duration.endTime ? { ...duration, endTime: now } : duration
    );

    updatedDurations.push({
      status: newStatus,
      startTime: now
    });

    const { columns } = get();
    const oldColumn = columns.find(c => c.id === task.status);
    const newColumn = columns.find(c => c.id === newStatus);

    const historyEntry: TaskHistoryEntry = {
      id: Math.random().toString(36).substr(2, 9),
      taskId,
      timestamp: now,
      field: 'status',
      oldValue: oldColumn?.title || task.status,
      newValue: newColumn?.title || newStatus,
      userId: 'system'
    };

    return {
      tasks: state.tasks.map((task) =>
        task.id === taskId ? { 
          ...task, 
          status: newStatus,
          durations: updatedDurations,
          history: [...task.history, historyEntry]
        } : task
      ),
    };
  }),

  deleteTask: (id) => set((state) => ({
    tasks: state.tasks.filter((task) => task.id !== id),
  })),

  addComment: (taskId, comment) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        comments: [...task.comments, {
          ...comment,
          id: Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          taskId
        }]
      } : task
    ),
  })),

  updateComment: (taskId, commentId, content) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        comments: task.comments.map((comment) =>
          comment.id === commentId ? {
            ...comment,
            content,
            edited: true
          } : comment
        )
      } : task
    ),
  })),

  deleteComment: (taskId, commentId) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        comments: task.comments.filter((comment) => comment.id !== commentId)
      } : task
    ),
  })),

  addSubtaskComment: (taskId: string, subtaskId: string, comment: Omit<TaskComment, 'id' | 'timestamp'>) => set((state) => {
    console.log("Adding subtask comment:", { taskId, subtaskId, comment });
    
    const taskIndex = state.tasks.findIndex((task) => task.id === taskId);
    if (taskIndex === -1) return state;
  
    const subtaskIndex = state.tasks[taskIndex].subtasks.findIndex((subtask) => subtask.id === subtaskId);
    if (subtaskIndex === -1) return state;
  
    const currentSubtask = state.tasks[taskIndex].subtasks[subtaskIndex];
    const currentComments = currentSubtask.comments || [];
  
    const newComment = {
      ...comment,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString()
    };
  
    const updatedState = {
      tasks: state.tasks.map((task, idx) =>
        idx === taskIndex ? {
          ...task,
          subtasks: task.subtasks.map((subtask, subIdx) =>
            subIdx === subtaskIndex ? {
              ...subtask,
              comments: [...currentComments, newComment]
            } : subtask
          )
        } : task
      ),
    };
  
    console.log("State after adding subtask comment:", updatedState);
    return updatedState;
  }),

  updateSubtaskComment: (taskId, subtaskId, commentId, content) => set((state) => {
    const taskIndex = state.tasks.findIndex((task) => task.id === taskId);
    console.log("Updating Comment for Task ID:", taskId, "Subtask ID:", subtaskId, "Comment ID:", commentId);
    if (taskIndex !== -1) {
      const subtaskIndex = state.tasks[taskIndex].subtasks.findIndex((subtask) => subtask.id === subtaskId);
      if (subtaskIndex !== -1) {
        const newComments = [...(state.tasks[taskIndex].subtasks[subtaskIndex].comments || [])];
        const commentIndex = newComments.findIndex((comment) => comment.id === commentId);
        if (commentIndex !== -1) {
          newComments[commentIndex] = { ...newComments[commentIndex], content, edited: true };
          console.log("Comment Updated:", newComments[commentIndex]);
        } else {
          newComments.push({ id: commentId, content, userId: 'current-user', timestamp: new Date().toISOString(), taskId: subtaskId });
          console.log("New Comment Added:", newComments);
        }
        return {
          tasks: state.tasks.map((task, index) =>
            index === taskIndex ? {
              ...task,
              subtasks: task.subtasks.map((subtask, subtaskIndex) =>
                subtaskIndex === subtaskIndex ? {
                  ...subtask,
                  comments: newComments
                } : subtask
              )
            } : task
          ),
        };
      }
    }
    return state;
  }),

  deleteSubtaskComment: (taskId, subtaskId, commentId) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        subtasks: task.subtasks.map((subtask) =>
          subtask.id === subtaskId ? {
            ...subtask,
            comments: (subtask.comments || []).filter((comment) => comment.id !== commentId)
          } : subtask
        )
      } : task
    ),
  })),

  addHistoryEntry: (taskId, entry) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        history: [...task.history, {
          ...entry,
          id: Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          taskId
        }]
      } : task
    ),
  })),

  addUserGroup: (group) => set((state) => ({
    userGroups: [...state.userGroups, { ...group, id: group.id || Math.random().toString(36).substr(2, 9) }],
  })),

  updateUserGroup: (id, updatedGroup) => set((state) => ({
    userGroups: state.userGroups.map((group) =>
      group.id === id ? { ...group, ...updatedGroup } : group
    ),
  })),

  deleteUserGroup: (id) => set((state) => ({
    userGroups: state.userGroups.filter((group) => group.id !== id),
  })),

  addProject: (project) => set((state) => ({
    projects: [...state.projects, { ...project, id: project.id || Math.random().toString(36).substr(2, 9), tasks: [] }],
  })),

  updateProject: (id, updatedProject) => set((state) => ({
    projects: state.projects.map((project) =>
      project.id === id ? { ...project, ...updatedProject } : project
    ),
  })),

  deleteProject: (id) => set((state) => ({
    projects: state.projects.filter((project) => project.id !== id),
    tasks: state.tasks.filter((task) => task.projectId !== id),
  })),

  addColumn: (column) => set((state) => ({
    columns: [...state.columns, { ...column, id: column.id || Math.random().toString(36).substr(2, 9) }],
  })),

  updateColumn: (id, updatedColumn) => set((state) => ({
    columns: state.columns.map((column) =>
      column.id === id ? { ...column, ...updatedColumn } : column
    ),
  })),

  deleteColumn: (id) => set((state) => ({
    columns: state.columns.filter((column) => column.id !== id),
    tasks: state.tasks.map((task) => 
      task.status === id ? { ...task, status: 'todo' } : task
    ),
  })),

  reorderColumns: (newColumns) => set(() => ({
    columns: newColumns,
  })),

  addFolder: (folder) => set((state) => ({
    folders: [...state.folders, { ...folder, id: folder.id || Math.random().toString(36).substr(2, 9) }],
  })),

  updateFolder: (id, updatedFolder) => set((state) => ({
    folders: state.folders.map((folder) =>
      folder.id === id ? { ...folder, ...updatedFolder } : folder
    ),
  })),

  deleteFolder: (id) => set((state) => {
    const folderIds = new Set<string>();
    const getFolderIds = (folderId: string) => {
      folderIds.add(folderId);
      state.folders
        .filter(f => f.parentId === folderId)
        .forEach(f => getFolderIds(f.id));
    };
    getFolderIds(id);

    return {
      folders: state.folders.filter(folder => !folderIds.has(folder.id)),
      projects: state.projects.map(project => 
        folderIds.has(project.folderId || '') 
          ? { ...project, folderId: undefined }
          : project
      ),
      tasks: state.tasks.map(task =>
        folderIds.has(task.folderId || '')
          ? { ...task, folderId: undefined }
          : task
      ),
    };
  }),

  addUser: (user) => {
    console.log('Adding user:', user);
    return set((state) => {
      const newUser = {
        ...user,
        id: user.id || Math.random().toString(36).substr(2, 9),
        skillsetIds: user.skillsetIds || [],
        capacity: user.capacity || undefined
      };
      const newUsers = [...state.users, newUser];
      console.log('New users array:', newUsers);
      return { users: newUsers };
    });
  },

  updateUser: (id, updatedUser) => set((state) => ({
    users: state.users.map((user) =>
      user.id === id ? { ...user, ...updatedUser } : user
    ),
  })),

  deleteUser: (id) => set((state) => ({
    users: state.users.filter((user) => user.id !== id),
    tasks: state.tasks.map(task => ({
      ...task,
      assignedUsers: task.assignedUsers?.filter(userId => userId !== id) || [],
      ownerId: task.ownerId === id ? undefined : task.ownerId,
      subtasks: task.subtasks.map(subtask => ({
        ...subtask,
        assignedUserId: subtask.assignedUserId === id ? undefined : subtask.assignedUserId
      }))
    }))
  })),

  addSubtask: (taskId, subtask) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId && task.subtasks.length < 20 ? {
        ...task,
        subtasks: [...task.subtasks, {
          ...subtask,
          id: Math.random().toString(36).substr(2, 9),
          comments: [],
          history: [{
            id: Math.random().toString(36).substr(2, 9),
            taskId,
            timestamp: new Date().toISOString(),
            field: 'created',
            oldValue: '',
            newValue: 'Subtask created',
            userId: 'system'
          }],
          durations: [{
            status: subtask.status,
            startTime: new Date().toISOString()
          }]
        }]
      } : task
    ),
  })),

  updateSubtask: (taskId, subtaskId, updates) => set((state) => {
    console.log("Updating subtask:", { taskId, subtaskId, updates });
    
    const task = state.tasks.find(t => t.id === taskId);
    if (!task) return state;
  
    const subtask = task.subtasks.find(s => s.id === subtaskId);
    if (!subtask) return state;
  
    const updatedState = {
      tasks: state.tasks.map((task) =>
        task.id === taskId ? {
          ...task,
          subtasks: task.subtasks.map((subtask) =>
            subtask.id === subtaskId ? {
              ...subtask,
              ...updates,
              comments: subtask.comments || [], // Preserve existing comments
            } : subtask
          )
        } : task
      ),
    };
  
    console.log("State after updating subtask:", updatedState);
    return updatedState;
  }),

  deleteSubtask: (taskId, subtaskId) => set((state) => ({
    tasks: state.tasks.map((task) =>
      task.id === taskId ? {
        ...task,
        subtasks: task.subtasks.filter((subtask) => subtask.id !== subtaskId)
      } : task
    ),
  })),

  resetTasks: () => set(() => ({ tasks: [] })),
  resetProjects: () => set(() => ({ projects: [] })),
  resetUserGroups: () => set(() => ({ userGroups: [] })),
  resetColumns: () => set(() => ({ columns: [] })),
  resetFolders: () => set(() => ({ folders: [] })),
  resetUsers: () => set(() => ({ users: [] })),
  resetAll: () => set(() => ({
    tasks: [],
    projects: [],
    userGroups: [],
    columns: [],
    folders: [],
    users: [],
  })),
  setTasksViewMode: (mode) => set({ tasksViewMode: mode }),
  setSelectedTreeNode: (nodeId) => set({ selectedTreeNode: nodeId }),
  toggleTreeNode: (nodeId) => set((state) => {
    const newExpanded = new Set(state.expandedTreeNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    return { expandedTreeNodes: newExpanded };
  }),

  setTaskFilters: (filters) => set((state) => ({
    taskFilters: { ...state.taskFilters, ...filters }
  })),

  clearTaskFilters: () => set({
    taskFilters: {
      assignedUsers: [],
      status: [],
      priority: [],
      owners: [],
      assignedGroups: [],
      tags: [],
      dueDateRange: {},
      hasOverdueTasks: false,
    }
  }),

  toggleFilterValue: (filterKey, value) => set((state) => {
    const currentValues = state.taskFilters[filterKey];
    if (Array.isArray(currentValues)) {
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return {
        taskFilters: {
          ...state.taskFilters,
          [filterKey]: newValues
        }
      };
    }
    return state;
  }),

  // Skillset Group Management
  addSkillsetGroup: (skillset) => set((state) => ({
    skillsetGroups: [...state.skillsetGroups, {
      ...skillset,
      id: skillset.id || Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }]
  })),

  updateSkillsetGroup: (id, updatedSkillset) => set((state) => ({
    skillsetGroups: state.skillsetGroups.map((skillset) =>
      skillset.id === id ? {
        ...skillset,
        ...updatedSkillset,
        updatedAt: new Date().toISOString()
      } : skillset
    )
  })),

  deleteSkillsetGroup: (id) => set((state) => ({
    skillsetGroups: state.skillsetGroups.filter((skillset) => skillset.id !== id),
    users: state.users.map(user => ({
      ...user,
      skillsetIds: user.skillsetIds.filter(skillsetId => skillsetId !== id)
    })),
    userCapacities: state.userCapacities,
    taskEfforts: state.taskEfforts
  })),

  // User Capacity Management
  addUserCapacity: (capacity) => set((state) => ({
    userCapacities: [...state.userCapacities.filter(c => c.userId !== capacity.userId), capacity]
  })),

  updateUserCapacity: (userId, updatedCapacity) => set((state) => ({
    userCapacities: state.userCapacities.map((capacity) =>
      capacity.userId === userId ? { ...capacity, ...updatedCapacity } : capacity
    )
  })),

  deleteUserCapacity: (userId) => set((state) => ({
    userCapacities: state.userCapacities.filter((capacity) => capacity.userId !== userId)
  })),

  // Task Effort Management
  addTaskEffort: (effort) => set((state) => ({
    taskEfforts: [...state.taskEfforts.filter(e => e.taskId !== effort.taskId), effort]
  })),

  updateTaskEffort: (taskId, updatedEffort) => set((state) => ({
    taskEfforts: state.taskEfforts.map((effort) =>
      effort.taskId === taskId ? { ...effort, ...updatedEffort } : effort
    )
  })),

  deleteTaskEffort: (taskId) => set((state) => ({
    taskEfforts: state.taskEfforts.filter((effort) => effort.taskId !== taskId)
  })),
}));






