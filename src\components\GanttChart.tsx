import React, { useState, useEffect, useMemo } from 'react';
import { Task, TaskDependency } from '../types';
import { Calendar, Filter, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { dependencyService } from '../services/dependencyService';
import { 
  parseISO, 
  format, 
  addDays, 
  differenceInDays, 
  isValid, 
  startOfWeek, 
  endOfWeek,
  eachDayOfInterval,
  isWeekend
} from 'date-fns';

interface GanttChartProps {
  tasks: Task[];
  onTaskUpdate?: (task: Task) => void;
}

interface GanttTask {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  progress: number;
  dependencies: TaskDependency[];
  isOnCriticalPath: boolean;
  color: string;
  projectId?: string;
}

export default function GanttChart({ tasks, onTaskUpdate }: GanttChartProps) {
  const { projects } = useSupabaseStore();
  const [dependencies, setDependencies] = useState<TaskDependency[]>([]);
  const [viewMode, setViewMode] = useState<'days' | 'weeks' | 'months'>('days');
  const [showDependencies, setShowDependencies] = useState(true);
  const [showCriticalPath, setShowCriticalPath] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDependencies();
  }, [tasks]);

  const loadDependencies = async () => {
    setIsLoading(true);
    try {
      const taskIds = tasks.map(t => t.id);
      console.log('🔍 GanttChart: Loading dependencies for tasks:', taskIds);

      if (taskIds.length > 0) {
        const result = await dependencyService.getBatchDependencies(taskIds);
        console.log('🔍 GanttChart: Dependency service result:', result);

        if (result.success) {
          setDependencies(result.data || []);
          console.log('🔍 GanttChart: Dependencies loaded:', result.data?.length || 0);
        } else {
          console.error('🔍 GanttChart: Failed to load dependencies:', result.error);
        }
      }
    } catch (error) {
      console.error('Failed to load dependencies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Transform tasks to Gantt format
  const ganttTasks: GanttTask[] = useMemo(() => {
    console.log('🔍 GanttChart: Transforming tasks to Gantt format');
    console.log('🔍 GanttChart: Input tasks:', tasks.map(t => ({
      id: t.id,
      title: t.title,
      startDate: t.startDate,
      dueDate: t.dueDate
    })));

    const filteredTasks = tasks.filter(task => task.startDate && task.dueDate);
    console.log('🔍 GanttChart: Tasks with dates:', filteredTasks.length);

    const tasksWithoutDates = tasks.filter(task => !task.startDate || !task.dueDate);
    console.log('🔍 GanttChart: Tasks WITHOUT dates:', tasksWithoutDates.map(t => ({
      id: t.id,
      title: t.title,
      startDate: t.startDate,
      dueDate: t.dueDate
    })));

    // Check specifically for the missing predecessor task
    const missingPredecessor = tasks.find(t => t.id === 'a7955fba-dcd8-4b6f-b706-87de6ffb14d4');
    console.log('🔍 GanttChart: Missing predecessor task details:', missingPredecessor);

    return filteredTasks
      .map(task => {
        const startDate = parseISO(task.startDate!);
        const endDate = parseISO(task.dueDate!);

        if (!isValid(startDate) || !isValid(endDate)) {
          console.log('🔍 GanttChart: Invalid dates for task:', task.title, { startDate: task.startDate, dueDate: task.dueDate });
          return null;
        }

        const project = projects.find(p => p.id === task.projectId);
        const taskDependencies = dependencies.filter(
          dep => dep.successorTaskId === task.id || dep.predecessorTaskId === task.id
        );

        return {
          id: task.id,
          title: task.title,
          startDate,
          endDate,
          progress: task.status === 'done' ? 100 : task.status === 'in-progress' ? 50 : 0,
          dependencies: taskDependencies,
          isOnCriticalPath: false, // TODO: Calculate critical path
          color: project?.color || 'bg-blue-500',
          projectId: task.projectId
        };
      })
      .filter(Boolean) as GanttTask[];
  }, [tasks, projects, dependencies]);

  // Calculate date range
  const dateRange = useMemo(() => {
    if (ganttTasks.length === 0) {
      const today = new Date();
      return {
        start: today,
        end: addDays(today, 30),
        days: eachDayOfInterval({ start: today, end: addDays(today, 30) })
      };
    }

    const startDates = ganttTasks.map(task => task.startDate);
    const endDates = ganttTasks.map(task => task.endDate);
    
    const earliestStart = new Date(Math.min(...startDates.map(d => d.getTime())));
    const latestEnd = new Date(Math.max(...endDates.map(d => d.getTime())));
    
    // Add some padding
    const start = addDays(earliestStart, -7);
    const end = addDays(latestEnd, 7);
    
    return {
      start,
      end,
      days: eachDayOfInterval({ start, end })
    };
  }, [ganttTasks]);

  // Calculate task position and width
  const getTaskStyle = (task: GanttTask) => {
    const totalDays = differenceInDays(dateRange.end, dateRange.start);
    const taskStart = differenceInDays(task.startDate, dateRange.start);
    const taskDuration = differenceInDays(task.endDate, task.startDate) + 1;
    
    const left = (taskStart / totalDays) * 100;
    const width = (taskDuration / totalDays) * 100;
    
    return {
      left: `${Math.max(0, left)}%`,
      width: `${Math.max(1, width)}%`
    };
  };

  // Get dependency lines
  const getDependencyLines = () => {
    console.log('🔍 GanttChart: getDependencyLines called');
    console.log('🔍 GanttChart: showDependencies:', showDependencies);
    console.log('🔍 GanttChart: dependencies:', dependencies);
    console.log('🔍 GanttChart: ganttTasks:', ganttTasks.map(t => ({ id: t.id, title: t.title })));

    if (!showDependencies) return [];

    const lines = dependencies
      .filter(dep => {
        const predecessor = ganttTasks.find(t => t.id === dep.predecessorTaskId);
        const successor = ganttTasks.find(t => t.id === dep.successorTaskId);
        const hasMatch = predecessor && successor;

        console.log('🔍 GanttChart: Dependency filter check:', {
          depId: dep.id,
          predecessorId: dep.predecessorTaskId,
          successorId: dep.successorTaskId,
          foundPredecessor: !!predecessor,
          foundSuccessor: !!successor,
          hasMatch
        });

        // Log missing tasks for debugging
        if (!predecessor) {
          const missingTask = tasks.find(t => t.id === dep.predecessorTaskId);
          console.log('🔍 GanttChart: Missing predecessor task:', {
            id: dep.predecessorTaskId,
            title: missingTask?.title,
            startDate: missingTask?.startDate,
            dueDate: missingTask?.dueDate,
            reason: !missingTask?.startDate || !missingTask?.dueDate ? 'Missing dates' : 'Invalid dates'
          });
        }

        if (!successor) {
          const missingTask = tasks.find(t => t.id === dep.successorTaskId);
          console.log('🔍 GanttChart: Missing successor task:', {
            id: dep.successorTaskId,
            title: missingTask?.title,
            startDate: missingTask?.startDate,
            dueDate: missingTask?.dueDate,
            reason: !missingTask?.startDate || !missingTask?.dueDate ? 'Missing dates' : 'Invalid dates'
          });
        }

        return hasMatch;
      })
      .map(dep => {
        const predecessor = ganttTasks.find(t => t.id === dep.predecessorTaskId)!;
        const successor = ganttTasks.find(t => t.id === dep.successorTaskId)!;

        return {
          id: dep.id,
          from: predecessor,
          to: successor,
          type: dep.dependencyType
        };
      });

    console.log('🔍 GanttChart: Final dependency lines:', lines);
    return lines;
  };

  const formatDateHeader = (date: Date) => {
    switch (viewMode) {
      case 'days':
        return format(date, 'MMM d');
      case 'weeks':
        return format(startOfWeek(date), 'MMM d');
      case 'months':
        return format(date, 'MMM yyyy');
      default:
        return format(date, 'MMM d');
    }
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="max-w-full mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <Calendar className="w-6 h-6 text-blue-600" />
            <h2 className="text-2xl font-bold">Gantt Chart</h2>
          </div>
          
          <div className="flex items-center gap-4">
            {/* View Mode */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">View:</span>
              <select
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value as any)}
                className="border rounded-lg px-3 py-1 text-sm"
              >
                <option value="days">Days</option>
                <option value="weeks">Weeks</option>
                <option value="months">Months</option>
              </select>
            </div>

            {/* Options */}
            <div className="flex items-center gap-3">
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={showDependencies}
                  onChange={(e) => setShowDependencies(e.target.checked)}
                  className="rounded"
                />
                Dependencies
              </label>
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={showCriticalPath}
                  onChange={(e) => setShowCriticalPath(e.target.checked)}
                  className="rounded"
                />
                Critical Path
              </label>
            </div>
          </div>
        </div>

        {/* Gantt Chart */}
        <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <div className="min-w-[1200px]">
              {/* Timeline Header */}
              <div className="sticky top-0 bg-white z-10 border-b">
                <div className="flex">
                  <div className="w-80 p-4 font-semibold border-r bg-gray-50">Task</div>
                  <div className="flex-1 flex gantt-timeline">
                    {dateRange.days.map((date, index) => (
                      <div
                        key={index}
                        className={`flex-1 p-2 text-center text-xs border-r ${
                          format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
                            ? 'bg-blue-50 text-blue-700'
                            : isWeekend(date)
                            ? 'bg-gray-50 text-gray-500'
                            : 'text-gray-700'
                        }`}
                        style={{ minWidth: '40px' }}
                      >
                        <div className="font-medium">{formatDateHeader(date)}</div>
                        <div className="text-xs opacity-75">{format(date, 'EEE')}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Task Rows */}
              <div className="relative">
                {ganttTasks.map((task, index) => (
                  <div key={task.id} className="flex items-center hover:bg-gray-50 border-b">
                    {/* Task Info */}
                    <div className="w-80 p-4 border-r bg-white sticky left-0 z-10">
                      <div className="font-medium text-sm">{task.title}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {format(task.startDate, 'MMM d')} - {format(task.endDate, 'MMM d')}
                        ({differenceInDays(task.endDate, task.startDate) + 1} days)
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        Progress: {task.progress}%
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="flex-1 relative h-16 flex items-center">
                      {/* Task Bar */}
                      <div
                        className={`absolute h-6 rounded ${task.color} bg-opacity-80 border-l-4 ${task.color.replace('bg-', 'border-')} ${
                          task.isOnCriticalPath && showCriticalPath ? 'ring-2 ring-red-500' : ''
                        }`}
                        style={getTaskStyle(task)}
                      >
                        <div className="px-2 py-1 text-xs text-white font-medium truncate">
                          {task.title}
                        </div>
                        {/* Progress Bar */}
                        {task.progress > 0 && (
                          <div
                            className="absolute top-0 left-0 h-full bg-green-500 bg-opacity-50 rounded"
                            style={{ width: `${task.progress}%` }}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Dependency Lines */}
                {showDependencies && (
                  <svg
                    className="absolute inset-0 pointer-events-none"
                    style={{ zIndex: 10 }}
                    width="100%"
                    height="100%"
                  >
                    {getDependencyLines().map((line) => {
                      // Calculate line positions
                      const fromIndex = ganttTasks.findIndex(t => t.id === line.from.id);
                      const toIndex = ganttTasks.findIndex(t => t.id === line.to.id);

                      if (fromIndex === -1 || toIndex === -1) {
                        console.log('🔍 GanttChart: Skipping line - task not found:', {
                          lineId: line.id,
                          fromId: line.from.id,
                          toId: line.to.id,
                          fromIndex,
                          toIndex
                        });
                        return null;
                      }

                      // Calculate Y positions (row centers)
                      const rowHeight = 64;
                      const headerHeight = 40;
                      const fromY = headerHeight + (fromIndex * rowHeight) + (rowHeight / 2);
                      const toY = headerHeight + (toIndex * rowHeight) + (rowHeight / 2);

                      // Calculate X positions based on task dates
                      const fromTask = line.from;
                      const toTask = line.to;

                      // Get task end position for predecessor and start position for successor
                      const fromEndDate = new Date(fromTask.dueDate);
                      const toStartDate = new Date(toTask.startDate);

                      // Calculate positions based on actual date positioning logic
                      const totalDays = differenceInDays(dateRange.end, dateRange.start);

                      // For predecessor task: find the END date position
                      const fromEndDays = differenceInDays(line.from.endDate, dateRange.start);
                      const fromX = 320 + (fromEndDays / totalDays) * (window.innerWidth - 400); // 320px = task name width, 400px = total margins

                      // For successor task: find the START date position
                      const toStartDays = differenceInDays(line.to.startDate, dateRange.start);
                      const toX = 320 + (toStartDays / totalDays) * (window.innerWidth - 400);



                      // Create connector path
                      const gap = 20; // Gap between tasks
                      const midX = fromX + gap;

                      return (
                        <g key={line.id}>
                          {/* Simple L-shaped connector for testing */}
                          {/* Horizontal line from predecessor */}
                          <line
                            x1={fromX}
                            y1={fromY}
                            x2={midX}
                            y2={fromY}
                            stroke="#3b82f6"
                            strokeWidth="2"
                          />
                          {/* Vertical connecting line */}
                          <line
                            x1={midX}
                            y1={fromY}
                            x2={midX}
                            y2={toY}
                            stroke="#3b82f6"
                            strokeWidth="2"
                          />
                          {/* Horizontal line to successor */}
                          <line
                            x1={midX}
                            y1={toY}
                            x2={toX}
                            y2={toY}
                            stroke="#3b82f6"
                            strokeWidth="2"
                            markerEnd="url(#arrowhead)"
                          />
                          {/* Start point indicator */}
                          <circle
                            cx={fromX}
                            cy={fromY}
                            r="3"
                            fill="#3b82f6"
                          />
                          {/* End point indicator */}
                          <circle
                            cx={toX}
                            cy={toY}
                            r="3"
                            fill="#3b82f6"
                          />

                        </g>
                      );
                    })}
                    
                    {/* Arrow marker */}
                    <defs>
                      <marker
                        id="arrowhead"
                        markerWidth="12"
                        markerHeight="8"
                        refX="11"
                        refY="4"
                        orient="auto"
                        markerUnits="strokeWidth"
                      >
                        <polygon
                          points="0 0, 12 4, 0 8"
                          fill="#ef4444"
                          fill="#3b82f6"
                        />
                      </marker>
                    </defs>
                  </svg>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="mt-4 flex items-center gap-6 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded"></div>
            <span>Task</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-500 bg-opacity-50 rounded"></div>
            <span>Progress</span>
          </div>
          {showDependencies && (
            <div className="flex items-center gap-2">
              <div className="w-4 h-0.5 bg-blue-500" style={{ borderTop: '2px dashed #3b82f6' }}></div>
              <span>Dependencies</span>
            </div>
          )}
          {showCriticalPath && (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded ring-2 ring-red-500"></div>
              <span>Critical Path</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
