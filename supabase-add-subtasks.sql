-- Add subtasks support to tasks table
-- Run this to add subtasks functionality

-- Add subtasks column to tasks table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tasks' 
        AND column_name = 'subtasks'
    ) THEN
        ALTER TABLE tasks ADD COLUMN subtasks JSONB DEFAULT '[]'::jsonb;
    END IF;
END $$;

-- Create index for subtasks JSONB queries
CREATE INDEX IF NOT EXISTS idx_tasks_subtasks ON tasks USING GIN (subtasks);

-- Update existing tasks to have empty subtasks array if null
UPDATE tasks SET subtasks = '[]'::jsonb WHERE subtasks IS NULL;

SELECT 'Subtasks column added successfully!' as message;
