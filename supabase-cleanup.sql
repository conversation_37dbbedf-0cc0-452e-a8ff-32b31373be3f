-- Complete cleanup script
-- Run this first to clean up any existing schema elements

-- Drop all triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
DROP TRIGGER IF EXISTS update_user_groups_updated_at ON user_groups;
DROP TRIGGER IF EXISTS update_folders_updated_at ON folders;
DROP TRIGGER IF EXISTS update_projects_updated_at ON projects;
DROP TRIGGER IF EXISTS update_kanban_columns_updated_at ON kanban_columns;
DROP TRIGGER IF EXISTS update_skillset_groups_updated_at ON skillset_groups;
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
DROP TRIGGER IF EXISTS update_task_comments_updated_at ON task_comments;
DROP TRIGGER IF EXISTS update_user_capacities_updated_at ON user_capacities;
DROP TRIGGER IF EXISTS update_task_efforts_updated_at ON task_efforts;

-- Drop all functions
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.handle_new_user_simple();
DROP FUNCTION IF EXISTS public.insert_default_data();
DROP FUNCTION IF EXISTS public.insert_default_data_manual();
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop all policies
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can update any profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can view all user groups" ON user_groups;
DROP POLICY IF EXISTS "Authenticated users can create user groups" ON user_groups;
DROP POLICY IF EXISTS "Creators and admins can update user groups" ON user_groups;
DROP POLICY IF EXISTS "Creators and admins can delete user groups" ON user_groups;
DROP POLICY IF EXISTS "Users can view all kanban columns" ON kanban_columns;
DROP POLICY IF EXISTS "Authenticated users can create kanban columns" ON kanban_columns;
DROP POLICY IF EXISTS "Creators and admins can update kanban columns" ON kanban_columns;
DROP POLICY IF EXISTS "Creators and admins can delete kanban columns" ON kanban_columns;

-- Drop all tables (in correct order due to foreign key constraints)
DROP TABLE IF EXISTS task_efforts;
DROP TABLE IF EXISTS user_capacities;
DROP TABLE IF EXISTS task_history;
DROP TABLE IF EXISTS task_comments;
DROP TABLE IF EXISTS tasks;
DROP TABLE IF EXISTS skillset_groups;
DROP TABLE IF EXISTS kanban_columns;
DROP TABLE IF EXISTS projects;
DROP TABLE IF EXISTS folders;
DROP TABLE IF EXISTS user_groups;
DROP TABLE IF EXISTS user_profiles;

-- Drop custom types
DROP TYPE IF EXISTS task_priority;
DROP TYPE IF EXISTS task_status;
DROP TYPE IF EXISTS user_role;

-- Success message
SELECT 'Cleanup completed successfully!' as message;
