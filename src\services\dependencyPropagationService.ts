import { supabase } from '../lib/supabase';
import { Task } from '../types';
import {
  DateCalculationResult,
  DatePropagationConfig,
  DependencyServiceResponse
} from '../types/dependencies';
import { dependencyService } from './dependencyService';
import { dateCalculationService } from './dateCalculationService';

// Service for handling automatic date propagation when dependencies change
export class DependencyPropagationService {
  
  private static isProcessing = false;
  private static processingQueue = new Set<string>();

  // Main entry point for handling task date changes
  static async handleTaskDateChange(
    taskId: string,
    oldStartDate?: string,
    oldDueDate?: string,
    newStartDate?: string,
    newDueDate?: string,
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<string[]>> {
    
    // Prevent recursive processing
    if (this.processingQueue.has(taskId)) {
      return { success: true, data: [] };
    }

    try {
      this.processingQueue.add(taskId);

      // Check if dates actually changed
      const startDateChanged = oldStartDate !== newStartDate;
      const dueDateChanged = oldDueDate !== newDueDate;

      if (!startDateChanged && !dueDateChanged) {
        return { success: true, data: [] };
      }

      console.log(`Processing date change for task ${taskId}:`, {
        oldStartDate,
        oldDueDate,
        newStartDate,
        newDueDate
      });

      // Get all tasks that depend on this task
      const dependentsResult = await dependencyService.getTaskDependents(taskId);
      if (!dependentsResult.success) {
        return { success: false, error: dependentsResult.error };
      }

      const dependents = dependentsResult.data || [];
      const affectedTaskIds: string[] = [];

      // Process each dependent task
      for (const dependency of dependents) {
        const result = await this.processTaskDependency(
          dependency.successorTaskId,
          config
        );

        if (result.success && result.data) {
          affectedTaskIds.push(...result.data);
        }
      }

      return { success: true, data: affectedTaskIds };
    } catch (error) {
      console.error('Failed to handle task date change:', error);
      return { success: false, error: 'Failed to propagate date changes' };
    } finally {
      this.processingQueue.delete(taskId);
    }
  }

  // Process a single task's dependency-based date calculation
  private static async processTaskDependency(
    taskId: string,
    config: Partial<DatePropagationConfig>
  ): Promise<DependencyServiceResponse<string[]>> {
    try {
      // Calculate new dates based on dependencies
      const calculationResult = await dateCalculationService.calculateTaskDates(taskId, config);
      
      if (!calculationResult.success || !calculationResult.data) {
        return { success: false, error: calculationResult.error };
      }

      const calculation = calculationResult.data;
      const affectedTaskIds: string[] = [];

      // Check if we need to update the task dates
      const shouldUpdateDates = this.shouldUpdateTaskDates(calculation, config);
      
      if (shouldUpdateDates) {
        // Get current task data
        const { data: currentTask, error: taskError } = await supabase
          .from('tasks')
          .select('start_date, due_date, title')
          .eq('id', taskId)
          .single();

        if (taskError || !currentTask) {
          return { success: false, error: 'Failed to fetch current task data' };
        }

        const oldStartDate = currentTask.start_date;
        const oldDueDate = currentTask.due_date;

        // Update the task dates
        const updateResult = await dateCalculationService.updateTaskDates(
          taskId,
          calculation.calculatedStartDate,
          calculation.calculatedDueDate
        );

        if (updateResult.success) {
          affectedTaskIds.push(taskId);

          console.log(`Updated dates for task "${currentTask.title}":`, {
            oldStartDate,
            oldDueDate,
            newStartDate: calculation.calculatedStartDate,
            newDueDate: calculation.calculatedDueDate
          });

          // Recursively propagate to tasks that depend on this one
          const propagationResult = await this.handleTaskDateChange(
            taskId,
            oldStartDate,
            oldDueDate,
            calculation.calculatedStartDate,
            calculation.calculatedDueDate,
            config
          );

          if (propagationResult.success && propagationResult.data) {
            affectedTaskIds.push(...propagationResult.data);
          }
        }
      }

      return { success: true, data: affectedTaskIds };
    } catch (error) {
      console.error('Failed to process task dependency:', error);
      return { success: false, error: 'Failed to process task dependency' };
    }
  }

  // Determine if task dates should be updated based on calculation results
  private static shouldUpdateTaskDates(
    calculation: DateCalculationResult,
    config: Partial<DatePropagationConfig>
  ): boolean {
    const respectManualDates = config.respectManualDates ?? true;

    // If we respect manual dates and there's a conflict, don't update
    if (respectManualDates && calculation.hasConflict) {
      console.log(`Skipping date update for task ${calculation.taskId} due to manual date conflict`);
      return false;
    }

    // Check if calculated dates are different from current dates
    const startDateChanged = calculation.originalStartDate !== calculation.calculatedStartDate;
    const dueDateChanged = calculation.originalDueDate !== calculation.calculatedDueDate;

    return startDateChanged || dueDateChanged;
  }

  // Handle dependency creation/deletion
  static async handleDependencyChange(
    predecessorTaskId: string,
    successorTaskId: string,
    changeType: 'created' | 'deleted' | 'updated',
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<string[]>> {
    try {
      console.log(`Handling dependency ${changeType}:`, {
        predecessorTaskId,
        successorTaskId
      });

      // For dependency changes, recalculate the successor task
      const result = await this.processTaskDependency(successorTaskId, config);
      
      return result;
    } catch (error) {
      console.error('Failed to handle dependency change:', error);
      return { success: false, error: 'Failed to handle dependency change' };
    }
  }

  // Batch process multiple task date changes
  static async batchProcessDateChanges(
    changes: Array<{
      taskId: string;
      oldStartDate?: string;
      oldDueDate?: string;
      newStartDate?: string;
      newDueDate?: string;
    }>,
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<string[]>> {
    try {
      const allAffectedTasks: string[] = [];
      
      // Process changes in order
      for (const change of changes) {
        const result = await this.handleTaskDateChange(
          change.taskId,
          change.oldStartDate,
          change.oldDueDate,
          change.newStartDate,
          change.newDueDate,
          config
        );

        if (result.success && result.data) {
          allAffectedTasks.push(...result.data);
        }
      }

      // Remove duplicates
      const uniqueAffectedTasks = [...new Set(allAffectedTasks)];
      
      return { success: true, data: uniqueAffectedTasks };
    } catch (error) {
      console.error('Failed to batch process date changes:', error);
      return { success: false, error: 'Failed to batch process date changes' };
    }
  }

  // Get propagation preview without actually updating dates
  static async getDatePropagationPreview(
    taskId: string,
    newStartDate?: string,
    newDueDate?: string,
    config: Partial<DatePropagationConfig> = {}
  ): Promise<DependencyServiceResponse<DateCalculationResult[]>> {
    try {
      // Temporarily update the task dates in memory for calculation
      const originalTask = await supabase
        .from('tasks')
        .select('start_date, due_date')
        .eq('id', taskId)
        .single();

      if (!originalTask.data) {
        return { success: false, error: 'Task not found' };
      }

      // Simulate the date change by temporarily updating the database
      await supabase
        .from('tasks')
        .update({
          start_date: newStartDate || originalTask.data.start_date,
          due_date: newDueDate || originalTask.data.due_date
        })
        .eq('id', taskId);

      // Calculate propagation effects
      const propagationResult = await dateCalculationService.propagateDateChanges(taskId, config);

      // Restore original dates
      await supabase
        .from('tasks')
        .update({
          start_date: originalTask.data.start_date,
          due_date: originalTask.data.due_date
        })
        .eq('id', taskId);

      return propagationResult;
    } catch (error) {
      console.error('Failed to get date propagation preview:', error);
      return { success: false, error: 'Failed to get propagation preview' };
    }
  }

  // Check if propagation is currently in progress
  static isProcessingPropagation(): boolean {
    return this.isProcessing || this.processingQueue.size > 0;
  }

  // Clear processing state (for error recovery)
  static clearProcessingState(): void {
    this.isProcessing = false;
    this.processingQueue.clear();
  }
}

export const dependencyPropagationService = DependencyPropagationService;
