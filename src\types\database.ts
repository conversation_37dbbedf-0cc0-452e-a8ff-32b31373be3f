export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          email: string
          name: string
          avatar_url: string | null
          role: 'admin' | 'user'
          group_id: string | null
          skillset_ids: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          avatar_url?: string | null
          role?: 'admin' | 'user'
          group_id?: string | null
          skillset_ids?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          avatar_url?: string | null
          role?: 'admin' | 'user'
          group_id?: string | null
          skillset_ids?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      user_groups: {
        Row: {
          id: string
          name: string
          color: string
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          color: string
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          color?: string
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string
          color: string
          start_date: string | null
          end_date: string | null
          folder_id: string | null
          effort: Json | null
          created_at: string
          updated_at: string
          created_by: string
          version: number
          updated_by: string | null
        }
        Insert: {
          id?: string
          name: string
          description: string
          color: string
          start_date?: string | null
          end_date?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by: string
          version?: number
          updated_by?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string
          color?: string
          start_date?: string | null
          end_date?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by?: string
          version?: number
          updated_by?: string | null
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string
          status: string
          priority: 'low' | 'medium' | 'high'
          assigned_user_id: string | null
          assigned_users: string[]
          assigned_groups: string[]
          owner_id: string | null
          due_date: string | null
          start_date: string | null
          tags: string[]
          project_id: string | null
          folder_id: string | null
          effort: Json | null
          subtasks: Json | null
          created_at: string
          updated_at: string
          created_by: string
          version: number
          updated_by: string | null
        }
        Insert: {
          id?: string
          title: string
          description: string
          status?: string
          priority?: 'low' | 'medium' | 'high'
          assigned_user_id?: string | null
          assigned_users?: string[]
          assigned_groups?: string[]
          owner_id?: string | null
          due_date?: string | null
          start_date?: string | null
          tags?: string[]
          project_id?: string | null
          folder_id?: string | null
          effort?: Json | null
          subtasks?: Json | null
          created_at?: string
          updated_at?: string
          created_by: string
          version?: number
          updated_by?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string
          status?: string
          priority?: 'low' | 'medium' | 'high'
          assigned_user_id?: string | null
          assigned_users?: string[]
          assigned_groups?: string[]
          owner_id?: string | null
          due_date?: string | null
          start_date?: string | null
          tags?: string[]
          project_id?: string | null
          folder_id?: string | null
          effort?: Json | null
          subtasks?: Json | null
          created_at?: string
          updated_at?: string
          created_by?: string
          version?: number
          updated_by?: string | null
        }
      }
      task_comments: {
        Row: {
          id: string
          task_id: string
          user_id: string
          content: string
          parent_id: string | null
          edited: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          task_id: string
          user_id: string
          content: string
          parent_id?: string | null
          edited?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          user_id?: string
          content?: string
          parent_id?: string | null
          edited?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      task_history: {
        Row: {
          id: string
          task_id: string
          user_id: string
          field: string
          old_value: string
          new_value: string
          created_at: string
        }
        Insert: {
          id?: string
          task_id: string
          user_id: string
          field: string
          old_value: string
          new_value: string
          created_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          user_id?: string
          field?: string
          old_value?: string
          new_value?: string
          created_at?: string
        }
      }
      task_dependencies: {
        Row: {
          id: string
          predecessor_task_id: string
          successor_task_id: string
          dependency_type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish'
          lag_days: number
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          predecessor_task_id: string
          successor_task_id: string
          dependency_type?: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish'
          lag_days?: number
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          predecessor_task_id?: string
          successor_task_id?: string
          dependency_type?: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish'
          lag_days?: number
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      folders: {
        Row: {
          id: string
          name: string
          parent_id: string | null
          created_at: string
          updated_at: string
          created_by: string
          version: number
          updated_by: string | null
        }
        Insert: {
          id?: string
          name: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
          created_by: string
          version?: number
          updated_by?: string | null
        }
        Update: {
          id?: string
          name?: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
          created_by?: string
          version?: number
          updated_by?: string | null
        }
      }
      kanban_columns: {
        Row: {
          id: string
          title: string
          color: string
          position: number
          created_at: string
          updated_at: string
          created_by: string
          version: number
          updated_by: string | null
        }
        Insert: {
          id?: string
          title: string
          color: string
          position?: number
          created_at?: string
          updated_at?: string
          created_by: string
          version?: number
          updated_by?: string | null
        }
        Update: {
          id?: string
          title?: string
          color?: string
          position?: number
          created_at?: string
          updated_at?: string
          created_by?: string
          version?: number
          updated_by?: string | null
        }
      }
      skillset_groups: {
        Row: {
          id: string
          name: string
          description: string | null
          color: string
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          color: string
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          color?: string
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      user_capacities: {
        Row: {
          id: string
          user_id: string
          daily_hours: number
          weekly_hours: number
          working_days: number[]
          effective_from: string
          effective_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          daily_hours: number
          weekly_hours: number
          working_days: number[]
          effective_from: string
          effective_to?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          daily_hours?: number
          weekly_hours?: number
          working_days?: number[]
          effective_from?: string
          effective_to?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      task_efforts: {
        Row: {
          id: string
          task_id: string
          estimated_hours: number
          actual_hours: number | null
          assigned_user_id: string | null
          required_skillsets: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          task_id: string
          estimated_hours: number
          actual_hours?: number | null
          assigned_user_id?: string | null
          required_skillsets?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          estimated_hours?: number
          actual_hours?: number | null
          assigned_user_id?: string | null
          required_skillsets?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          recipient_id: string
          sender_id: string
          type: string
          title: string
          content: string
          task_id: string | null
          comment_id: string | null
          is_read: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          recipient_id: string
          sender_id: string
          type?: string
          title: string
          content: string
          task_id?: string | null
          comment_id?: string | null
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          recipient_id?: string
          sender_id?: string
          type?: string
          title?: string
          content?: string
          task_id?: string | null
          comment_id?: string | null
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      task_durations: {
        Row: {
          id: string
          task_id: string
          status: string
          start_time: string
          end_time: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          task_id: string
          status: string
          start_time: string
          end_time?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          status?: string
          start_time?: string
          end_time?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      automation_workflows: {
        Row: {
          id: string
          name: string
          description: string | null
          is_active: boolean
          is_template: boolean
          created_by: string
          project_id: string | null
          folder_id: string | null
          trigger_config: Json
          condition_config: Json | null
          action_config: Json
          execution_count: number
          last_executed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          is_active?: boolean
          is_template?: boolean
          created_by: string
          project_id?: string | null
          folder_id?: string | null
          trigger_config: Json
          condition_config?: Json | null
          action_config: Json
          execution_count?: number
          last_executed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          is_active?: boolean
          is_template?: boolean
          created_by?: string
          project_id?: string | null
          folder_id?: string | null
          trigger_config?: Json
          condition_config?: Json | null
          action_config?: Json
          execution_count?: number
          last_executed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      automation_executions: {
        Row: {
          id: string
          workflow_id: string
          trigger_data: Json
          condition_result: Json | null
          action_results: Json
          status: string
          error_message: string | null
          execution_time_ms: number | null
          executed_at: string
        }
        Insert: {
          id?: string
          workflow_id: string
          trigger_data: Json
          condition_result?: Json | null
          action_results: Json
          status?: string
          error_message?: string | null
          execution_time_ms?: number | null
          executed_at?: string
        }
        Update: {
          id?: string
          workflow_id?: string
          trigger_data?: Json
          condition_result?: Json | null
          action_results?: Json
          status?: string
          error_message?: string | null
          execution_time_ms?: number | null
          executed_at?: string
        }
      }
      automation_triggers: {
        Row: {
          id: string
          workflow_id: string
          trigger_type: string
          trigger_config: Json
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          workflow_id: string
          trigger_type: string
          trigger_config: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          workflow_id?: string
          trigger_type?: string
          trigger_config?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'user'
      task_status: 'todo' | 'in-progress' | 'review' | 'done'
      task_priority: 'low' | 'medium' | 'high'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
