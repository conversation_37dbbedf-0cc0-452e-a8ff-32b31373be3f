import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Task } from '../types';

interface ClickableStatusBadgeProps {
  status: Task['status'];
  columns?: any[];
  onChange: (status: Task['status']) => void;
}

export default function ClickableStatusBadge({ status, columns, onChange }: ClickableStatusBadgeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Default colors for standard statuses
  const defaultColors: { [key: string]: string } = {
    'todo': 'bg-gray-100 text-gray-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    'review': 'bg-yellow-100 text-yellow-800',
    'done': 'bg-green-100 text-green-800'
  };

  // Get current status display info
  const getCurrentStatusInfo = () => {
    let colorClass = defaultColors[status];
    let statusLabel = status.replace('-', ' ');

    if (columns) {
      const column = columns.find(col => col.id === status);
      if (column) {
        colorClass = column.color || colorClass;
        statusLabel = column.title || statusLabel;
      }
    }

    // Fallback for custom statuses
    if (!colorClass) {
      colorClass = 'bg-purple-100 text-purple-800';
    }

    return { colorClass, statusLabel };
  };

  // Get all available status options
  const getStatusOptions = () => {
    if (columns && columns.length > 0) {
      return columns.map(col => ({
        id: col.id,
        label: col.title,
        color: col.color || defaultColors[col.id] || 'bg-purple-100 text-purple-800'
      }));
    }

    // Fallback to default options
    return [
      { id: 'todo', label: 'To Do', color: 'bg-gray-100 text-gray-800' },
      { id: 'in-progress', label: 'In Progress', color: 'bg-blue-100 text-blue-800' },
      { id: 'review', label: 'Review', color: 'bg-yellow-100 text-yellow-800' },
      { id: 'done', label: 'Done', color: 'bg-green-100 text-green-800' }
    ];
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const { colorClass, statusLabel } = getCurrentStatusInfo();
  const statusOptions = getStatusOptions();

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium transition-all hover:opacity-80 ${colorClass}`}
      >
        {statusLabel}
        <ChevronDown className="w-3 h-3" />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[120px]">
          {statusOptions.map((option) => (
            <button
              key={option.id}
              type="button"
              onClick={() => {
                onChange(option.id as Task['status']);
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 text-xs font-medium hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                option.id === status ? 'bg-gray-50' : ''
              }`}
            >
              <span className={`inline-flex items-center px-2 py-1 rounded-full ${option.color}`}>
                {option.label}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
